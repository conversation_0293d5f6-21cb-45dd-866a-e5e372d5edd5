package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.xpautotest.accessibility.AccessibilityHelper;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.xpautotest.community.test.TestResult;

public class BaseAction implements IAction<AccessibilityConfig> {
    protected AccessibilityHelper service;

    @Override
    public void init(AccessibilityConfig config) {
        this.service = config.getService();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        return TestResult.failure("BaseAction execute not implemented");
    }
}
