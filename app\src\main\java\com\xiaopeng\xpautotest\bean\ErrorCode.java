package com.xiaopeng.xpautotest.bean;

import com.xiaopeng.xpautotest.R;

// ErrorCode.java
public enum ErrorCode {
    ERROR_UNKNOWN(1000, R.string.error_unknown),
    // 网络类错误
    ERROR_NETWORK_NO_CONNECTION(1001, R.string.error_network_no_connection),
    ERROR_NETWORK_NO_WIFI(1002, R.string.error_network_no_wifi),
    ERROR_NETWORK_TIMEOUT(1003, R.string.error_network_timeout),
    ERROR_NETWORK_GENERIC(1004, R.string.error_network_generic),

    // 服务端错误
    ERROR_API_SERVER_ERROR(2000, R.string.error_api_server_error),
    ERROR_API_SERVER_UNKNOWN(2001, R.string.error_api_server_unknown),
    ERROR_API_NOT_FOUND(2002, R.string.error_api_not_found),
    ERROR_API_EMPTY_PARAM(2003, R.string.error_api_empty_param),
    ERROR_API_REGISTER_FAIL(2010, R.string.error_api_register_fail),
    ERROR_API_REGISTER_NO_TASK(2011, R.string.error_api_register_no_task),
    ERROR_API_DOWNLOAD_SCRIPT(2012, R.string.error_api_download_script),
    ERROR_API_DOWNLOAD_SCRIPT_WRITE(2013, R.string.error_api_download_script_write),
    ERROR_API_EXECUTION_STOP(2014, R.string.error_api_execution_stop),
    ERROR_API_LOGIN_FAIL(2041, R.string.error_auth_token_expired),

    // 本地操作错误
    ERROR_LOAD_SCRIPT(3001, R.string.error_load_script),
    ERROR_FILE_WRITE(3002, R.string.error_api_download_script_write),

    // 认证错误
    ERROR_AUTH_TOKEN_EXPIRED(4001, R.string.error_auth_token_expired);

    private final int code;
    private final int messageRes;

    ErrorCode(int code, int messageRes) {
        this.code = code;
        this.messageRes = messageRes;
    }

    public int getCode() { return code; }
    public int getMessageRes() { return messageRes; }

    public String toString() {
        return "ErrorCode{" +
                "code=" + code +
                ", messageRes=" + messageRes +
                '}';
    }
}
