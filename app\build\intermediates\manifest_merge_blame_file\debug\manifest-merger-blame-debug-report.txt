1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.xiaopeng.xpautotest"
4    android:sharedUserId="android.uid.system"
5    android:sharedUserMaxSdkVersion="32"
6    android:versionCode="9999"
7    android:versionName="1.0.0-SNAPSHOT" >
8
9    <uses-sdk
10        android:minSdkVersion="28"
11        android:targetSdkVersion="30" />
12
13    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
13-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:5-75
13-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
14-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:5-74
14-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:8:22-71
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:5-76
15-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:10:22-73
16    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
16-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:5-76
16-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:11:22-73
17    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
17-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:5-79
17-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.INTERNET" />
18-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:5-67
18-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:13:22-64
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:5-79
19-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:14:22-76
20    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
20-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:5-75
20-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:15:22-72
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:5-81
21-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:16:22-78
22    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
22-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:5-81
22-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:17:22-78
23    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
23-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:5-82
23-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:18:22-79
24    <uses-permission android:name="android.permission.REBOOT" />
24-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:5-65
24-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:19:22-62
25    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
25-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:5-78
25-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:20:22-75
26    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
26-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:5-80
26-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:21:22-77
27    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
27-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:5-84
27-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:22:22-81
28    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
28-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:5-88
28-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:23:22-85
29    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
29-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:5-82
29-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:24:22-79
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:5-68
30-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:25:22-65
31    <uses-permission android:name="android.permission.DEVICE_POWER" />
31-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:5-71
31-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:26:22-68
32    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
32-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:5-80
32-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:27:22-77
33    <uses-permission android:name="android.permission.READ_LOGS" />
33-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:5-68
33-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:28:22-65
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:5-78
34-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:29:22-75
35    <uses-permission android:name="android.permission.RECOVERY" />
35-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:5-67
35-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:30:22-64
36    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
36-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:5-80
36-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:31:22-77
37    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
37-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:5-84
37-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:32:22-81
38    <uses-permission android:name="android.permission.SUPER_APPLICATION_RUNNING" />
38-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:5-84
38-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:33:22-81
39    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION" />
39-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:5-83
39-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:34:22-80
40    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
40-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:5-85
40-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:35:22-82
41    <uses-permission android:name="android.permission.MEDIA_PROJECTION" />
41-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:5-75
41-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:36:22-72
42    <uses-permission android:name="com.xiaopeng.permission.OTA_SERVICE" />
42-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:5-75
42-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:37:22-72
43    <uses-permission android:name="com.xiaopeng.permission.CAR_SERVICE" />
43-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:5-75
43-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:38:22-72
44    <uses-permission android:name="com.xiaopeng.permission.ACTIVITY" />
44-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:5-72
44-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:39:22-69
45    <uses-permission android:name="com.xiaopeng.permission.SERVICE" />
45-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:5-71
45-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:40:22-68
46    <uses-permission android:name="com.xiaopeng.permission.BROADCAST" />
46-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:5-73
46-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:41:22-70
47    <uses-permission android:name="xiaopeng.permission.DATA_SERVICE" />
47-->[com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:5-72
47-->[com.xiaopeng.lib:lib_bughunter:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\06c4a5defa88c79ac0600c91b0a800cd\transformed\jetified-lib_bughunter-2.3.5\AndroidManifest.xml:9:22-69
48    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
48-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:5-81
48-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:24:22-78
49    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
49-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:5-79
49-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:25:22-76
50    <uses-permission android:name="com.xiaopeng.permission.DATA_SERVICE" />
50-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:5-76
50-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:26:22-73
51    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
51-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:5-85
51-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:27:22-82
52    <uses-permission android:name="android.permission.GET_TASKS" />
52-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:5-68
52-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:29:22-65
53    <uses-permission android:name="android.permission.CALL_PHONE" />
53-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:5-69
53-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:19:22-66
54    <uses-permission android:name="android.permission.READ_SMS" />
54-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:5-67
54-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:20:22-64
55    <uses-permission android:name="com.xiaopeng.permission.SYSTEM_DELEGATE" />
55-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:5-79
55-->[com.xiaopeng.lib:lib_http:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\218da006680170b6d5665821eb508a43\transformed\jetified-lib_http-1.5.4\AndroidManifest.xml:21:22-76
56    <uses-permission android:name="android.permission.REORDER_TASKS" />
56-->[com.xiaopeng.lib:lib_utils:1.7.5.9] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-1.7.5.9\AndroidManifest.xml:8:5-72
56-->[com.xiaopeng.lib:lib_utils:1.7.5.9] C:\Users\<USER>\.gradle\caches\transforms-3\345f54dae22b51e68d9ad4e9d6bbe49f\transformed\jetified-lib_utils-1.7.5.9\AndroidManifest.xml:8:22-69
57    <uses-permission android:name="android.permission.XIAOPENG_APP" />
57-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:11:5-71
57-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:11:22-68
58
59    <permission
59-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
60        android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
64
65    <application
65-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:42:5-107:19
66        android:name="com.xiaopeng.xpautotest.App"
66-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:43:9-51
67        android:allowBackup="false"
67-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:32:9-36
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\58c60d7f1f34ad6e3d961d9b76173355\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
69        android:configChanges="keyboard|keyboardHidden|navigation"
69-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:44:9-67
70        android:debuggable="true"
71        android:extractNativeLibs="true"
71-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:45:9-41
72        android:icon="@mipmap/ic_launcher"
72-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:47:9-43
73        android:label="@string/app_name"
73-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:48:9-41
74        android:networkSecurityConfig="@xml/network_security_config"
74-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:46:9-69
75        android:supportsRtl="true"
75-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:49:9-35
76        android:testOnly="true"
77        android:theme="@style/autotest_AppTheme" >
77-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:50:9-49
78        <meta-data
78-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:52:9-54:109
79            android:name="com.xiaopeng.lib.lib_feature_modules"
79-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:53:13-64
80            android:value="com.xiaopeng.carcontrol,com.xiaopeng.caraccount,com.xiaopeng.carspeechservice" />
80-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:54:13-106
81
82        <activity
82-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:55:9-66:20
83            android:name="com.xiaopeng.xpautotest.ui.MainActivity"
83-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:56:13-67
84            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
84-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:57:13-204
85            android:exported="true"
85-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:58:13-36
86            android:launchMode="singleInstance"
86-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:59:13-48
87            android:theme="@style/autotest_AppTheme" >
87-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:60:13-53
88            <intent-filter>
88-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:62:13-65:29
89                <action android:name="android.intent.action.MAIN" />
89-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:63:17-69
89-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:63:25-66
90
91                <category android:name="android.intent.category.LAUNCHER" />
91-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:64:17-77
91-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:64:27-74
92            </intent-filter>
93        </activity>
94
95        <service
95-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:68:9-72:62
96            android:name="com.xiaopeng.xpautotest.service.TestExecutionService"
96-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:69:13-80
97            android:enabled="true"
97-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:70:13-35
98            android:exported="false"
98-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:71:13-37
99            android:foregroundServiceType="connectedDevice" />
99-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:72:13-60
100        <service
100-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:74:9-78:62
101            android:name="com.xiaopeng.xpautotest.service.DebuggingModeService"
101-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:75:13-80
102            android:enabled="true"
102-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:76:13-35
103            android:exported="false"
103-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:77:13-37
104            android:foregroundServiceType="connectedDevice" />
104-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:78:13-60
105        <service
105-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:80:9-91:19
106            android:name="com.xiaopeng.xpautotest.accessibility.AutoTestAccessibilityService"
106-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:81:13-71
107            android:enabled="true"
107-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:83:13-35
108            android:exported="true"
108-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:84:13-36
109            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
109-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:82:13-79
110            <intent-filter>
110-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:85:13-87:29
111                <action android:name="android.accessibilityservice.AccessibilityService" />
111-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:86:17-92
111-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:86:25-89
112            </intent-filter>
113
114            <meta-data
114-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:88:13-90:58
115                android:name="android.accessibilityservice"
115-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:89:17-60
116                android:resource="@xml/service_config" />
116-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:90:17-55
117        </service>
118
119        <receiver
119-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:93:9-99:20
120            android:name="com.xiaopeng.xpautotest.receiver.StartBroadcastReceiver"
120-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:93:19-89
121            android:enabled="true"
121-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:94:13-35
122            android:exported="true" >
122-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:95:13-36
123            <intent-filter>
123-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:96:13-98:29
124                <action android:name="com.xiaoppeng.xpautotest.OPEN_APP_ACTION" />
124-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:97:17-83
124-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:97:25-80
125            </intent-filter>
126        </receiver>
127
128        <service
128-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:100:9-106:19
129            android:name="com.xiaopeng.xpautotest.service.OSSUploadService"
129-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:101:13-76
130            android:exported="true" >
130-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:102:13-36
131            <intent-filter>
131-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:103:13-105:29
132                <action android:name="com.xiaopeng.xpautotest.action.START_OSS_UPLOAD_SERVICE" />
132-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:104:17-98
132-->E:\WorkSpace\code\xpAutoTest\app\src\main\AndroidManifest.xml:104:25-95
133            </intent-filter>
134        </service>
135        <service
135-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-13:19
136            android:name="com.xiaopeng.xpautotest.trace.service.CanDataCollectService"
136-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-87
137            android:enabled="true"
137-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
138            android:exported="false"
138-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
139            android:foregroundServiceType="connectedDevice" >
139-->[:trace] E:\WorkSpace\code\xpAutoTest\trace\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-60
140        </service>
141        <service android:name="org.eclipse.paho.android.service.MqttService" />
141-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:9-80
141-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:34:18-77
142
143        <provider
143-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:36:9-40:20
144            android:name="com.xiaopeng.lib.framework.netchannelmodule.common.ContextNetStatusProvider"
144-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:37:13-103
145            android:authorities="com.xiaopeng.xpautotest.netmodule.provider"
145-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:38:13-70
146            android:exported="false" >
146-->[com.xiaopeng.lib.framework:netchannelmodule:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3d6f56cf45d51d7f8630e7de618a9b24\transformed\jetified-netchannelmodule-*******\AndroidManifest.xml:39:13-37
147        </provider>
148        <provider
148-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:11:9-15:34
149            android:name="com.xiaopeng.lib.apirouter.server.ApiPublisherProvider"
149-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:12:13-82
150            android:authorities="com.xiaopeng.xpautotest.api.publisher"
150-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:13:13-65
151            android:exported="true" />
151-->[com.xiaopeng.lib:apirouterclient:********-napa] C:\Users\<USER>\.gradle\caches\transforms-3\64fe9e1ae14ec86b1ff457627b3ea16e\transformed\jetified-apirouterclient-********-napa\AndroidManifest.xml:14:13-36
152        <provider
152-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:14:9-19:34
153            android:name="com.xiaopeng.lib.feature.XpFeatureProvider"
153-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:15:13-70
154            android:authorities="com.xiaopeng.xpautotest.XpFeatureProvider"
154-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:16:13-69
155            android:exported="false"
155-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:17:13-37
156            android:initOrder="1100" />
156-->[com.xiaopeng.lib:lib_feature:*******] C:\Users\<USER>\.gradle\caches\transforms-3\3076e586b7f124b2620ca4c76e411e06\transformed\jetified-lib_feature-*******\AndroidManifest.xml:18:13-37
157        <provider
157-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
158            android:name="androidx.startup.InitializationProvider"
158-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
159            android:authorities="com.xiaopeng.xpautotest.androidx-startup"
159-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
160            android:exported="false" >
160-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
161            <meta-data
161-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
162                android:name="androidx.emoji2.text.EmojiCompatInitializer"
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
163                android:value="androidx.startup" />
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3d1cc5acdece0c7924f4dd7ea1c1b13\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
164            <meta-data
164-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
165                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
165-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
166                android:value="androidx.startup" />
166-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\242929c79ad26c0bf9e543be08f4862a\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
167            <meta-data
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
168                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
169                android:value="androidx.startup" />
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
170        </provider>
171
172        <receiver
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
173            android:name="androidx.profileinstaller.ProfileInstallReceiver"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
174            android:directBootAware="false"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
175            android:enabled="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
176            android:exported="true"
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
177            android:permission="android.permission.DUMP" >
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
178            <intent-filter>
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
179                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
180            </intent-filter>
181            <intent-filter>
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
182                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
183            </intent-filter>
184            <intent-filter>
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
185                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
186            </intent-filter>
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
188                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e34b0fb71f791f4e3deb5417c19a8f31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
189            </intent-filter>
190        </receiver>
191    </application>
192
193</manifest>
