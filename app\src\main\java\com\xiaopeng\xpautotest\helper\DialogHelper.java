package com.xiaopeng.xpautotest.helper;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;

import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.dialog.SystemDialog;
import com.xiaopeng.xui.app.XDialog;
import com.xiaopeng.xui.app.XDialogInterface;
import com.xiaopeng.xui.app.XDialogSystemType;
import com.xiaopeng.xui.app.XLoadingDialog;
import com.xiaopeng.xui.widget.XTextView;

import java.util.HashMap;
import java.util.Map;

public class DialogHelper {

    private static final String TAG = "DialogHelper";

    // same as dimens/script_data_dialog_width
    private static final int SCRIPT_DATA_DIALOG_WIDTH = 1900;
    // 系统弹窗标题限制15个字符
    private static final int SYSTEM_DIALOG_TITLE_LIMITED = 15;

    private Context mContext;
    private XDialog mDialog;
    private XLoadingDialog mLoadingDialog;

    private final Map<String, Integer> mCallbackInvokeMap = new HashMap<>();

    private static class DialogHelperHolder {
        static final DialogHelper INSTANCE = new DialogHelper();
    }

    public static DialogHelper getInstance() {
        return DialogHelperHolder.INSTANCE;
    }

    private DialogHelper() {
    }

    public void setContext(Context context) {
        mContext = context;
    }

    public XLoadingDialog showLoading(Context context, String message) {
        dismissAllDialog();

        this.mLoadingDialog = XLoadingDialog.show(context, message);
        this.mLoadingDialog.setCancelable(false);

        return this.mLoadingDialog;
    }

    public XDialog showCustomLoading(Context context, String title, String message) {
        dismissAllDialog();

        this.mDialog = new SystemDialog(context);

        this.mDialog.setCancelable(false);
        this.mDialog.setCloseVisibility(false);

        View view = LayoutInflater.from(context).inflate(R.layout.at_layout_dialog_loading, null);
        XTextView contentTextView = view.findViewById(R.id.textview);

        this.mDialog.setCustomView(view);

        if (!TextUtils.isEmpty(title)) {
            this.mDialog.setTitle(title);
        }

        if (!TextUtils.isEmpty(message)) {
            contentTextView.setText(message);
        }

        this.mDialog.show();

        return this.mDialog;
    }

    public XLoadingDialog showLoadingAndDismissTimeout(View view,
                                                       String message,
                                                       long dismissTimeout) {
        if (view == null) {
            return null;
        }

        dismissAllDialog();

        this.mLoadingDialog = XLoadingDialog.show(view.getContext(), message);
        view.postDelayed(() -> {
            this.mLoadingDialog.dismiss();
        }, dismissTimeout);

        return this.mLoadingDialog;
    }

    public XDialog showCustomizeLoading(Context context, String message) {
        return showCustomizeLoading(context, message, false);
    }

    public XDialog showCustomizeLoading(Context context, String message, boolean closeVisibility) {
        dismissAllDialog();

        return createCustomizeLoadingDialog(context, message, closeVisibility);
    }

    private XDialog createCustomizeLoadingDialog(Context context, String message,
                                                 boolean closeVisibility) {
        View view = LayoutInflater.from(context).inflate(R.layout.at_fragment_loading_dialog, null);
        XTextView messageTextView = view.findViewById(R.id.tv_loading_message);
        messageTextView.setText(message);

        this.mDialog = new XDialog(context).
                setCloseVisibility(closeVisibility).
                setTitleBarVisibility(false).
                setCustomView(view, false).
                setSystemDialog(XDialogSystemType.TYPE_SYSTEM_DIALOG);

        this.mDialog.setCancelable(false);
        this.mDialog.show();

        return this.mDialog;
    }

    public XDialog showDialog(Context context, String message) {
        dismissAllDialog();

        this.mDialog = new SystemDialog(context);
        this.mDialog.setTitle(R.string.factory_loading_error_title);
        this.mDialog.setMessage(message);
        this.mDialog.setPositiveButton(R.string.factory_loading_error_button);
        this.mDialog.setCancelable(false);
        this.mDialog.show();

        return this.mDialog;
    }
    public XDialog showDialog(Context context, int title, String message) {
        dismissAllDialog();
        this.mDialog = new SystemDialog(context);
        this.mDialog.setTitle(title);
        this.mDialog.setMessage(message);
        this.mDialog.setCloseVisibility(true);
        this.mDialog.show();
        return this.mDialog;
    }

    public XDialog showDialog(Context context, String title, String message,
                              String positiveButtonText, String negativeButtonText) {
        return showDialog(context, title, message, positiveButtonText, negativeButtonText, null);
    }

    public XDialog showDialog(Context context, String title, String message,
                              String positiveButtonText, String negativeButtonText,
                              XDialogInterface.OnClickListener listener) {
        return showDialog(context, title, message, positiveButtonText, negativeButtonText, false, listener);
    }

    public XDialog showDialog(Context context, String title, String message,
                              String positiveButtonText, String negativeButtonText,
                              boolean closeVisibility,
                              XDialogInterface.OnClickListener listener) {
        dismissAllDialog();

        this.mDialog = new SystemDialog(context);
        this.mDialog.setTitle(title);
        this.mDialog.setMessage(message);
        this.mDialog.setPositiveButton(positiveButtonText);
        this.mDialog.setNegativeButton(negativeButtonText);

        this.mDialog.setCancelable(false);
        this.mDialog.setPositiveButtonListener(listener);
        this.mDialog.setNegativeButtonListener(listener);

        this.mDialog.setCloseVisibility(closeVisibility);
        if (closeVisibility) {
            this.addDefaultCloseButton(this.mDialog);
        }

        this.mDialog.show();

        return this.mDialog;
    }

    private void addDefaultCloseButton(XDialog dialog) {
        if (dialog == null) {
            return;
        }

        if (!dialog.isPositiveButtonShowing() && !dialog.isNegativeButtonShowing()) {
            dialog.setPositiveButton(R.string.dialog_positive_button);
        }
    }

    private void dismissAllDialog() {
        Log.d(TAG, "dismiss all");

        if (this.mDialog != null) {
            this.mDialog.dismiss();
        }

        if (this.mLoadingDialog != null) {
            this.mLoadingDialog.dismiss();
        }
    }

    public void dismissLoading() {
        if (this.mLoadingDialog != null) {
            this.mLoadingDialog.dismiss();
        }
    }

    public void dismiss() {
        dismissAllDialog();
    }

    public interface InputTextListener {
        boolean onInputComplete(XDialog xDialog, String inputText);
    }
}
