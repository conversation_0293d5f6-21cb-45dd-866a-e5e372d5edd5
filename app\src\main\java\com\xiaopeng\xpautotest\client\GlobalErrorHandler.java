package com.xiaopeng.xpautotest.client;

import com.xiaopeng.xpautotest.client.api.ApiException;

import java.io.IOException;

// 废弃
public class GlobalErrorHandler {
    public static void handleError(Throwable t) {
        if (t instanceof ApiException) {
            ApiException e = (ApiException) t;
            switch (e.getCode()) {
                case 401:
                    // 处理未授权
                    break;
                case 404:
                    // 处理资源未找到
                    break;
                default:
                    // 处理其他错误
            }
        } else if (t instanceof IOException) {
            // 处理网络错误
        } else {
            // 处理未知错误
        }
    }
}
