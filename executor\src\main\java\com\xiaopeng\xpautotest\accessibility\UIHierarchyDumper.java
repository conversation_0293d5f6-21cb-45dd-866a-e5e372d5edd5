package com.xiaopeng.xpautotest.accessibility;

import android.graphics.Rect;
import android.os.Build;
import android.util.Log;
import android.util.Xml;
import android.view.Display;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityWindowInfo;

import org.xmlpull.v1.XmlSerializer;
import java.io.IOException;
import java.io.OutputStream;
import androidx.annotation.RequiresApi;

import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

public class UIHierarchyDumper {

    private static final String TAG = "UIHierarchyDumper";
    private static final String[] NAF_EXCLUDED_CLASSES = new String[] {
            android.widget.GridView.class.getName(), android.widget.GridLayout.class.getName(),
            android.widget.ListView.class.getName(), android.widget.TableLayout.class.getName()
    };

    private static final Map<Integer, String> ACTION_ID_TO_NAME_MAP = new HashMap<>();
    static {
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_FOCUS, "ACTION_FOCUS");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_CLEAR_FOCUS, "ACTION_CLEAR_FOCUS");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_SELECT, "ACTION_SELECT");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_CLEAR_SELECTION, "ACTION_CLEAR_SELECTION");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_CLICK, "ACTION_CLICK");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_LONG_CLICK, "ACTION_LONG_CLICK");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_ACCESSIBILITY_FOCUS, "ACTION_ACCESSIBILITY_FOCUS");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_CLEAR_ACCESSIBILITY_FOCUS, "ACTION_CLEAR_ACCESSIBILITY_FOCUS");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_NEXT_AT_MOVEMENT_GRANULARITY, "ACTION_NEXT_AT_MOVEMENT_GRANULARITY");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY, "ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_NEXT_HTML_ELEMENT, "ACTION_NEXT_HTML_ELEMENT");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_PREVIOUS_HTML_ELEMENT, "ACTION_PREVIOUS_HTML_ELEMENT");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD, "ACTION_SCROLL_FORWARD");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD, "ACTION_SCROLL_BACKWARD");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_COPY, "ACTION_COPY");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_PASTE, "ACTION_PASTE");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_CUT, "ACTION_CUT");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_SET_SELECTION, "ACTION_SET_SELECTION");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_EXPAND, "ACTION_EXPAND");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_COLLAPSE, "ACTION_COLLAPSE");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_DISMISS, "ACTION_DISMISS");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.ACTION_SET_TEXT, "ACTION_SET_TEXT");
        // API 23+
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_CONTEXT_CLICK.getId(), "ACTION_CONTEXT_CLICK");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_TO_POSITION.getId(), "ACTION_SCROLL_TO_POSITION");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SHOW_ON_SCREEN.getId(), "ACTION_SHOW_ON_SCREEN");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_UP.getId(), "ACTION_SCROLL_UP");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_DOWN.getId(), "ACTION_SCROLL_DOWN");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_LEFT.getId(), "ACTION_SCROLL_LEFT");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SCROLL_RIGHT.getId(), "ACTION_SCROLL_RIGHT");
        // API 24+
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SET_PROGRESS.getId(), "ACTION_SET_PROGRESS");
        // API 28+
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_SHOW_TOOLTIP.getId(), "ACTION_SHOW_TOOLTIP");
        ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_HIDE_TOOLTIP.getId(), "ACTION_HIDE_TOOLTIP");
        // API 30+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_IME_ENTER.getId(), "ACTION_IME_ENTER");
            ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_MOVE_WINDOW.getId(), "ACTION_MOVE_WINDOW"); 
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) { // API 31+
             ACTION_ID_TO_NAME_MAP.put(AccessibilityNodeInfo.AccessibilityAction.ACTION_PRESS_AND_HOLD.getId(), "ACTION_PRESS_AND_HOLD");
        }
    }

    private final int mDisplayWidth;
    private final int mDisplayHeight;

    public UIHierarchyDumper(int displayWidth, int displayHeight) {
        this.mDisplayWidth = displayWidth;
        this.mDisplayHeight = displayHeight;
    }

    public void dumpTreeNodesToSerializer(AccessibilityNodeInfo rootNodeToDump, XmlSerializer serializer, int maxDepth, int nodeIndexForThisTree) throws IOException {
        if (rootNodeToDump == null) {
            FileLogger.w(TAG, "rootNodeToDump is null.");
            return;
        }
        dumpNodeRecursive(rootNodeToDump, serializer, 0, maxDepth);
    }

    private String getActionName(int actionId) {
        String name = ACTION_ID_TO_NAME_MAP.get(actionId);
        return name != null ? name : String.valueOf(actionId); // Fallback to numeric ID if not in map
    }

    private void dumpNodeRecursive(AccessibilityNodeInfo node, XmlSerializer serializer,
                                   int index, int currentRemainingDepth) throws IOException {
        if (node == null) {
            return;
        }
        serializer.startTag("", "node");
        if (!nafExcludedClass(node) && !nafCheck(node)) {
            serializer.attribute("", "NAF", Boolean.toString(true));
        }
        serializer.attribute(null, "index", Integer.toString(index));
        try {
            serializer.attribute(null, "text", safeCharSeqToString(node.getText()));
            serializer.attribute(null, "resource-id", safeCharSeqToString(node.getViewIdResourceName()));
            serializer.attribute(null, "class", safeCharSeqToString(node.getClassName()));
            serializer.attribute(null, "package", safeCharSeqToString(node.getPackageName()));
            serializer.attribute(null, "content-desc", safeCharSeqToString(node.getContentDescription()));
        } catch (IllegalArgumentException e) {
            FileLogger.e(TAG, "IllegalArgumentException while getting node attributes: " + e.getMessage());
            e.printStackTrace();
        }
        
        serializer.attribute(null, "checkable", String.valueOf(node.isCheckable()));
        serializer.attribute(null, "checked", String.valueOf(node.isChecked()));
        serializer.attribute(null, "clickable", String.valueOf(node.isClickable()));
        serializer.attribute(null, "enabled", String.valueOf(node.isEnabled()));
        serializer.attribute(null, "focusable", String.valueOf(node.isFocusable()));
        serializer.attribute(null, "focused", String.valueOf(node.isFocused()));
        serializer.attribute(null, "scrollable", String.valueOf(node.isScrollable()));
        serializer.attribute(null, "long-clickable", String.valueOf(node.isLongClickable()));
        serializer.attribute(null, "password", String.valueOf(node.isPassword()));
        serializer.attribute(null, "selected", String.valueOf(node.isSelected()));
        serializer.attribute(null, "visible-to-user", String.valueOf(node.isVisibleToUser()));

        List<AccessibilityNodeInfo.AccessibilityAction> actionList = node.getActionList();
        if (actionList != null && !actionList.isEmpty()) {
            StringBuilder actionsStr = new StringBuilder();
            actionsStr.append("[");
            for (int i = 0; i < actionList.size(); i++) {
                AccessibilityNodeInfo.AccessibilityAction accessibilityAction = actionList.get(i);
                if (accessibilityAction != null) {
                    actionsStr.append(getActionName(accessibilityAction.getId()));
                    CharSequence label = accessibilityAction.getLabel();
                    if (label != null && label.length() > 0) {
                        String sanitizedLabel = label.toString().replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;").replace("\"", "&quot;").replace("'", "&apos;");
                        actionsStr.append(":").append(sanitizedLabel);
                    }
                }
                if (i < actionList.size() - 1) {
                    actionsStr.append(", ");
                }
            }
            actionsStr.append("]");
            serializer.attribute(null, "actions", actionsStr.toString());
        } else {
            serializer.attribute(null, "actions", "[]");
        }
        
        Rect bounds = getVisibleBoundsInScreen(node, this.mDisplayWidth, this.mDisplayHeight, false);
        serializer.attribute(null, "bounds", bounds != null ? bounds.toShortString() : "");

        serializer.attribute("", "drawing-order", Integer.toString(Api24Impl.getDrawingOrder(node)));
        serializer.attribute("", "hint", safeCharSeqToString(Api26Impl.getHintText(node)));
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            serializer.attribute("", "display-id", Integer.toString(Api30Impl.getDisplayId(node)));
        }

        if (currentRemainingDepth > 0) {
            int childCount = node.getChildCount();
            for (int i = 0; i < childCount; i++) {
                AccessibilityNodeInfo child = node.getChild(i);
                if (child != null) {
                    if (child.isVisibleToUser()) {
                        dumpNodeRecursive(child, serializer, i, currentRemainingDepth - 1);
                        child.recycle();
                    }
                }
            }
        }
        serializer.endTag("", "node");
    }

    private static boolean nafExcludedClass(AccessibilityNodeInfo node) {
        String className = safeCharSeqToString(node.getClassName());
        for(String excludedClassName : NAF_EXCLUDED_CLASSES) {
            if(className.endsWith(excludedClassName))
                return true;
        }
        return false;
    }

    private static boolean nafCheck(AccessibilityNodeInfo node) {
        boolean isNaf = node.isClickable() && node.isEnabled()
                && safeCharSeqToString(node.getContentDescription()).isEmpty()
                && safeCharSeqToString(node.getText()).isEmpty();
        if (!isNaf) return true;
        return childNafCheck(node);
    }

    private static boolean childNafCheck(AccessibilityNodeInfo node) {
        int childCount = node.getChildCount();
        for (int x = 0; x < childCount; x++) {
            AccessibilityNodeInfo childNode = node.getChild(x);
            if (childNode == null) continue;
            if (!safeCharSeqToString(childNode.getContentDescription()).isEmpty()
                    || !safeCharSeqToString(childNode.getText()).isEmpty()) {
                return true;
            }
            if (childNafCheck(childNode)) return true;
        }
        return false;
    }

    private static String safeCharSeqToString(CharSequence cs) {
        return cs == null ? "" : stripInvalidXMLChars(cs);
    }

    private static String stripInvalidXMLChars(CharSequence cs) {
        StringBuilder ret = new StringBuilder();
        char ch;
        for (int i = 0; i < cs.length(); i++) {
            ch = cs.charAt(i);
            if ((ch >= 0x0 && ch <= 0x8) || (ch >= 0xB && ch <= 0xC) || (ch >= 0xE && ch <= 0x1F) ||
                (ch >= 0x7F && ch <= 0x84) || (ch >= 0x86 && ch <= 0x9F) ||
                (ch >= 0xD800 && ch <= 0xDFFF) || (ch >= 0xFDD0 && ch <= 0xFDDF) || 
                (ch >= 0xFFFE && ch <= 0xFFFF)) {
                ret.append(".");
            } else {
                ret.append(ch);
            }
        }
        return ret.toString();
    }

    static class Api24Impl {
        private Api24Impl() { }
        static int getDrawingOrder(AccessibilityNodeInfo accessibilityNodeInfo) {
            return accessibilityNodeInfo.getDrawingOrder();
        }
    }

    static class Api26Impl {
        private Api26Impl() { }
        static String getHintText(AccessibilityNodeInfo accessibilityNodeInfo) {
            CharSequence chars = accessibilityNodeInfo.getHintText();
            return chars != null ? chars.toString() : null;
        }
    }

    @RequiresApi(30)
    static class Api30Impl {
        private Api30Impl() { }
        static int getDisplayId(AccessibilityNodeInfo accessibilityNodeInfo) {
            AccessibilityWindowInfo accessibilityWindowInfo = accessibilityNodeInfo.getWindow();
            return accessibilityWindowInfo == null ? Display.DEFAULT_DISPLAY :
                    accessibilityWindowInfo.getDisplayId();
        }
    }

    static Rect getVisibleBoundsInScreen(AccessibilityNodeInfo node, int width, int height,
            boolean trimScrollableParent) {
        return getVisibleBoundsInScreen(node, new Rect(0, 0, width, height), trimScrollableParent);
    }

    static Rect getVisibleBoundsInScreen(AccessibilityNodeInfo node, Rect displayRect,
            boolean trimScrollableParent) {
        if (node == null) {
            return null;
        }
        Rect nodeRect = new Rect();
        node.getBoundsInScreen(nodeRect);

        if (displayRect == null) {
            displayRect = new Rect();
        }
        intersectOrWarn(nodeRect, displayRect);

        Rect bounds = new Rect();
        AccessibilityWindowInfo window = HelperApi21Impl.getWindow(node);
        if (window != null) {
            HelperApi21Impl.getBoundsInScreen(window, bounds);
            intersectOrWarn(nodeRect, bounds);
        }

        if (trimScrollableParent) {
            for (AccessibilityNodeInfo ancestor = node.getParent(); ancestor != null; ancestor =
                    ancestor.getParent()) {
                if (ancestor.isScrollable()) {
                    Rect ancestorRect = getVisibleBoundsInScreen(ancestor, displayRect, true);
                    intersectOrWarn(nodeRect, ancestorRect);
                    break;
                }
            }
        }
        return nodeRect;
    }

    private static void intersectOrWarn(Rect target, Rect bounds) {
        target.intersect(bounds);
    }

    static class HelperApi21Impl {
        private HelperApi21Impl() {
        }
        static void getBoundsInScreen(AccessibilityWindowInfo accessibilityWindowInfo,
                Rect outBounds) {
            accessibilityWindowInfo.getBoundsInScreen(outBounds);
        }
        static AccessibilityWindowInfo getWindow(AccessibilityNodeInfo accessibilityNodeInfo) {
            return accessibilityNodeInfo.getWindow();
        }
    }

    /**
     * 打印节点层次结构到日志
     * @param rootNode 根节点
     */
    public static void printNodeHierarchy(AccessibilityNodeInfo rootNode) {
        if (rootNode == null) {
            FileLogger.e(TAG, "printNodeHierarchy: rootNode is null");
            return;
        }
        
        StringBuilder hierarchyTree = new StringBuilder();
        hierarchyTree.append("=== UI Hierarchy Dump Start ===\n");
        buildNodeHierarchyString(rootNode, 0, hierarchyTree);
        hierarchyTree.append("=== UI Hierarchy Dump End ===");
        FileLogger.e(TAG, hierarchyTree.toString());
    }

    /**
     * 递归构建节点层次结构字符串
     * @param node 要处理的节点
     * @param depth 当前深度
     * @param hierarchyBuilder 用于构建层次结构的StringBuilder
     */
    private static void buildNodeHierarchyString(AccessibilityNodeInfo node, int depth, StringBuilder hierarchyBuilder) {
        if (node == null) {
            return;
        }
        for (int i = 0; i < depth; i++) {
            hierarchyBuilder.append("  ");
        }
        // 获取详细的节点信息
        hierarchyBuilder.append("[depth=").append(depth).append("] ");
        hierarchyBuilder.append("class=").append(safeCharSeqToString(node.getClassName())).append(" ");
        hierarchyBuilder.append("text='").append(safeCharSeqToString(node.getText())).append("' ");
        hierarchyBuilder.append("resource-id=").append(safeCharSeqToString(node.getViewIdResourceName())).append(" ");
        hierarchyBuilder.append("package=").append(safeCharSeqToString(node.getPackageName())).append(" ");
        hierarchyBuilder.append("content-desc='").append(safeCharSeqToString(node.getContentDescription())).append("' ");
        hierarchyBuilder.append("bounds=").append(getBoundsString(node));
        hierarchyBuilder.append("\n");
        // 递归处理子节点
        int childCount = node.getChildCount();
        for (int i = 0; i < childCount; i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child != null) {
                buildNodeHierarchyString(child, depth + 1, hierarchyBuilder);
                child.recycle();
            }
        }
    }

    /**
     * 获取节点边界信息字符串
     * @param node 节点
     * @return 边界信息字符串
     */
    private static String getBoundsString(AccessibilityNodeInfo node) {
        if (node == null) {
            return "null";
        }
        Rect bounds = new Rect();
        node.getBoundsInScreen(bounds);
        return String.format("[%d,%d][%d,%d]", bounds.left, bounds.top, bounds.right, bounds.bottom);
    }
}