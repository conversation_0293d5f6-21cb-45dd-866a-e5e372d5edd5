package com.xiaopeng.xpautotest.client.oss;

import com.xiaopeng.xpautotest.App;
import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.utils.ApiRouterUtils;

public class OSSManager implements IUploader {
    private static final String TAG = "OSSManager";
    private final IUploader uploader;
    private static volatile OSSManager instance;

    private OSSManager() {
        if (!ApiRouterUtils.isFactoryMode()) {
            FileLogger.i(TAG, "Non-Factory Mode: Using MinIOUploader.");
            this.uploader = MinIOOSSUploader.getInstance();
        } else {
            FileLogger.i(TAG, "Factory Mode: Using AliyunOSSUploader.");
            this.uploader = AliyunOSSUploader.getInstance();
        }
    }

    public static OSSManager getInstance() {
        if (instance == null) {
            synchronized (OSSManager.class) {
                if (instance == null) {
                    instance = new OSSManager();
                }
            }
        }
        return instance;
    }

    @Override
    public boolean upload(UploadFileTask uploadFileTask){
        try {
            return uploader.upload(uploadFileTask);
        } catch (Exception e) {

            FileLogger.e(TAG, "Upload failed via " + uploader.getClass().getSimpleName() + " for " + uploadFileTask.getAbsoluteFilePath() + ": " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
}