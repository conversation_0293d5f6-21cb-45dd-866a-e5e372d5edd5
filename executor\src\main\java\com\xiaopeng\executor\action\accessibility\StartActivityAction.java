package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class StartActivityAction extends BaseAction {
    private static final String TAG = "StartActivityAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String packageName = context.getStringParam();
        if (packageName == null || packageName.isEmpty()) {
            throw new ActionException("package name is null or empty!", FailureCode.SI001);
        }

        boolean result;
        String activityName = context.getStringParam();
        double waitTime = context.getDoubleParam();
        if (activityName == null || activityName.isEmpty()) {
            FileLogger.i(TAG, "begin startPackage " + packageName);
            result = this.service.startPackage(packageName, waitTime);
        } else {
            FileLogger.i(TAG, "begin startActivity " + packageName + "/" + activityName);
            result = this.service.startActivity(packageName, activityName, waitTime);
        }

        if (!result) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.EI004);
            return TestResult.failure("Failed to start activity: " + packageName + "/" + activityName);
        }
        return TestResult.success("Successfully started activity: " + packageName + "/" + activityName);
    }
}
