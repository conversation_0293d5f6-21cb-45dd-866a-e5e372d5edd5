package com.xiaopeng.xpautotest.ui;

import android.content.Context;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import com.xiaopeng.lib.utils.ThreadUtils;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xui.widget.XButton;

import androidx.lifecycle.MutableLiveData;

public class TestingFloatingView {
    private Context context;
    private WindowManager windowManager;
    private View floatingView;
    private XButton closeButton;
    private MutableLiveData<String> testProgress = new MutableLiveData<>();
    private OnClickedListener listener;
    public interface OnClickedListener {
        void onClicked();
    }

    public TestingFloatingView(Context context, OnClickedListener listener) {
        this.context = context;
        this.listener = listener;
        this.windowManager = (WindowManager) context.getSystemService(context.WINDOW_SERVICE);
        open();
    }

    private void open() {
        floatingView = LayoutInflater.from(this.context).inflate(R.layout.ly_floating_window, null);

        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT);

        // 悬浮窗默认显示在右下角
        params.gravity = Gravity.BOTTOM | Gravity.END;
        params.x = 0;
        params.y = 100;

        windowManager.addView(floatingView, params);

        closeButton = floatingView.findViewById(R.id.close_button);
        closeButton.setOnClickListener(v -> this.listener.onClicked());
//        testProgress.observe(getViewLifecycleOwner(), progress -> closeButton.setText("测试进度: " + progress));
    }

    public void updateProgress(String progress) {
        testProgress.postValue(progress);
        Log.i("updateProgress: " + progress);
        ThreadUtils.runOnMainThread(() -> {
            try {
                if (floatingView != null && floatingView.isAttachedToWindow()) {
                    closeButton.setText("测试进度: " + testProgress.getValue());
                }
            } catch (Throwable t) {
                Log.e("FloatingView", "setText failed, view may be detached");
            }
        });
    }

    public void close() {
        if (floatingView != null) {
            windowManager.removeView(floatingView);
        }
    }

}
