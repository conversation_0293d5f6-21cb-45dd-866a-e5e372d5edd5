package com.xiaopeng.executor.core;

import com.xiaopeng.executor.bean.ExecutionState;
import com.xiaopeng.executor.bean.ExecutorContext;
import com.xiaopeng.xpautotest.community.test.TestScene;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阶段执行单元
 * 
 * 通用的阶段执行单元，可以处理Precondition、Procedure、PostCondition等任何阶段
 */
public class PhaseUnit implements ExecutableUnit {
    private static final String TAG = "PhaseUnit";
    private final String phaseType;  // 阶段类型：Precondition、Procedure、PostCondition
    private final List<TestScene> scenes;
    private final List<SceneUnit> sceneUnits;
    
    /**
     * 构造函数
     * 
     * @param phaseType 阶段类型（Precondition、Procedure、PostCondition）
     * @param scenes 该阶段的所有场景
     */
    public PhaseUnit(String phaseType, List<TestScene> scenes) {
        this.phaseType = phaseType;
        this.scenes = scenes;
        this.sceneUnits = scenes.stream()
            .map(SceneUnit::new)
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean execute(ExecutorContext context) {
        ExecutionState state = context.getExecutionState();
        int loopIndex = state.getSceneLoopIndex();

        if (scenes.isEmpty()) {
            FileLogger.i(TAG, "No " + phaseType.toLowerCase() + " scenes to execute");
            return true;
        }

        FileLogger.i(TAG, "Begin to execute " + phaseType + ", scene count: " + scenes.size());

        // 设置当前阶段类型到ExecutorContext
        context.setCurrentPhaseType(phaseType);

        for (int i = 0; i < sceneUnits.size(); i++) {
            TestScene scene = scenes.get(i);

            // 更新执行状态：保持当前脚本循环和阶段索引，更新场景索引
            context.updateExecutionLocation(state.getScriptLoopIndex(), state.getCurrentPhaseIndex(), i + 1, 0, 0);

            // 执行Scene
            boolean result = sceneUnits.get(i).execute(context);
            if (!result) {
                FileLogger.e(TAG, phaseType + " scene-" + (i + 1) + " " + scene + "|FAIL");
                return false;
            }
        }

        FileLogger.i(TAG, phaseType + " execution completed successfully");
        return true;
    }
    
    @Override
    public String getUnitId() {
        return phaseType + "Unit[" + scenes.size() + " scenes]";
    }
    

    /**
     * 获取阶段类型
     */
    public String getPhaseType() {
        return phaseType;
    }
    
    /**
     * 获取该阶段的所有场景
     */
    public List<TestScene> getScenes() {
        return scenes;
    }
    
    /**
     * 检查该阶段是否为空
     */
    public boolean isEmpty() {
        return scenes.isEmpty();
    }
}
