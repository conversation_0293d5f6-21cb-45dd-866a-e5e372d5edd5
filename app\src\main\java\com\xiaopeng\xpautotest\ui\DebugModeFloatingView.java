package com.xiaopeng.xpautotest.ui;

import android.content.Context;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xui.widget.XButton;

import androidx.lifecycle.MutableLiveData;

public class DebugModeFloatingView {
    private Context context;
    private WindowManager windowManager;
    private View debugFloatingView;
    private XButton closeButton;
    private MutableLiveData<String> testProgress = new MutableLiveData<>();
    private OnClickedListener listener;
    public interface OnClickedListener {
        void onClicked();
    }

    public DebugModeFloatingView(Context context, OnClickedListener listener) {
        this.context = context;
        this.listener = listener;
        this.windowManager = (WindowManager) context.getSystemService(context.WINDOW_SERVICE);
        open();
    }

    private void open() {
        debugFloatingView = LayoutInflater.from(this.context).inflate(R.layout.at_floating_debug_mode, null);

        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT);

        // 悬浮窗默认显示在右下角
        params.gravity = Gravity.BOTTOM | Gravity.END;
        params.x = 0;
        params.y = 100;

        windowManager.addView(debugFloatingView, params);

        closeButton = debugFloatingView.findViewById(R.id.debug_mode_close_button);
        closeButton.setOnClickListener(v -> this.listener.onClicked());
//        testProgress.observe(getViewLifecycleOwner(), progress -> closeButton.setText("测试进度: " + progress));
    }

    public void close() {
        if (debugFloatingView != null) {
            windowManager.removeView(debugFloatingView);
        }
    }

}
