package com.xiaopeng.executor.core;

import com.xiaopeng.executor.bean.ExecutionStoppedException;
import com.xiaopeng.executor.bean.ExecutorContext;

/**
 * 统一的可执行单元接口
 *
 * 这是重构的核心抽象，所有执行单元（脚本、场景、步骤）都实现此接口
 * 通过统一抽象实现代码复用和可扩展性
 */
public interface ExecutableUnit {

    /**
     * 执行单元
     *
     * 通过ExecutorContext参数传递所有需要的上下文信息：
     * - 基础设施访问（BaseTestExecutor）
     * - 执行状态管理（ExecutionState）
     * - 执行控制管理（暂停/恢复/停止）
     *
     * @param context 执行器上下文，包含所有执行所需的状态和基础设施
     * @return 执行结果，true表示成功，false表示失败
     * @throws ExecutionStoppedException 当手动停止执行时抛出
     */
    boolean execute(ExecutorContext context) throws ExecutionStoppedException;

    /**
     * 获取单元标识符
     *
     * @return 单元的唯一标识符，用于日志记录和调试
     */
    default String getUnitId() {
        return this.getClass().getSimpleName() + "@" + Integer.toHexString(hashCode());
    }
}
