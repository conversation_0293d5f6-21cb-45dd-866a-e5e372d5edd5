package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class CheckTextNotEqualByIDAction extends BaseAction {
    private static final String TAG = "CheckTextNotEqualByIDAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String sourceId = (String) context.getStringParam();
        String notExpectedText = (String) context.getStringParam();
        if (sourceId == null || notExpectedText == null) {
            throw new ActionException("sourceId or notExpectedText is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "resourceId: " + sourceId + ", notExpectedText: " + notExpectedText);
        String actualText = this.service.getTextById(sourceId);
        boolean result = actualText.equals(notExpectedText);
        if (result) {
            return TestResult.failure("Text should not equal '" + notExpectedText + "' for element " + sourceId + ", but actual text is: '" + actualText + "'");
        }
        return TestResult.success("Text check passed for element " + sourceId + ": text is not '" + notExpectedText + "'");
    }
}
