package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class DelayAction extends BaseAction {
    private static final String TAG = "DelayAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        double time = context.getDoubleParam();
        if (time == -1) {
            throw new ActionException("delay time is null!", FailureCode.SI001);
        }
        FileLogger.i(TAG, "begin wait " + (long) (1000L * time));
        try {
            Thread.sleep((long) (1000L * time));
            return TestResult.success("Delay action completed successfully.");
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new ActionException(e.getMessage(), FailureCode.EI004, e);
        }
    }
}
