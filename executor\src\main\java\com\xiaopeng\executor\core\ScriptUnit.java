package com.xiaopeng.executor.core;

import com.xiaopeng.executor.bean.ExecutionStoppedException;
import com.xiaopeng.executor.bean.ExecutorContext;
import com.xiaopeng.xpautotest.community.test.TestScript;
import com.xiaopeng.xpautotest.community.test.TestScene;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Script execution unit
 *
 * 按照脚本的真实层次结构重新设计：
 * ScriptUnit -> PreconditionUnit/ProcedureUnit/PostConditionUnit -> SceneUnit -> StepUnit
 *
 * 根据TestScene的phaseType分组执行：
 * 1. Precondition阶段的所有Scene
 * 2. Procedure阶段的所有Scene
 * 3. PostCondition阶段的所有Scene
 */
public class ScriptUnit implements ExecutableUnit {

    private static final String TAG = "ScriptUnit";
    private final TestScript script;
    private final List<PhaseUnit> phaseUnits;

    public ScriptUnit(TestScript script) {
        this.script = script;

        // 按phaseType分组TestScene并创建PhaseUnit列表
        this.phaseUnits = createPhaseUnits(script);
    }

    /**
     * 创建阶段执行单元列表
     * 按照Precondition -> Procedure -> PostCondition的顺序
     */
    private List<PhaseUnit> createPhaseUnits(TestScript script) {
        List<PhaseUnit> units = new ArrayList<>();

        // 定义阶段顺序
        String[] phaseTypes = {"Precondition", "Procedure", "PostCondition"};

        for (String phaseType : phaseTypes) {
            List<TestScene> scenes = script.getTestSceneList().stream()
                .filter(scene -> phaseType.equals(scene.getPhaseType()))
                .collect(Collectors.toList());

            if (!scenes.isEmpty()) {
                units.add(new PhaseUnit(phaseType, scenes));
            }
        }

        return units;
    }
    
    @Override
    public boolean execute(ExecutorContext context) throws ExecutionStoppedException {
        if (script.empty()) {
            FileLogger.e(TAG, "load TestScript was empty!");
            return false;
        }

        FileLogger.i(TAG, "begin to run TestScript " + script.getScriptId() + ", loop times " + script.getLoopTime()
                + ", total phases: " + phaseUnits.size() + ", scenes: " + script.getTestSceneList().size());

        // 脚本级循环
        for (int i = 0; i < script.getLoopTime(); i++) {
            int loopIndex = i + 1;
            // 按顺序执行所有阶段
            for (int phaseIndex = 0; phaseIndex < phaseUnits.size(); phaseIndex++) {
                PhaseUnit phaseUnit = phaseUnits.get(phaseIndex);

                // 更新执行状态：脚本循环、阶段索引、场景索引、场景循环、步骤索引
                context.updateExecutionLocation(loopIndex, phaseIndex + 1, 0, 0, 0);

                boolean result = phaseUnit.execute(context);
                if (!result) {
                    FileLogger.e(TAG, "Script loop " + loopIndex + " " + phaseUnit.getPhaseType() + " FAIL");
                    return false;
                }
            }

            FileLogger.i(TAG, "Script loop " + loopIndex + " completed successfully");
        }

        FileLogger.i(TAG, "TestScript " + script.getScriptId() + " execution completed successfully");
        return true;
    }

    @Override
    public String getUnitId() {
        return "ScriptUnit[" + script.getScriptId() + "]";
    }

    /**
     * 获取脚本ID
     * 
     * @return 脚本ID
     */
    public long getScriptId() {
        return script.getScriptId();
    }

    /**
     * 检查脚本是否为空
     * 
     * @return true表示脚本为空
     */
    public boolean empty() {
        return script.empty();
    }
}
