package com.xiaopeng.xpautotest.adapter;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.xiaopeng.xpautotest.BuildConfig;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.model.TestCaseItem;
import com.xiaopeng.xpautotest.helper.ResourceHelper;
import com.xiaopeng.xui.widget.XButton;
import com.xiaopeng.xui.widget.XImageView;
import com.xiaopeng.xui.widget.XTextView;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class TestCaseAdapter extends RecyclerView.Adapter<TestCaseAdapter.ViewHolder> {
    private static final String TAG = "TestCaseAdapter";
    private List<TestCaseItem> testCases;
    private OnActionClickListener listener;
    public interface OnActionClickListener {
        void onExecuteClick(TestCaseItem caseItem);
        void onStopClick(TestCaseItem caseItem);
    }

    public TestCaseAdapter(List<TestCaseItem> testCases, OnActionClickListener listener) {
        this.testCases = testCases;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.at_layout_factory_component_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
//        if (BuildConfig.DEBUG) {
//            Log.i(TAG, "onBindViewHolder: " + position);
//        }
        TestCaseItem testCase = testCases.get(position);
//        Log.i(TAG, "onBindViewHolder: " + testCase.getStatus());
        holder.mCaseNameView.setText(testCase.getName());
//        holder.mStatusImageView.setImageResource(R.mipmap.icon_status_pending);
        holder.mStatusImageView.setImageResource(
                ResourceHelper.getCaseExecuteStateIcon(testCase.getStatus()));

        updateButtonState(holder, testCase);
//        holder.mViewLogButton.setText(R.string.component_view_log);
//        holder.mViewLogButton.setOnClickListener(v -> mOnClickedListener.onClicked(v, position));
//
//        holder.mHistoryResultButton.setText(R.string.component_view_history_result);
//        holder.mHistoryResultButton.setOnClickListener(v -> mOnClickedListener.onClicked(v, position));
//        holder.mHistoryResultButton.setEnabled(false);

//        holder.mExecuteButton.setText(R.string.component_execute);
        holder.mExecuteButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onExecuteClick(testCase);
            }
        });

//        holder.mAbortButton.setOnClickListener(v -> {
//            if (listener != null) {
//                listener.onStopClick(testCase);
//            }
//        });
//        holder.mAbortButton.setEnabled(false);
    }

    private void updateButtonState(ViewHolder holder, TestCaseItem item) {
        // 根据运行状态控制按钮
        if (item.isFailure()) {
            holder.mExecuteButton.setEnabled(true);
        } else {
            holder.mExecuteButton.setEnabled(false);
        }
//        holder.mAbortButton.setEnabled(isExecuting);
//        holder.progressBar.setVisibility(isRunning ? View.VISIBLE : View.GONE);
//        holder.tvStatus.setVisibility(isRunning ? View.VISIBLE : View.GONE);
    }

    /**
     * 刷新全部数据, 用于首次加载或者下拉刷新
     * */
    @SuppressLint("NotifyDataSetChanged")
    public void updateData(List<TestCaseItem> testCases) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "updateData, TestCaseList size: " + (testCases != null ? testCases.size() : "null"));
        }
        this.testCases = testCases;
        notifyDataSetChanged();
    }

    /**
     * 更新单个数据, 用于局部刷新，比如更新某个item的状态，减少性能消耗
     * */
    public void updateItemData(TestCaseItem testCase, int position) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "updateItemData: " + position + ", " + testCase.getName());
        }
        testCases.set(position, testCase);
        notifyItemChanged(position);
    }

    public void updateTestResult(TestResultEntity result) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "updateTestResult: " + result.getScriptId());
        }
        int position = -1;
        // 根据scriptId查找对应的位置
        for (int i = 0; i < testCases.size(); i++) {
            if (testCases.get(i).getScriptId() == result.getScriptId()) {
                position = i;
                break;
            }
        }
        if (position == -1) {
            Log.e(TAG, "updateTestResult: not found position");
            return;
        }
        TestCaseItem testCase = testCases.get(position);
        testCase.setStatus(result.getStatus());
        testCases.set(position, testCase);
        notifyItemChanged(position);
    }

    @Override
    public int getItemCount() {
        if (testCases == null) {
            return 0;
        }
        return testCases.size();
    }

    public List<TestCaseItem> getTestCases() {
        return testCases;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        XTextView mCaseIdView, mCaseNameView, mCaseStartTimeView, mCaseEndTimeView, mCaseResultView;
        XImageView mStatusImageView;
        XButton mViewLogButton, mHistoryResultButton, mExecuteButton, mAbortButton;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
//            mCaseIdView = itemView.findViewById(R.id.tv_case_id);
            mCaseNameView = itemView.findViewById(R.id.tv_name);
            mStatusImageView = itemView.findViewById(R.id.iv_status);
//            startTime = itemView.findViewById(R.id.tv_start_time);
//            testResult = itemView.findViewById(R.id.tv_test_result);
//            failureReason = itemView.findViewById(R.id.tv_failure_reason);

//            mViewLogButton = itemView.findViewById(R.id.button_view_log);
//            mHistoryResultButton = itemView.findViewById(R.id.button_view_history_result);
            mExecuteButton = itemView.findViewById(R.id.button_execute);
//            mAbortButton = itemView.findViewById(R.id.button_abort);
        }
    }
}
