package com.xiaopeng.xpautotest.utils;

import android.car.Car;

import com.xiaopeng.xpautotest.constant.Constant;
import com.xiaopeng.xpautotest.community.utils.Log;

public final class CarUtils {
    // Car Type
    public static final String CAR_TYPE_UNKNOWN = "UNKNOWN";
    public static final String CAR_TYPE_E28A = "E28A";
    public static final String CAR_TYPE_E28AV = "E28AV";
    private static final String CAR_TYPE_E28B = "E28B";
    private static final String CAR_TYPE_E29 = "E29";
    public static final String CAR_TYPE_E38 = "E38";
    public static final String CAR_TYPE_E38A = "E38A";
    public static final String CAR_TYPE_E38V = "E38V";
    public static final String CAR_TYPE_F01 = "F01";
    public static final String CAR_TYPE_F30 = "F30";
    public static final String CAR_TYPE_F30B = "F30B";
    public static final String CAR_TYPE_F30R = "F30R";
    public static final String CAR_TYPE_F57 = "F57";
    public static final String CAR_TYPE_H93 = "H93";
    public static final String CAR_TYPE_H93R = "H93R";
    public static final String CAR_TYPE_V01 = "QQ";

    // Car CDU版本号
    public static final String CDU_TYPE_UNKNOWN = "UNKNOWN";

    private static String sCarType = CAR_TYPE_UNKNOWN;
    private static String sCduType = CDU_TYPE_UNKNOWN;

    private CarUtils() {
    }

    public static String getCarType() {
        if (CAR_TYPE_UNKNOWN.equals(sCarType)) {
            switch (getXpCduType()) {
                case Car.CAR_CDU_TYPE_Q8_E28A:
                    sCarType = judgeE28aType();
                    break;
                case Car.CAR_CDU_TYPE_Q8_E28B:
                    sCarType = CAR_TYPE_E28B;
                    break;
                case Car.CAR_CDU_TYPE_QG_E29:
                    sCarType = CAR_TYPE_E29;
                    break;
                case Car.CAR_CDU_TYPE_Q7_E38:
                case Car.CAR_CDU_TYPE_Q7_E38A:
                case Car.CAR_CDU_TYPE_QL_E38B:
                    sCarType = judgeE38Type();
                    break;
                case Car.CAR_CDU_TYPE_QE_F01:
                    sCarType = CAR_TYPE_F01;
                    break;
                case Car.CAR_CDU_TYPE_Q9_F30:
                    sCarType = judgeF30Type();
                    break;
                case Car.CAR_CDU_TYPE_QI_F30B:
                    sCarType = CAR_TYPE_F30B;
                    break;
                case Car.CAR_CDU_TYPE_QD_F57:
                    sCarType = CAR_TYPE_F57;
                    break;
                case Car.CAR_CDU_TYPE_QB_H93:
                    sCarType = judgeH93Type();
                    break;
                case Car.CAR_CDU_TYPE_QQ_V01:
                    sCarType = CAR_TYPE_V01;
                    break;
                default:
                    break;
            }
            Log.i("getCarType: " + sCarType);
        }
        return sCarType;
    }

    public static String getXpCduType() {
        if (CAR_TYPE_UNKNOWN.equals(sCduType)) {
            try {
                sCduType = Car.getXpCduType();
            } catch (Exception e) {
                Log.e("CarUtils", "getXpCduType error = " + e);
            }
            Log.i("getXpCduType: " + sCduType);
        }
        return sCduType;
    }

    private static String judgeE38Type() {
        if (isEURegion()) {
            return CAR_TYPE_E38V;
        }
        return CAR_TYPE_E38;
    }

    private static String judgeE28aType() {
        if (isEURegion()) {
            return CAR_TYPE_E28AV;
        }
        return CAR_TYPE_E28A;
    }

    private static String judgeF30Type() {
        if (isEURegion()) {
            return CAR_TYPE_F30R;
        }
        return CAR_TYPE_F30;
    }

    private static String judgeH93Type() {
        if (isEURegion()) {
            return CAR_TYPE_H93R;
        }
        return CAR_TYPE_H93;
    }

    /**
     * 是否是国际版EU车型
     */
    public static boolean isEURegion() {
        try {
            return Car.isExportVersion();
        } catch (Exception e) {
            Log.e("CarUtils", "isEURegion error = " + e);
        }
        return false;
    }
}
