package com.xiaopeng.xpautotest.trace.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.SystemClock;

import androidx.annotation.Nullable;

import com.xiaopeng.xpautotest.community.event.TraceResultState;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.trace.BuildConfig;
import com.xiaopeng.xpautotest.community.event.TraceEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;


public class CanDataCollectService extends Service {
    private static final String TAG = "CanDataCollectService";
    private static final String TBOX_SERVER_IP = "***********";
    private static final int TBOX_SERVER_PORT = 3000;
    private static final String LOCAL_HOST = "**********";
    private LinkedBlockingQueue<byte[]> mByteDataQueue;
    private InputStream mInputStream;
    private volatile boolean mIsCollectingData = false;
    private volatile boolean mIsTboxConnected = false;
    private byte[] mSocketBuffer = new byte[8192];
    private ArrayList<TraceEvent.CanEvent> mCanSignals;
    private ArrayList<TraceEvent.LinEvent> mLinSignals;
    private ArrayList<TraceEvent.SomeIPEvent> mSomeIPSignals;
    private volatile boolean mMockMode = false;
    private ExecutorService executorService;

    /**
     * 计算CAN信号值
     *
     * @param startBit   起始位
     * @param length     信号长度
     * @param data       数据
     * @param resolution 信号系数
     * @param offset     偏移量
     * @return 计算后的信号值
     */
    private static double calculateCanSignal(int startBit, int length, byte[] data, double resolution, int offset) {
        int value;
        int lsbBitIndex = startBit % 8;
        int lsbByteIndex = startBit / 8;
        if (lsbBitIndex + length <= 8) {
            value = ((data[lsbByteIndex] & (0xFF >> (8 - lsbBitIndex - length))) >> lsbBitIndex);
            return value * resolution + offset;
        }
        int remainLength = (length - (8 - lsbBitIndex));
        if (remainLength <= 8) {
            int newByteIndex = lsbByteIndex - 1;
            value = ((data[newByteIndex] & (0xFF >> (8 - remainLength))) << (8 - lsbBitIndex)) | ((data[lsbByteIndex] & 0xFF) >> lsbBitIndex);
            return value * resolution + offset;
        }
        int completeByteSum = remainLength / 8;
        int topBitIndex = remainLength % 8;
        int topByteIndex = lsbByteIndex - completeByteSum - 1;
        value = (data[topByteIndex] & (0xFF >> (8 - topBitIndex)));
        for (int i = 0; i < completeByteSum; i++) {
            value = (value << 8) | (data[topByteIndex + 1 + i] & 0xFF);
        }
        value = (value << (8 - lsbBitIndex)) | ((data[lsbByteIndex] & 0xFF) >> lsbBitIndex);
        return value * resolution + offset;
    }

    /**
     * 计算LIN信号值
     *
     * @param startBit   起始位
     * @param length     信号长度
     * @param data       数据
     * @param resolution 信号系数
     * @param offset     偏移量
     * @return 计算后的信号值
     */
    private static double calculateLinSignal(int startBit, int length, byte[] data, double resolution, int offset) {
        int value = 0;
        for (int i = startBit; i < (startBit + length); i++) {
            int byteIndex = i / 8;
            int bitIndex = i % 8;
            value = (value << 1) | ((data[byteIndex] >> (7 - bitIndex)) & 0x01);
        }
        return value * resolution + offset;
    }

    /**
     * 获取实际长度
     *
     * @param lengthKey 长度键
     * @return 实际长度
     */
    private static int getRealLength(int lengthKey) {
        if (lengthKey < 0x1 || lengthKey > 0xF) {
            FileLogger.e(TAG, "Error: lengthKey is " + lengthKey);
            return 0;
        }
        int[] lengthMap = {0, 1, 2, 3, 4, 5, 6, 7, 8, 12, 16, 20, 24, 32, 48, 64};
        return lengthMap[lengthKey];
    }

    /**
     * 解析一帧数据
     *
     * @param data 数据
     */
    private void parseOneFrame(byte[] data) {
        int index = 0;
        boolean errorFlag = false;
        while (!errorFlag && index < data.length) {
            byte dataType = data[index];
            switch (dataType) {
                case 0x01:
                case 0x02: {
                    int channel = data[index + 3] & 0xFF;
                    int messageID = ((data[index + 4] & 0xFF) << 8) | (data[index + 5] & 0xFF);
                    int dataLength = data[index + 6] & 0xFF;
                    int realLength = getRealLength(dataLength);
                    byte[] dataBody = new byte[realLength];
                    System.arraycopy(data, index + 7, dataBody, 0, realLength);
                    for (TraceEvent.CanEvent signal : mCanSignals) {
                        if (signal.isFinished()) {
                            FileLogger.i(TAG, "can signal finished");
                            continue;
                        }
                        if (channel != signal.getChannel()) {
                            continue;
                        }
                        if (messageID != signal.getMessageID()) {
                            continue;
                        }
                        signal.setFound(true);
                        double value = calculateCanSignal(signal.getStartBit(), signal.getLength(), dataBody, signal.getResolution(), signal.getOffset());
                        if (!signal.compareValue(value)) {
                            continue;
                        }
                        FileLogger.i(TAG, "mCanSignals: " + signal.getScriptId() + " " + signal.getStepId() + " " + signal.getTraceId() + ", channel: " + channel + ", messageID: " + messageID + ", value: " + value);
                        EventBus.getDefault().post(
                                new TraceEvent.ResultEvent(signal.getScriptId(), signal.getStepId(), signal.getTraceId(), signal.getType(), signal.getSrc(), value, TraceResultState.OK, "")
                        );
                        FileLogger.i(TAG, "can post event, real: " + value + ", expected: " + signal.getExpectedValue());
                        signal.setFinished(true);
                    }
                    mCanSignals.removeIf(TraceEvent.BaseEvent::isFinished);
                    index += 7 + realLength;
                    break;
                }
                case 0x03: {
                    int channel = data[index + 3] & 0xFF;
                    int messageID = ((data[index + 4] & 0xFF) << 8) | (data[index + 5] & 0xFF);
                    int dataLength = data[index + 6] & 0xFF;
                    byte[] dataBody = new byte[dataLength];
                    System.arraycopy(data, index + 7, dataBody, 0, dataLength);
                    for (TraceEvent.LinEvent signal : mLinSignals) {
                        if (signal.isFinished()) {
                            FileLogger.i(TAG, "lin signal finished");
                            continue;
                        }
                        if (channel != signal.getChannel()) {
                            continue;
                        }
                        if (messageID != signal.getMessageID()) {
                            continue;
                        }
                        signal.setFound(true);
                        double value = calculateLinSignal(signal.getStartBit(), signal.getLength(), dataBody, signal.getResolution(), signal.getOffset());
                        if (!signal.compareValue(value)) {
                            continue;
                        }
                        FileLogger.i(TAG, "mLinSignals: " + signal.getScriptId() + " " + signal.getStepId() + " " + signal.getTraceId() + ", channel: " + channel + ", messageID: " + messageID + ", value: " + value);
                        EventBus.getDefault().post(
                                new TraceEvent.ResultEvent(signal.getScriptId(), signal.getStepId(), signal.getTraceId(), signal.getType(), signal.getSrc(), value, TraceResultState.OK, "")
                        );
                        FileLogger.i(TAG, "lin post event, real: " + value + ", expected: " + signal.getExpectedValue());
                        signal.setFinished(true);
                    }
                    mLinSignals.removeIf(TraceEvent.BaseEvent::isFinished);
                    index += 7 + dataLength;
                    break;
                }
                case 0x04: {
                    int nodeID = data[index + 3] & 0xFF;
                    int serviceID = ((data[index + 4] & 0xFF) << 8) | (data[index + 5] & 0xFF);
                    int elementID = ((data[index + 6] & 0xFF) << 8) | (data[index + 7] & 0xFF);
                    int dataLength = ((data[index + 8] & 0xFF) << 24) | ((data[index + 9] & 0xFF) << 16) | ((data[index + 10] & 0xFF) << 8) | (data[index + 11] & 0xFF);
                    //int clientID = ((data[index + 12] & 0xFF) << 8) | (data[index + 13] & 0xFF);
                    //String clientIDHex = String.format("0x%04X", clientID);
                    //int sessionID = ((data[index + 14] & 0xFF) << 8) | (data[index + 15] & 0xFF);
                    //int protocolVersion = data[index + 16] & 0xFF;
                    //int interfaceVersion = data[index + 17] & 0xFF;
                    //int messageType = data[index + 18] & 0xFF;
                    int returnCode = data[index + 19] & 0xFF;
                    int payloadLength = dataLength - 8;
                    if (returnCode != 0) {
                        FileLogger.e(TAG, "returnCode!=0, serviceID: " + serviceID + ", " + "elementID: " + elementID + ", payloadLength: " + payloadLength);
                    }
                    for (TraceEvent.SomeIPEvent signal : mSomeIPSignals) {
                        if (signal.isFinished()) {
                            FileLogger.i(TAG, "someip signal finished");
                            continue;
                        }
                        if (nodeID != signal.getNodeID()) {
                            continue;
                        }
                        if (serviceID != signal.getServiceID()) {
                            continue;
                        }
                        if (elementID != signal.getElementID()) {
                            continue;
                        }
                        if (payloadLength != signal.getMaxLength()) {
                            continue;
                        }
                        signal.setFound(true);
                        int startByteIndex = signal.getStartByte();
                        int endByteIndex = startByteIndex + signal.getLength();
                        int value = 0;
                        for (int i = startByteIndex; i < endByteIndex; i++) {
                            value = (value << 8) | (data[index + 19 + i] & 0xFF);
                        }
                        if (!signal.compareValue(value)) {
                            continue;
                        }
                        FileLogger.i(TAG, "mSomeIPSignals: " + signal.getScriptId() + " " + signal.getStepId() + " " + signal.getTraceId() + ", nodeID: " + nodeID + ", serviceID: " + serviceID + ", " + ", elementID: " + elementID + ", value: " + value);
                        EventBus.getDefault().post(
                                new TraceEvent.ResultEvent(signal.getScriptId(), signal.getStepId(), signal.getTraceId(), signal.getType(), signal.getSrc(), value, TraceResultState.OK, "")
                        );
                        FileLogger.i(TAG, "someip post event, real: " + value + ", expected: " + signal.getExpectedValue());
                        signal.setFinished(true);
                    }
                    mSomeIPSignals.removeIf(TraceEvent.BaseEvent::isFinished);
                    index += 12 + dataLength;
                    break;
                }
                case 0x05: {
                    int dataLength = ((data[index + 3] & 0xFF) << 24) | ((data[index + 4] & 0xFF) << 16) | ((data[index + 5] & 0xFF) << 8) | (data[index + 6] & 0xFF);
                    index += 7 + dataLength;
                    break;
                }
                case 0x06: {
                    int topicLength = data[index + 4] & 0xFF;
                    int tempIndex = 5 + topicLength + 8 + 2;
                    int dataLength = ((data[index + tempIndex] & 0xFF) << 24) | ((data[index + tempIndex + 1] & 0xFF) << 16) | ((data[index + tempIndex + 2] & 0xFF) << 8) | (data[index + tempIndex + 3] & 0xFF);
                    index += tempIndex + dataLength;
                    break;
                }
                default:
                    FileLogger.e(TAG, "Error: dataType is " + dataType);
                    errorFlag = true;
            }
        }
    }

    /**
     * 收集数据线程
     */
    private final Runnable mCollectCanDataBySocket = new Runnable() {
        @Override
        public void run() {
            FileLogger.i(TAG, "Collecting data thread is running");
            InetSocketAddress inetSocketAddress = new InetSocketAddress(TBOX_SERVER_IP, TBOX_SERVER_PORT);
            InetSocketAddress clientSocketAddress = new InetSocketAddress(LOCAL_HOST, 0);
            while (mIsCollectingData) {
                try (Socket client = new Socket()) {
                    client.setReceiveBufferSize(10 * 1024 * 1024);
                    client.setTcpNoDelay(true);
                    client.setSoTimeout(10 * 1000);
                    client.bind(clientSocketAddress);
                    client.connect(inetSocketAddress, 10 * 1000);
                    mInputStream = client.getInputStream();
                    mIsTboxConnected = true;
                    FileLogger.i(TAG, "Socket connected to TBOX");
                    int count;
                    while (mIsCollectingData) {
                        if ((count = mInputStream.read(mSocketBuffer)) > 0) {
                            byte[] temp = new byte[count];
                            System.arraycopy(mSocketBuffer, 0, temp, 0, count);
                            mByteDataQueue.put(temp);
                            //FileLogger.i("QueueSize", "QueueSize: " + mByteDataQueue.size());
                        }
                    }
                } catch (Exception e) {
                    FileLogger.e(TAG, "Socket error: " + e.getMessage());
                } finally {
                    try {
                        if (mInputStream != null) {
                            try {
                                mInputStream.close();
                            } catch (IOException e) {
                                FileLogger.e(TAG, "InputStream error: " + e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        FileLogger.e(TAG, "Disconnect error: " + e.getMessage());
                    }
                }
                if (!mIsTboxConnected) {
                    SystemClock.sleep(100); // 连接失败后等待一段时间再重试
                }
            }
        }
    };

    /**
     * 组装一帧数据的线程
     */
    private final Runnable mParseCanData = new Runnable() {
        @Override
        public void run() {
            FileLogger.i(TAG, "Parsing data thread is running");
            ByteArrayOutputStream curDataStream = new ByteArrayOutputStream();
            while (mIsCollectingData) {
                try {
                    byte[] data = mByteDataQueue.poll();
                    if (data == null) {
                        SystemClock.sleep(5); // 避免空轮询浪费 CPU
                        continue;
                    }
                    curDataStream.write(data);
                    byte[] curData = curDataStream.toByteArray();
                    if (curData.length < 6 || !(curData[0] == 0x78 && curData[1] == 0x73)) {
                        FileLogger.e(TAG, "Start flag is incorrect or insufficient data");
                        curDataStream.reset();
                        continue;
                    }
                    int totalLength = ((curData[2] & 0xFF) << 24) | ((curData[3] & 0xFF) << 16) | ((curData[4] & 0xFF) << 8) | (curData[5] & 0xFF);
                    int frameLength = totalLength + 6;
                    int dataBodyStartIndex = 13;
                    int dataBodyLength = frameLength - dataBodyStartIndex;
                    if (curData.length < frameLength) {
                        continue;
                    }
                    byte[] completeData = new byte[dataBodyLength];
                    System.arraycopy(curData, dataBodyStartIndex, completeData, 0, dataBodyLength);
                    curDataStream.reset();
                    if (curData.length > frameLength) {
                        curDataStream.write(curData, frameLength, curData.length - frameLength);
                    }
                    parseOneFrame(completeData);
                } catch (Exception e) {
                    curDataStream.reset();
                    e.printStackTrace();
                    FileLogger.e(TAG, "Parsing error: " + e.getMessage());
                }
            }
        }
    };

    /**
     * 模拟解析数据线程
     */
    private final Runnable mMockParseData = new Runnable() {
        @Override
        public void run() {
            FileLogger.i(TAG, "Mock thread is running");
            String result;
            String message;
            while (mIsCollectingData) {
                try {
                    SystemClock.sleep(1000); // 模拟数据解析间隔
                    for (TraceEvent.CanEvent signal : mCanSignals) {
                        result = TraceResultState.OK;
                        message = "";
                        double value = Math.random() * 100;
                        if (!signal.compareValue(value)) {
                            result = TraceResultState.NA;
                            message = "not expected value";
                        }
                        EventBus.getDefault().post(
                                new TraceEvent.ResultEvent(signal.getScriptId(), signal.getStepId(), signal.getTraceId(), signal.getType(), signal.getSrc(), value, result, message)
                        );
                        FileLogger.i(TAG, "mock can: post event, value: " + value);
                        signal.setFinished(true);
                    }
                    mCanSignals.removeIf(TraceEvent.CanEvent::isFinished);
                    for (TraceEvent.LinEvent signal : mLinSignals) {
                        result = TraceResultState.OK;
                        message = "";
                        double value = Math.random() * 100;
                        if (!signal.compareValue(value)) {
                            result = TraceResultState.NA;
                            message = "not expected value";
                        }
                        EventBus.getDefault().post(
                                new TraceEvent.ResultEvent(signal.getScriptId(), signal.getStepId(), signal.getTraceId(), signal.getType(), signal.getSrc(), value, result, message)
                        );
                        FileLogger.i(TAG, "mock lin: post event, value: " + value);
                        signal.setFinished(true);
                    }
                    mLinSignals.removeIf(TraceEvent.LinEvent::isFinished);
                    for (TraceEvent.SomeIPEvent signal : mSomeIPSignals) {
                        result = TraceResultState.OK;
                        message = "";
                        double value = Math.random() * 100;
                        if (!signal.compareValue(value)) {
                            result = TraceResultState.NA;
                            message = "not expected value";
                        }
                        EventBus.getDefault().post(
                                new TraceEvent.ResultEvent(signal.getScriptId(), signal.getStepId(), signal.getTraceId(), signal.getType(), signal.getSrc(), value, result, message)
                        );
                        FileLogger.i(TAG, "mock someip: post event, value: " + value);
                        signal.setFinished(true);
                    }
                    mSomeIPSignals.removeIf(TraceEvent.SomeIPEvent::isFinished);
                } catch (Exception e) {
                    FileLogger.e(TAG, "Mock error: " + e.getMessage());
                }
            }
        }
    };

    /**
     * 终止线程池
     */
    private void terminateExecutorService() {
        FileLogger.i(TAG, "terminateExecutorService");
        if (executorService != null && !executorService.isTerminated()) {
            try {
                executorService.shutdown();
                boolean isTerminated = executorService.awaitTermination(10, TimeUnit.SECONDS);
                FileLogger.i(TAG, "terminateExecutorService: isTerminated: " + isTerminated);
            } catch (InterruptedException e) {
                FileLogger.e(TAG, "terminateExecutorService: error: " + e.getMessage());
            }
        }
    }

    @Override
    public void onCreate() {
        FileLogger.i(TAG, "onCreate");
        super.onCreate();
        // 绑定事件监听
        EventBus.getDefault().register(this);
        mCanSignals = new ArrayList<>();
        mLinSignals = new ArrayList<>();
        mSomeIPSignals = new ArrayList<>();
        mByteDataQueue = new LinkedBlockingQueue<>();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        FileLogger.i(TAG, "onStartCommand");
        mMockMode = BuildConfig.MOCK;
        FileLogger.i(TAG, "mock mode: " + mMockMode);
        return START_NOT_STICKY;
    }

    /**
     * 接收开始解析事件
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onStartEvent(TraceEvent.StartEvent event) {
        FileLogger.i(TAG, "onStartEvent");
        if (mIsCollectingData) {
            FileLogger.i(TAG, "onStartEvent: already collecting can data");
            return;
        }
        terminateExecutorService();
        mIsCollectingData = true;
        mCanSignals.clear();
        mLinSignals.clear();
        mSomeIPSignals.clear();
        mByteDataQueue.clear();
        if (mMockMode) {
            executorService = Executors.newFixedThreadPool(1);
            executorService.execute(mMockParseData);
            return;
        }
        executorService = Executors.newFixedThreadPool(2);
        executorService.execute(mCollectCanDataBySocket);
        executorService.execute(mParseCanData);
    }

    /**
     * 处理测试脚本之行结束后失败的信号事件
     */
    private <T extends TraceEvent.BaseEvent> void handleFailedSignals(ArrayList<T> signals) {
        if (signals == null || signals.isEmpty()) return;
        for (T signal : signals) {
            Long scriptId = signal.getScriptId();
            int stepId = signal.getStepId();
            int traceId = signal.getTraceId();
            String type = signal.getType();
            String src = signal.getSrc();
            boolean isFound = signal.isFound();
            String message = isFound ? "not expected value" : "timeout";
            String result = isFound ? TraceResultState.NA : TraceResultState.NG;
            EventBus.getDefault().post(
                    new TraceEvent.ResultEvent(scriptId, stepId, traceId, type, src, 0, result, message)
            );
            FileLogger.e(TAG, type + " signal timeout: " + scriptId + "," + stepId + "," + traceId);
        }
    }

    /**
     * 接收停止解析事件
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEndEvent(TraceEvent.EndEvent event) {
        FileLogger.i(TAG, "onEndEvent");
        if (!mIsCollectingData) {
            FileLogger.i(TAG, "onEndEvent: not collecting can data");
            return;
        }
        mIsCollectingData = false;
        try {
            handleFailedSignals(mCanSignals);
            handleFailedSignals(mLinSignals);
            handleFailedSignals(mSomeIPSignals);
            mCanSignals.clear();
            mLinSignals.clear();
            mSomeIPSignals.clear();
            terminateExecutorService();
        } catch (Exception e) {
            e.printStackTrace();
            FileLogger.e(TAG, "onEndEvent: exception: " + e.getMessage());
        }
    }

    /**
     * 接收CAN信号解析事件
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onCanEvent(TraceEvent.CanEvent event) {
        FileLogger.i(TAG, "onCanEvent");
        if (!mIsTboxConnected) {
            sendTboxNotConnectedEvent(event.getScriptId(), event.getStepId(), event.getTraceId(), event.getType(), event.getSrc());
            return;
        }
        mCanSignals.add(event);
    }

    /**
     * 接收LIN信号解析事件
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onLinEvent(TraceEvent.LinEvent event) {
        FileLogger.i(TAG, "onLinEvent");
        if (!mIsTboxConnected) {
            sendTboxNotConnectedEvent(event.getScriptId(), event.getStepId(), event.getTraceId(), event.getType(), event.getSrc());
            return;
        }
        mLinSignals.add(event);
    }

    /**
     * 接收SOMEIP信号解析事件
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onSomeIPEvent(TraceEvent.SomeIPEvent event) {
        FileLogger.i(TAG, "onSomeIPEvent");
        if (!mIsTboxConnected) {
            sendTboxNotConnectedEvent(event.getScriptId(), event.getStepId(), event.getTraceId(), event.getType(), event.getSrc());
            return;
        }
        mSomeIPSignals.add(event);
    }

    /**
     * 发送TBOX未连接事件
     *
     * @param scriptId 脚本ID
     * @param stepId   步骤ID
     * @param traceId  跟踪ID
     * @param type     数据类型
     * @param src      数据源
     */
    private void sendTboxNotConnectedEvent(Long scriptId, int stepId, int traceId, String type, String src) {
        FileLogger.e(TAG, "TBOX is not connected, cannot collect data");
        EventBus.getDefault().post(
                new TraceEvent.ResultEvent(scriptId, stepId, traceId, type, src, 0, TraceResultState.ERROR, "TBOX not connected")
        );
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        FileLogger.i(TAG, "onDestroy");
        super.onDestroy();
        mIsCollectingData = false;
        EventBus.getDefault().unregister(this);
        terminateExecutorService();
    }
}
