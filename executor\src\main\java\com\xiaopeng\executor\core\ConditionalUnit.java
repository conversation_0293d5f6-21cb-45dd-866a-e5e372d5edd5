package com.xiaopeng.executor.core;

import com.xiaopeng.executor.bean.ExecutionState;
import com.xiaopeng.executor.bean.ExecutionStoppedException;
import com.xiaopeng.executor.bean.ExecutorContext;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.List;

/**
 * 条件执行单元
 * 
 * 实现if-else条件逻辑的执行控制：
 * 1. 条件评估：执行条件Action并判断结果
 * 2. 分支选择：根据条件结果选择执行分支
 * 3. 子单元执行：管理和执行分支内的ExecutableUnit
 * 4. 异常处理：处理条件评估和分支执行中的异常
 * 5. 日志记录：记录条件判断和分支执行的详细日志
 */
public class ConditionalUnit implements ExecutableUnit {
    
    private static final String TAG = "ConditionalUnit";
    
    private final ExecutableUnit ifCondition;                    // if条件
    private final List<ExecutableUnit> ifUnits;           // if分支执行单元
    private final List<ConditionalBranch> elseIfBranches; // elseif分支列表
    private final List<ExecutableUnit> elseUnits;         // else分支执行单元
    
    /**
     * 构造函数
     * 
     * @param ifCondition if条件
     * @param ifUnits if分支执行单元列表
     * @param elseIfBranches elseif分支列表
     * @param elseUnits else分支执行单元列表
     */
    public ConditionalUnit(ExecutableUnit ifCondition, 
                          List<ExecutableUnit> ifUnits,
                          List<ConditionalBranch> elseIfBranches,
                          List<ExecutableUnit> elseUnits) {
        this.ifCondition = ifCondition;
        this.ifUnits = ifUnits;
        this.elseIfBranches = elseIfBranches;
        this.elseUnits = elseUnits;
    }
    
    @Override
    public boolean execute(ExecutorContext context) throws ExecutionStoppedException {
        String executionContext = getExecutionContext(context);
        FileLogger.i(TAG, "Starting execution " + executionContext);

        // 评估if条件
        boolean ifResult = evaluateCondition(ifCondition, context);
        FileLogger.i(TAG, "If condition '" + ifCondition.getUnitId() + "' result: " + ifResult + " " + executionContext);

        if (ifResult) {
            // 执行if分支
            FileLogger.i(TAG, "Executing if branch with " + ifUnits.size() + " units " + executionContext);
            boolean ifExecutionResult = executeUnits(ifUnits, context, "if");
            FileLogger.i(TAG, "If branch execution result: " + ifExecutionResult + " " + executionContext);
            return ifExecutionResult;
        }

        // 评估elseif条件
        if (elseIfBranches != null && !elseIfBranches.isEmpty()) {
            for (int i = 0; i < elseIfBranches.size(); i++) {
                ConditionalBranch elseIfBranch = elseIfBranches.get(i);
                boolean elseIfResult = evaluateCondition(elseIfBranch.getCondition(), context);
                FileLogger.i(TAG, "ElseIf[" + i + "] condition '" +
                           elseIfBranch.getCondition().getUnitId() + "' result: " + elseIfResult + " " + executionContext);

                if (elseIfResult) {
                    // 执行匹配的elseif分支
                    FileLogger.i(TAG, "Executing elseif[" + i + "] branch with " +
                               elseIfBranch.getUnits().size() + " units " + executionContext);
                    boolean elseIfExecutionResult = executeUnits(elseIfBranch.getUnits(), context, "elseif[" + i + "]");
                    FileLogger.i(TAG, "ElseIf[" + i + "] branch execution result: " + elseIfExecutionResult + " " + executionContext);
                    return elseIfExecutionResult;
                }
            }
        }

        // 执行else分支
        if (elseUnits != null && !elseUnits.isEmpty()) {
            FileLogger.i(TAG, "Executing else branch with " + elseUnits.size() + " units " + executionContext);
            boolean elseExecutionResult = executeUnits(elseUnits, context, "else");
            FileLogger.i(TAG, "Else branch execution result: " + elseExecutionResult + " " + executionContext);
            return elseExecutionResult;
        }

        // 无匹配分支时返回成功
        FileLogger.i(TAG, "No matching condition, skipping all branches " + executionContext);
        return true;
    }
    
    /**
     * 评估条件
     *
     * @param condition 条件ExecutableUnit
     * @param context 执行器上下文
     * @return 条件评估结果，true表示条件成功
     */
    private boolean evaluateCondition(ExecutableUnit condition, ExecutorContext context) {
        if (condition == null) {
            FileLogger.w(TAG, "Null condition, returning false");
            return false;
        }

        try {
            FileLogger.i(TAG, "Evaluating condition: " + condition.getUnitId());

            // 设置为条件判断Action
            context.setConditionAction(true);
            boolean success = condition.execute(context);
            context.setConditionAction(false);

            FileLogger.i(TAG, "Condition '" + condition.getUnitId() + "' evaluation result: " + success);

            return success;

        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to evaluate condition: " + condition.getUnitId(), e);
            // 确保重置状态
            context.setConditionAction(false);
            return false;
        }
    }
    
    /**
     * 执行单元列表
     *
     * @param units 执行单元列表
     * @param context 执行器上下文
     * @param branchName 分支名称（用于日志）
     * @return 执行结果，所有单元都成功才返回true
     * @throws ExecutionStoppedException 当手动停止执行时抛出
     */
    private boolean executeUnits(List<ExecutableUnit> units, ExecutorContext context, String branchName) throws ExecutionStoppedException {
        if (units == null || units.isEmpty()) {
            FileLogger.i(TAG, branchName + " branch is empty, returning true");
            return true;
        }

        // 设置为条件分支内执行
        context.setInConditionalBranch(true);

        try {
            for (int i = 0; i < units.size(); i++) {
                ExecutableUnit unit = units.get(i);
                // 执行单元
                boolean result = unit.execute(context);
                if (!result) {
                    FileLogger.e(TAG, branchName + " branch unit[" + i + "] failed: " + unit.getUnitId());
                    return false;
                }
                FileLogger.i(TAG, branchName + " branch unit[" + i + "] succeeded: " + unit.getUnitId());
            }
        } finally {
            // 确保重置状态
            context.setInConditionalBranch(false);
        }

        FileLogger.i(TAG, branchName + " branch execution completed successfully");
        return true;
    }
    
    @Override
    public String getUnitId() {
        return "ConditionalUnit[if:" + (ifCondition != null ? ifCondition.getUnitId() : "null") +
               ", elseif:" + (elseIfBranches != null ? elseIfBranches.size() : 0) +
               ", else:" + (elseUnits != null ? "yes" : "no") + "]";
    }

    /**
     * 获取执行上下文信息，用于日志区分
     */
    private String getExecutionContext(ExecutorContext context) {
        if (context == null || context.getExecutionState() == null) {
            return "[context:null]";
        }

        ExecutionState state = context.getExecutionState();
        return String.format("[script:%d|phase:%d|scene:%d|loop:%d|step:%d]",
            state.getScriptLoopIndex(),
            state.getCurrentPhaseIndex(),
            state.getCurrentSceneIndex(),
            state.getSceneLoopIndex(),
            state.getCurrentStepIndex());
    }
    
    /**
     * 获取if条件
     */
    public ExecutableUnit getIfCondition() {
        return ifCondition;
    }
    
    /**
     * 获取if分支执行单元
     */
    public List<ExecutableUnit> getIfUnits() {
        return ifUnits;
    }
    
    /**
     * 获取elseif分支列表
     */
    public List<ConditionalBranch> getElseIfBranches() {
        return elseIfBranches;
    }
    
    /**
     * 获取else分支执行单元
     */
    public List<ExecutableUnit> getElseUnits() {
        return elseUnits;
    }
}
