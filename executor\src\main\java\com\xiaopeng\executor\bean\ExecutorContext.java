package com.xiaopeng.executor.bean;

import com.xiaopeng.executor.BaseTestExecutor;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.HashSet;
import java.util.Set;


public class ExecutorContext {

    private static final String TAG = "ExecutorContext";

    // 基础设施
    private final BaseTestExecutor executor;

    // 执行状态
    private final ExecutionState executionState;

    // 控制状态
    private volatile boolean isPaused = false;
    private volatile boolean isStopped = false;
    private final Object controlLock = new Object();

    // 执行跟踪状态
    private boolean hasProcedureStepExecuted = false;
    private boolean hasProcedureConditionalStepExecuted = false;
    private final Set<String> executedActions = new HashSet<>();
    private String currentPhaseType = null;
    private boolean hasProcedureConditionalStructure = false; // Procedure阶段是否有if-else结构
    private boolean isInConditionalBranch = false; // 当前是否在条件分支内
    private boolean isConditionAction = false; // 当前是否是条件判断Action

    /**
     * 构造函数
     *
     * @param executor 基础测试执行器
     */
    public ExecutorContext(BaseTestExecutor executor) {
        this.executor = executor;
        this.executionState = new ExecutionState();
    }


    /**
     * 获取基础测试执行器
     *
     * @return 基础测试执行器
     */
    public BaseTestExecutor getExecutor() {
        return executor;
    }

    /**
     * 获取执行处理器
     *
     * @return 执行处理器，可能为null
     */
    public BaseTestExecutor.ExecutionHandler getExecuteHandler() {
        return executor.getExecuteHandler();
    }


    /**
     * 获取当前执行状态
     *
     * @return 执行状态对象
     */
    public ExecutionState getExecutionState() {
        return executionState;
    }

    /**
     * 更新执行位置
     *
     * @param scriptLoopIndex 脚本循环索引
     * @param phaseIndex 阶段索引
     * @param sceneIndex 场景索引
     * @param sceneLoopIndex 场景循环索引
     * @param stepIndex 步骤索引
     */
    public void updateExecutionLocation(int scriptLoopIndex, int phaseIndex, int sceneIndex, int sceneLoopIndex, int stepIndex) {
        executionState.update(scriptLoopIndex, phaseIndex, sceneIndex, sceneLoopIndex, stepIndex);
        //FileLogger.i(TAG, "Execution location updated: " + executionState.getSummary());
    }

    /**
     * 重置执行状态
     */
    public void resetExecutionState() {
        executionState.reset();
        // 重置跟踪状态
        hasProcedureStepExecuted = false;
        hasProcedureConditionalStepExecuted = false;
        executedActions.clear();
        currentPhaseType = null;
        hasProcedureConditionalStructure = false;
        isInConditionalBranch = false;
        isConditionAction = false;
    }


    /**
     * 暂停执行
     */
    public void pauseExecution() {
        isPaused = true;
        FileLogger.i(TAG, "Execution paused");
    }

    /**
     * 恢复执行
     */
    public void resumeExecution() {
        synchronized (controlLock) {
            isPaused = false;
            controlLock.notifyAll();
        }
        FileLogger.i(TAG, "Execution resumed");
    }

    /**
     * 停止执行
     */
    public void stopExecution() {
        synchronized (controlLock) {
            isStopped = true;
            controlLock.notifyAll();
        }
        FileLogger.i(TAG, "Execution stopped");
    }

    /**
     * 检查是否已暂停
     *
     * @return true表示已暂停
     */
    public boolean isPaused() {
        return isPaused;
    }

    /**
     * 检查是否已停止
     *
     * @return true表示已停止
     */
    public boolean isStopped() {
        return isStopped;
    }

    /**
     * 重置控制状态
     */
    public void resetControlState() {
        synchronized (controlLock) {
            isPaused = false;
            isStopped = false;
        }
    }

    /**
     * 检查执行状态并处理暂停/停止
     *
     * 这个方法会阻塞当前线程直到恢复执行或抛出停止异常
     *
     * @throws ExecutionStoppedException 当执行被停止时抛出
     */
    public void checkExecutionState() throws ExecutionStoppedException {
        synchronized (controlLock) {
            // 处理暂停状态
            while (isPaused) {
                FileLogger.i(TAG, "Execution paused, waiting for resume...");
                try {
                    controlLock.wait();
                } catch (InterruptedException e) {
                    FileLogger.e(TAG, "Execution interrupted while paused: " + e.getMessage(), e);
                    Thread.currentThread().interrupt();
                    throw new ExecutionStoppedException("Execution interrupted");
                }
            }

            // 检查停止状态
            if (isStopped) {
                FileLogger.i(TAG, "Execution stopped");
                throw new ExecutionStoppedException("Execution manually stopped");
            }
        }
    }

    // ==================== 执行跟踪方法 ====================

    /**
     * 设置当前阶段类型
     *
     * @param phaseType 阶段类型（Precondition、Procedure、PostCondition）
     */
    public void setCurrentPhaseType(String phaseType) {
        this.currentPhaseType = phaseType;
    }

    /**
     * 获取当前阶段类型
     *
     * @return 当前阶段类型
     */
    public String getCurrentPhaseType() {
        return currentPhaseType;
    }

    /**
     * 设置当前是否在条件分支内
     *
     * @param inConditionalBranch 是否在条件分支内
     */
    public void setInConditionalBranch(boolean inConditionalBranch) {
        this.isInConditionalBranch = inConditionalBranch;
    }

    /**
     * 设置当前是否是条件判断Action
     *
     * @param conditionAction 是否是条件判断Action
     */
    public void setConditionAction(boolean conditionAction) {
        this.isConditionAction = conditionAction;
        // 只有在Procedure阶段的条件判断Action才标记为有条件结构
        if (conditionAction && "Procedure".equals(currentPhaseType)) {
            this.hasProcedureConditionalStructure = true;
        }
    }

    /**
     * 记录步骤执行
     *
     * @param action 执行的Action名称
     */
    public void recordStepExecution(String action) {
        // 只有Procedure阶段的非条件判断Action才计入执行统计
        if (action != null && !isConditionAction && "Procedure".equals(currentPhaseType)) {
            executedActions.add(action);
        }

        // 根据当前阶段类型判断是否在Procedure阶段
        // 条件判断Action也算Procedure阶段的执行
        if ("Procedure".equals(currentPhaseType)) {
            hasProcedureStepExecuted = true;
        }

        // 只有Procedure阶段分支内的非条件判断Action才算条件分支执行
        if (isInConditionalBranch && !isConditionAction && "Procedure".equals(currentPhaseType)) {
            hasProcedureConditionalStepExecuted = true;
        }
    }

    /**
     * 获取是否执行过Procedure阶段的step
     *
     * @return true表示执行过Procedure阶段的step
     */
    public boolean hasProcedureStepExecuted() {
        return hasProcedureStepExecuted;
    }

    /**
     * 获取Procedure阶段是否有条件结构
     *
     * @return true表示Procedure阶段有if-else条件结构
     */
    public boolean hasProcedureConditionalStructure() {
        return hasProcedureConditionalStructure;
    }

    /**
     * 获取Procedure阶段是否执行过条件分支内的step
     *
     * @return true表示Procedure阶段执行过条件分支内的step
     */
    public boolean hasProcedureConditionalStepExecuted() {
        return hasProcedureConditionalStepExecuted;
    }

    /**
     * 获取执行的Action集合
     *
     * @return 执行的Action集合
     */
    public Set<String> getExecutedActions() {
        return new HashSet<>(executedActions); // 返回副本，避免外部修改
    }

    /**
     * 获取执行统计信息（用于调试）
     *
     * @return 执行统计信息
     */
    public String getExecutionSummary() {
        return String.format("ExecutionSummary{hasProcedure=%s, hasProcedureConditionalStructure=%s, hasProcedureConditionalExecution=%s, actions=%s}",
                hasProcedureStepExecuted, hasProcedureConditionalStructure, hasProcedureConditionalStepExecuted, executedActions);
    }
}