package com.xiaopeng.executor.core;

import com.xiaopeng.executor.BaseTestExecutor;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.ExecutionState;
import com.xiaopeng.executor.bean.ExecutorContext;
import com.xiaopeng.xpautotest.community.test.TestStep;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureReasonDetail;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.SimpleFailureReasonInferrer;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.LogTagHelper;

/**
 * 步骤执行单元
 * 
 * 包装TestStep，保持原有的步骤执行逻辑：
 * 1. 执行状态检查（暂停/停止）
 * 2. 动作执行
 * 3. 结果处理和回调
 * 4. 异常处理
 * 5. 日志记录
 */
public class StepUnit implements ExecutableUnit {

    private static final String TAG = "StepUnit";
    private final TestStep step;

    public StepUnit(TestStep step) {
        this.step = step;
    }
    
    @Override
    public boolean execute(ExecutorContext context) {
        BaseTestExecutor executor = context.getExecutor();
        ExecutionState state = context.getExecutionState();

        // 1. 执行准备和记录开始
        long startTime = System.currentTimeMillis();
        LogTagHelper.logStepStart(step, state.getScriptLoopIndex(), state.getCurrentSceneIndex(), state.getSceneLoopIndex(), startTime);

        // 2. 记录步骤执行（在实际执行前记录）
        String actionName = step.getAction();
        context.recordStepExecution(actionName);

        // 3. 执行步骤动作
        TestResult result = runTestStep(executor, String.valueOf(startTime));

        // 4. 设置结果时间信息
        long endTime = System.currentTimeMillis();
        if (result != null) {
            result.setStartTime(startTime);
            result.setEndTime(endTime);
        }

        // 5. 处理和记录结果
        boolean success = result != null && result.isSuccess();
        String status = success ? "PASS" : "FAIL";
        LogTagHelper.logStepEnd(step, status, state.getScriptLoopIndex(), state.getCurrentSceneIndex(), state.getSceneLoopIndex(), endTime);
        FileLogger.i(TAG, String.format("loop %d|scene-%d %d|step-%s|%s",
                state.getScriptLoopIndex(), state.getCurrentSceneIndex(), state.getSceneLoopIndex(), step, status));
        reportStepResult(executor, result, success);

        // 6. 检查执行状态（暂停/停止）
        context.checkExecutionState();

        return success;
    }

    /**
     * 运行测试步骤 - StepUnit的核心执行逻辑
     *
     * @param executor 基础测试执行器
     * @param timeStampString 时间戳字符串
     * @return 测试结果
     */
    private TestResult runTestStep(BaseTestExecutor executor, String timeStampString) {
        TestResult result = null;
        try {
            String actionName = step.getAction();
            ActionContext context = new ActionContext(step.getScriptId(), step.getStepId(), timeStampString, step.getParamArray());
            result = executor.getSmartActionExecutor().execute(actionName.toLowerCase(), context);
            if (result == null) {
                FailureReasonDetail failureReason = new FailureReasonDetail(
                    FailureCode.AB001,
                    "Executor returned null result for action: " + actionName,
                    null);
                result = TestResult.failure("Executor returned null result for action: " + actionName, failureReason);
            }
        } catch (ActionException e) {
            // 处理ActionException，优先使用自带的失败原因
            FileLogger.e(TAG, "ActionException in step " + step.getAction() + ": " + e.getMessage(), e);
            FailureReasonDetail failureReason;
            if (e.hasFailureReason()) {
                failureReason = e.getFailureReason();
            } else {
                // 兜底使用智能推断
                failureReason = new SimpleFailureReasonInferrer().inferFailureReason(
                    step.getAction(), e.getMessage(), e);
            }
            result = TestResult.failure("Action failed: " + e.getMessage(), failureReason);

        } catch (Exception e) {
            // 处理其他异常
            FileLogger.e(TAG, "Executor failed in step " + step.getAction() + ": " + e.getMessage(), e);
            FailureReasonDetail failureReason = new FailureReasonDetail(
                FailureCode.AB001,
                e.getMessage(),
                e);
            result = TestResult.failure("Executor failed in step " + step.getAction() + ": " + e.getMessage(), failureReason);
        }
        return result;
    }

    /**
     * 上报Step执行结果
     */
    private void reportStepResult(BaseTestExecutor executor, TestResult result, boolean success) {
        if (executor.getExecuteHandler() != null) {
            try {
                if (success && step.isReport()) {
                    executor.getExecuteHandler().onStepSuccess(step, result);
                } else if (!success) {
                    executor.getExecuteHandler().onStepFailure(step, result);
                }
            } catch (Exception e) {
                // 捕获回调异常，避免影响主执行流程
                FileLogger.e(TAG, "Error in step result callback: " + e.getMessage(), e);
            }
        }
    }



    @Override
    public String getUnitId() {
        return "StepUnit[" + step.getAction() + "]";
    }

    public TestStep getStep() {
        return step;
    }
}
