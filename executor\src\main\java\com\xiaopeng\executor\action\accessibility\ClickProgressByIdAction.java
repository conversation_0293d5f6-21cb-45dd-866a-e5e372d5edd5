package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.accessibility.AccessibilityHelper;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.Locale;

public class ClickProgressByIdAction extends BaseAction {
    private static final String TAG = "ClickProgressByIdAction";

    /**
     * 执行通过点击设置进度条的操作
     * @param context 包含资源ID和进度值的上下文
     * @return TestResult
     */
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String resourceId = context.getStringParam().trim();
        float percentageValue = context.getFloatParam();
        if(Float.isNaN(percentageValue) || percentageValue < 0.0f || percentageValue > 100.0f) {
            String errorMsg = String.format(Locale.ROOT, "Invalid percentage value: %.2f. It must be between 0.0 and 100.0.", percentageValue);
            FileLogger.e(TAG, errorMsg);
            throw new ActionException(errorMsg, FailureCode.SI001);
        }
        
        boolean success = this.service.setProgressByGesture( resourceId, percentageValue, AccessibilityHelper.GESTURE_TYPE_CLICK);

        if (success) {
            String message = String.format(Locale.ROOT,"Successfully set progress (by click) for resource ID '%s' to %.2f%%.", resourceId, percentageValue);
            FileLogger.i(TAG, message);
            return TestResult.success(message);
        } else {
            String message = String.format(Locale.ROOT,"Failed to set progress (by click) for resource ID '%s' at %.2f%%.", resourceId, percentageValue);
            FileLogger.i(TAG, message);
            return TestResult.failure(message);
        }
    }
}