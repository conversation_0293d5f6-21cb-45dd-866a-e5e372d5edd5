package com.xiaopeng.xpautotest.utils;

import android.net.Uri;
import android.os.RemoteException;

import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.xiaopeng.lib.apirouter.ApiRouter;
import com.xiaopeng.xpautotest.bean.ApiRouterResponse;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.community.utils.Log;

public class ApiRouterUtils {

    public static final String TAG = ApiRouterUtils.class.getSimpleName();

    /**
     * 是否处在EOL模式
     *
     * @throws Exception
     */
    public static boolean isFactoryMode() {
        try {
            Uri targetUrl = new Uri.Builder()
                    .authority("com.xiaopeng.diagnostic.DiagnoseService")
                    .path("getFactoryMode")
    //                .appendQueryParameter("bundle", data)
                    .build();

            String result = ApiRouter.route(targetUrl);
            Log.i(TAG, "isFactoryMode result: " + result);
            // 返回值格式：{"code":0,"message":"success","mode":0}
            ApiRouterResponse response = GsonUtils.fromJson(result, new TypeToken<ApiRouterResponse>(){}.getType());
            // mode为1表示工厂模式
            return response.getMode() == 1;
        } catch (RemoteException e) {
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}