package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.Scanner;

public class SwipeAction extends BaseAction {
    private static final String TAG = "SwipeAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String text = (String) context.getAllParams();
        if (text == null) {
            throw new ActionException("coordinate is null!", FailureCode.SI001);
        }

        boolean result;
        FileLogger.i(TAG, "params: " + text);
        try (Scanner scanner = new Scanner(text).useDelimiter("[^0-9]+")) {
            int startX = scanner.nextInt();
            int startY = scanner.nextInt();
            int endX = scanner.nextInt();
            int endY = scanner.nextInt();
            int duration = scanner.nextInt();
            result = this.service.swipe(startX, startY, endX, endY, duration);
        } catch (Exception e) {
            FileLogger.e(TAG, e.getMessage());
            throw new ActionException("SwipeAction: " + e.getMessage(), FailureCode.AB001, e);
        }

        if (!result) {
            return TestResult.failure("SwipeAction Failed.");
        }
        return TestResult.success("SwipeAction Success.");
    }
}
