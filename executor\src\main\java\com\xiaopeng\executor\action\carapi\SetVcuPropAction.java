package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IVcuController;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SetVcuPropAction extends BaseAction {
    private static final String TAG = "SetVcuPropAction";
    IVcuController mIVcuController = null;

    @Override
    public void init(CarApiConfig config) {
        super.init(config);
        initControllers();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String key = (String) context.getStringParam();
        String value = (String) context.getStringParam();
        if (key == null || value == null) {
            throw new ActionException("key or value is null!", FailureCode.SI001);
        }
        boolean res = setVcuProp(key, value);
        FileLogger.i(TAG, "key: " + key + ", value: " + value + ", res: " + res);
        if (res) {
            return TestResult.success("Vcu set property success: " + key);
        } else {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB005);
            return TestResult.failure("Vcu set property failed: " + key);
        }
    }

    public boolean setVcuProp(String key, String value) {
        return mIVcuController.setValue(key, value);
    }

    private void initControllers() {
        mIVcuController = (IVcuController) carClientWrapper.getController(CarClientWrapper.XP_VCU_SERVICE);
    }
}
