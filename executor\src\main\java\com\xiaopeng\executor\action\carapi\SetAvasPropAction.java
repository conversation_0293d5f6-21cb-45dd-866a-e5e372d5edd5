package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IAvasController;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SetAvasPropAction extends BaseAction {
    private static final String TAG = "SetAvasPropAction";
    IAvasController mIAvasController = null;

    @Override
    public void init(CarApiConfig config) {
        super.init(config);
        initControllers();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String key = (String) context.getStringParam();
        String value = (String) context.getStringParam();
        if (key == null || value == null) {
            throw new ActionException("key or value is null!", FailureCode.SI001);
        }
        boolean res = setAvasProp(key, value);
        FileLogger.i(TAG, "key: " + key + ", value: " + value + ", res: " + res);
        if (res) {
            return TestResult.success("AVAS set property success: " + key);
        } else {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB005);
            return TestResult.failure("AVAS set property failed: " + key);
        }
    }

    public boolean setAvasProp(String key, String value) {
        return mIAvasController.setValue(key, value);
    }

    private void initControllers() {
        mIAvasController = (IAvasController) carClientWrapper.getController(CarClientWrapper.XP_AVAS_SERVICE);
    }
}
