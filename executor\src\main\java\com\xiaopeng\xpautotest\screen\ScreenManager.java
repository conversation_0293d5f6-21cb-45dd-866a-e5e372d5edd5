package com.xiaopeng.xpautotest.screen;


import android.content.Context;
import android.hardware.display.DisplayManager;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.accessibility.AccessibilityWindowInfo;
import android.view.accessibility.AccessibilityNodeInfo;
import java.util.List;
import com.xiaopeng.screen.ScreenDevice;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.utils.DisplayUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

/**
 * 屏幕管理器，负责屏幕切换和屏幕值解析
 * 提供统一的屏幕管理接口，解耦变量设置和屏幕操作
 */
public class ScreenManager {
    private static final String TAG = "ScreenManager";
    private static ScreenManager instance;

    // 当前屏幕ID，默认为主屏(0)
    private int currentScreenId = 0;
    private String currentScreenName = "ivi"; //

    // 屏幕尺寸信息
    private int screenWidth = 0;
    private int screenHeight = 0;

    // 缓存相关字段
    private long cachedPhysicalDisplayId = -1L; // 缓存的物理显示屏ID，-1表示未初始化
    private int lastUpdatedScreenId = -1; // 上次更新屏幕尺寸时的屏幕ID，用于避免重复更新

    private Context context;

    private ScreenManager() {
    }
    
    public static synchronized ScreenManager getInstance() {
        if (instance == null) {
            instance = new ScreenManager();
        }
        return instance;
    }

    /**
     * 初始化ScreenManager
     * @param context 应用上下文
     */
    public void initialize(Context context) {
        this.context = context;
    }
    
    /**
     * 设置屏幕（支持字符串名称和数字ID），返回操作结果
     * @param screenValue 屏幕值（如：main, ivi, psg, rear, 0, 1, 2, 3等）
     * @return ScreenSwitchResult 包含成功状态和详细信息
     */
    public ScreenSwitchResult setScreen(String screenValue) {
        if (screenValue == null || screenValue.trim().isEmpty()) {
            return new ScreenSwitchResult(false, "Screen value is null or empty");
        }
        
        int targetScreenId = parseScreenValue(screenValue.trim());
        if (targetScreenId == -1) {
            return new ScreenSwitchResult(false, "Invalid screen value: " + screenValue);
        }
        
        try {
            // 更新内部的currentScreenId
            this.currentScreenId = targetScreenId;
            this.currentScreenName = ScreenDevice.getUniqueName(targetScreenId);
            // 更新屏幕尺寸
            boolean sizeUpdateSuccess = updateScreenSize(targetScreenId);
            if (!sizeUpdateSuccess) {
                return new ScreenSwitchResult(false,
                    "Failed to update screen size for screenId: " + targetScreenId +
                    ". Display with ID " + targetScreenId + " not found or not available.");
            }

            return new ScreenSwitchResult(true,
                "Screen switched successfully to: " + screenValue + " (screenId: " + targetScreenId + ")");
        } catch (Exception e) {
            return new ScreenSwitchResult(false,
                "Failed to switch screen: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前屏幕ID
     * @return 当前屏幕ID
     */
    public int getCurrentScreenId() {
        return currentScreenId;
    }

    /**
     * 获取屏幕宽度
     * @return 屏幕宽度
     */
    public int getScreenWidth() {
        return screenWidth;
    }

    /**
     * 获取屏幕高度
     * @return 屏幕高度
     */
    public int getScreenHeight() {
        return screenHeight;
    }

    /**
     * 更新屏幕尺寸 - 支持多屏操作
     * @param screenId 屏幕ID
     * @return 是否成功更新屏幕尺寸
     */
    public boolean updateScreenSize(int screenId) {
        // 如果screenId与上次更新的相同，且已有有效的屏幕尺寸，则跳过更新
        if (lastUpdatedScreenId == screenId && screenWidth > 0 && screenHeight > 0) {
            FileLogger.d(TAG, "Screen size already cached for screenId " + screenId + ": " +
                        screenWidth + "x" + screenHeight);
            return true;
        }

        if (context == null) {
            FileLogger.e(TAG, "Context is null, cannot update screen size");
            return false;
        }

        DisplayMetrics metrics = new DisplayMetrics();
        DisplayManager displayManager = (DisplayManager) context.getSystemService(Context.DISPLAY_SERVICE);
        if (displayManager == null) {
            FileLogger.e(TAG, "DisplayManager is null, cannot update screen size");
            return false;
        }

        Display display = displayManager.getDisplay(screenId);
        if (display == null) {
            FileLogger.e(TAG, "Display with ID " + screenId + " not found, cannot update screen size");
            FailureContextHolder.setFailureIfNotSet(FailureCode.SI003);
            return false;
        }
        display.getRealMetrics(metrics);

        // 更新屏幕尺寸
        this.screenWidth = metrics.widthPixels;
        this.screenHeight = metrics.heightPixels;
        lastUpdatedScreenId = screenId;

        // 同时缓存物理displayId,截图时直接使用
        cachedPhysicalDisplayId = DisplayUtils.getPhysicalDisplayId(display);
        FileLogger.i(TAG, "Screen size updated: " + screenWidth + "x" + screenHeight +
                    " for screenId: " + screenId + " (physical: " + cachedPhysicalDisplayId + ")");
        return true;
    }

    /**
     * 获取缓存的物理显示屏ID
     * @return 物理显示屏ID
     */
    public long getCachedPhysicalDisplayId() {
        return cachedPhysicalDisplayId;
    }

    /**
     * 获取当前屏幕的名称
     * @return 屏幕名称
     */
    public String getCurrentScreenName() {
        return currentScreenName;
    }

    /**
     * 解析屏幕值，支持字符串名称和数字ID
     * @param screenValue 屏幕值（如：main, ivi, psg, 0, 1, 2, 3等）
     * @return 屏幕ID，如果无效则返回-1
     */
    public static int parseScreenValue(String screenValue) {
        String value = screenValue.toLowerCase();
        
        // 首先尝试解析为数字
        try {
            int numericValue = Integer.parseInt(value);
            if (numericValue >= 0 ) {
                return numericValue;
            } else {
                FileLogger.w(TAG, "Invalid screen ID: " + numericValue + ". Screen ID must be non-negative.");
                return -1;
            }
        } catch (NumberFormatException e) {
            // 不是数字，继续尝试字符串映射
        }

        try {
            return ScreenDevice.getScreenId(screenValue);
        } catch (Exception e) {
            FileLogger.e(TAG, "Unknown screen name: " + screenValue + ", and ScreenDevice.getScreenId() failed", e);
            return -1;
        }
    }

    /**
     * 屏幕切换结果类
     */
    public static class ScreenSwitchResult {
        public final boolean success;
        public final String message;
        
        public ScreenSwitchResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        @Override
        public String toString() {
            return "ScreenSwitchResult{success=" + success + ", message='" + message + "'}";
        }
    }

}
