package com.xiaopeng.xpautotest.community.test;

import java.io.IOException;
import java.util.ArrayList;

public class TestScene {
    private String phaseType = ""; // 阶段类型，可能是"Precondition", "Procedure", "PostCondition"
    private long scriptId;
    // 保存步骤列表
    private ArrayList<TestStep> stepList = new ArrayList<>();
    private int loopTime = 1;
    private boolean isTryMode = false;
    private String currentComment = "";
    private String currentKeywords = ""; // 当前步骤的关键字
    public TestScene(Long scriptId, String line) {
        this.scriptId = scriptId;
        String[] arr = line.replace(":", "").split(" +");
//        System.out.println(arr);
        if (arr.length > 1) {
            if (!"".equals(arr[1]))
                loopTime = Integer.parseInt(arr[1]);
        }
        if (arr.length > 2) {
            if ("-try".equals(arr[2])) {
                isTryMode = true;
            }
        }
    }

    public void addStep(int stepId, String stepLine) throws IOException {
        if (stepLine.contains("#")) {
            // 解析当前行的注释和关键字（会覆盖之前的状态，实现优先级处理）
            parseCommentLine(stepLine);

            // 跳过纯注释行，注释和关键字信息不会被消费掉，会带到下一行
            if (stepLine.trim().startsWith("#")) {
                //consumeKeyword();
                return;
            }

            // 提取命令部分（第一个#之前的内容）
            stepLine = stepLine.split("#")[0].trim();
        }

        // 创建测试步骤，使用当前状态并清空
        stepList.add(new TestStep(scriptId, stepId, stepLine,
            consumeComment(), consumeKeyword(), phaseType));
    }

    /**
     * 解析注释行，提取注释和关键字
     *
     * 优先级规则：
     * - 当前行的注释和关键字会覆盖上一行设置的状态
     * - 支持跨行注释：纯注释行的内容会保留到下一行使用
     */
    private void parseCommentLine(String stepLine) {
        String[] parts = stepLine.split("#");
        if (parts.length <= 1) return;

        // 第二部分作为注释（如果不是关键字）
        // 优先级处理：当前行注释会覆盖上一行设置的注释
        String secondPart = parts[1].trim();
        if (!secondPart.startsWith("AW-")) {
            setCurrentComment(secondPart);
        }

        // 最后一部分作为关键字（如果以AW-开头）
        // 优先级处理：当前行关键字会覆盖上一行设置的关键字
        String lastPart = parts[parts.length - 1].trim();
        if (lastPart.startsWith("AW-")) {
            setCurrentKeywords(lastPart);
        }
    }

    /**
     * 消费注释状态（获取并清空）
     */
    private String consumeComment() {
        if (currentComment.isEmpty()) {
            return "";
        }
        String comment = currentComment;
        currentComment = "";
        return comment;
    }

    /**
     * 消费关键字状态（获取并清空）
     */
    private String consumeKeyword() {
        String keyword = currentKeywords;
        currentKeywords = "";
        return keyword;
    }

    public void setCurrentComment(String comment) {
        currentComment = comment.replace("#", "").trim();
    }

    public void setCurrentKeywords(String keywords) {
        currentKeywords = keywords.replace("#", "").trim();
}

    public int getLoopTime() {
        return loopTime;
    }

    public boolean isTryMode() {
        return isTryMode;
    }

    public ArrayList<TestStep> getStepList() {
        return stepList;
    }

    public void setStepList(ArrayList<TestStep> stepList) {
        this.stepList = stepList;
    }

    public String toString() {
        return loopTime + "|" + stepList;
    }

    public String getPhaseType() {
        return phaseType;
    }

    public void setPhaseType(String phaseType) {
        this.phaseType = phaseType;
    }

    public long getScriptId() {
        return scriptId;
    }

    // 兼容性方法，保持向后兼容
    @Deprecated
    public String getExecuteType() {
        return phaseType;
    }

    @Deprecated
    public void setExecuteType(String executeType) {
        this.phaseType = executeType;
    }
}
