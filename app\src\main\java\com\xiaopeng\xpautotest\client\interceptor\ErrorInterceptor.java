package com.xiaopeng.xpautotest.client.interceptor;

import com.xiaopeng.xpautotest.client.api.ApiException;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class ErrorInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = chain.proceed(request);

        if (!response.isSuccessful()) {
            throw new ApiException(response.code(), response.message());
        }

        return response;
    }
}
