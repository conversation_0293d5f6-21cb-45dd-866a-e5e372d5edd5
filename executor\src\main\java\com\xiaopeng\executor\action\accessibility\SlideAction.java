package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SlideAction extends BaseAction {
    private static final String TAG = "SlideAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        int x = (int) context.getIntParam();
        if (x < 0) {
            throw new ActionException("x is null!", FailureCode.SI001);
        }
        int y = (int) context.getIntParam();
        if (y < 0) {
            throw new ActionException("y is null!",FailureCode.SI001);
        }
        FileLogger.i(TAG, "x: " + x + ", y: " + y);
        boolean result = this.service.click(x, y);
        if (!result) {
            return TestResult.failure("SlideAction Failed.");
        }
        return TestResult.success("SlideAction Success.");
    }
}
