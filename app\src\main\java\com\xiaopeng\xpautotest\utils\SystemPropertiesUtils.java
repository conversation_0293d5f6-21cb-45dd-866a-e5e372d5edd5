package com.xiaopeng.xpautotest.utils;

import android.os.SystemProperties;

import com.xiaopeng.lib.utils.SystemPropertyUtil;

public class SystemPropertiesUtils {

    // XMARTQ2ZHD21B1_V1.0.0.999_20200325021210.6_REV01_UserDebug_DEV
    private static final String SYSTEM_SOFTWARE_VERSION = "ro.xiaopeng.software";

    // on/off
    private static final String SYSTEM_REPAIR_MODE = "persist.sys.xiaopeng.repairmode";
    private static final String SYSTEM_PROPERTY_FACTORY_MODE = "persist.sys.xiaopeng.factory_mode";

    /**
     * OTA 状态
     */
    public static final String SYSTEM_PROPERTY_OTA_STATE = "sys.xiaopeng.ota.state";
    // ota upgrade state
    public static final int PROPERTY_OTA_STATE_IDLE = 0;
    public static final int PROPERTY_OTA_STATE_UPGRADING = 1;

    private static final String SYSTEM_PROPERTY_OTA_CAMPAIGN_STATE = "persist.sys.xiaopeng.ota_campaign_state";
    private static final int OTA_CAMPAIGN_RUNNING = 1;

    private static final String ON = "on";
    private static final String OFF = "off";

    private static final int INVALID = 0;
    private static final int NEGATIVE = -1;

    public static String getSystemSoftwareVersion() {
        return SystemProperties.get(SYSTEM_SOFTWARE_VERSION, "");
    }

    public static String getSoftwareVersionCode() {
        String sid = SystemProperties.get("ro.build.display.id", "");
        if (sid != null && sid.contains("_")) {
            String[] split = sid.split("_");
            if (split.length > 1) {
                return split[1];
            }
        }
        return "";
    }

    public static boolean isRepairMode() {
        return ON.equalsIgnoreCase(getRepairMode());
    }

    public static boolean isRepairMode(String mode) {
        return ON.equalsIgnoreCase(mode);
    }

    public static boolean isRepairModeOff(String mode) {
        return OFF.equalsIgnoreCase(mode);
    }

    public static final String getRepairMode() {
        return SystemProperties.get(SYSTEM_REPAIR_MODE, "");
    }

    public static int getOtaState() {
        return SystemProperties.getInt(SYSTEM_PROPERTY_OTA_STATE, NEGATIVE);
    }

    public static boolean isCampaignRunningState() {
        return OTA_CAMPAIGN_RUNNING == SystemPropertiesUtils.getOtaCampaignState();
    }

    private static int getOtaCampaignState() {
        return SystemProperties.getInt(SYSTEM_PROPERTY_OTA_CAMPAIGN_STATE, NEGATIVE);
    }

    public static String getVIN() {
        String vin = SystemPropertyUtil.getVIN();
//        if (vin == null || vin.isEmpty()) {
//            vin = "L1NSPGKB0SB014447";
//        }
        return vin;
    }

    public static String getSID() {
        return SystemProperties.get("ro.build.display.id", "");
    }

}
