package com.xiaopeng.xpautotest.manager;

import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.client.api.ApiResponse;
import com.xiaopeng.xpautotest.client.api.VehicleApiImpl;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.utils.SystemPropertiesUtils;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import androidx.core.util.Supplier;

public class HeartbeatManager {
    private static final String TAG = "HeartbeatManager";
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private long delay = 30; // Initial delay in seconds
    private final long maxDelay = 300; // Maximum delay in seconds
    private final long minDelay = 5; // Minimum delay in seconds

    public void startHeartbeat(Supplier<Boolean> performHeartbeat) {
        scheduler.schedule(() -> sendHeartbeat(performHeartbeat), delay, TimeUnit.SECONDS);
    }

    private void sendHeartbeat(Supplier<Boolean> performHeartbeat) {
        try {
            // Simulate heartbeat logic
            boolean success = performHeartbeat.get();

            if (success) {
                delay = minDelay; // Reset delay on success
            } else {
                delay = Math.min(delay * 2, maxDelay); // Exponential backoff
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "sendHeartbeat: Exception occurred - " + e.getMessage());
            delay = Math.min(delay * 2, maxDelay); // Exponential backoff on failure
        } finally {
            scheduler.schedule(() -> sendHeartbeat(performHeartbeat), delay, TimeUnit.SECONDS);
        }
    }

    public void stopHeartbeat() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            FileLogger.e(TAG, "stopHeartbeat: Interrupted during shutdown - " + e.getMessage());
            scheduler.shutdownNow();
        }
    }
}
