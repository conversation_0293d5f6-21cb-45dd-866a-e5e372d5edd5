package com.xiaopeng.xpautotest.community.test;

public class FailureReasonDetail {
    private FailureCode errorCode;        // 错误码
    private String errorMessage;          // 详细错误信息
    private String originalMessage;       // 原始错误消息
    private String exception;             // 原始异常对象的字符串表示

    public FailureReasonDetail(FailureCode errorCode, String originalMessage, Throwable exception) {
        this.errorCode = errorCode;
        this.errorMessage = errorCode.getDescription();
        this.originalMessage = originalMessage;
        this.exception = null;
    }
    
    public FailureCode getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(FailureCode errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getOriginalMessage() {
        return originalMessage;
    }

    public void setOriginalMessage(String originalMessage) {
        this.originalMessage = originalMessage;
    }

    public String getException() {
        return exception;
    }
    
    public void setException(String exception) {
        this.exception = exception;
    }
}
