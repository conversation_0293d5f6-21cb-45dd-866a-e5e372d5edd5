package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.utils.ValueCompareUtils;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class CheckIsNumberByIDAction extends BaseAction {
    private static final String TAG = "CheckIsNumberByIDAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String sourceId = (String) context.getStringParam();
        if (sourceId == null) {
            throw new ActionException("sourceId or expectedText is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "resourceId: " + sourceId);
        boolean result = ValueCompareUtils.isNumber(this.service.getTextById(sourceId));
        if (!result) {
            return TestResult.failure("Failed to getText by id: " + sourceId);
        }
        return TestResult.success("GetText by id: " + sourceId + " successfully.");
    }
}