package com.xiaopeng.executor.core;

import com.xiaopeng.executor.bean.VariableContext;
import com.xiaopeng.executor.bean.VariableException;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 变量解析器
 * 
 * 负责识别和解析脚本中的变量引用：
 * 1. 变量引用识别：识别$VARIABLE_NAME格式的变量引用
 * 2. 变量替换：将变量引用替换为实际值
 * 3. 参数处理：处理包含变量引用的参数数组
 * 4. 错误处理：处理未定义变量的引用错误
 */
public class VariableResolver {
    
    private static final String TAG = "VariableResolver";
    
    // 变量引用的正则表达式：$VARIABLE_NAME
    // 变量名支持字母、数字、下划线，必须以字母或下划线开头
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$([a-zA-Z_][a-zA-Z0-9_]*)");
    
    // 简单的变量引用检查
    private static final String VARIABLE_PREFIX = "$";
    
    /**
     * 检查字符串是否包含变量引用
     * 
     * @param text 要检查的文本
     * @return true表示包含变量引用
     */
    public static boolean containsVariableReference(String text) {
        return text != null && text.contains(VARIABLE_PREFIX);
    }
    
    /**
     * 检查参数数组是否包含变量引用
     * 
     * @param params 参数数组
     * @return true表示至少有一个参数包含变量引用
     */
    public static boolean containsVariableReference(String[] params) {
        if (params == null || params.length == 0) {
            return false;
        }
        
        for (String param : params) {
            if (containsVariableReference(param)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 从变量引用中提取变量名
     * 
     * @param variableRef 变量引用（如$VAR_NAME）
     * @return 变量名（如VAR_NAME），如果不是有效的变量引用返回null
     */
    public static String extractVariableName(String variableRef) {
        if (variableRef == null || !variableRef.startsWith(VARIABLE_PREFIX)) {
            return null;
        }
        
        Matcher matcher = VARIABLE_PATTERN.matcher(variableRef);
        if (matcher.matches()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * 解析单个参数中的变量引用
     * 
     * @param param 参数值
     * @param context 变量上下文
     * @return 解析后的参数值
     * @throws VariableException 当变量未定义时抛出异常
     */
    public static String resolveVariable(String param, VariableContext context) throws VariableException {
        if (param == null || context == null) {
            return param;
        }
        
        if (!containsVariableReference(param)) {
            return param;
        }
        
        //FileLogger.i(TAG, "Resolving variables in: " + param);
        
        String result = param;
        Matcher matcher = VARIABLE_PATTERN.matcher(param);
        
        while (matcher.find()) {
            String fullMatch = matcher.group(0);  // $VARIABLE_NAME
            String varName = matcher.group(1);    // VARIABLE_NAME
            
            if (!context.hasVariable(varName)) {
                throw new VariableException("Variable not defined: " + varName);
            }
            
            String varValue = context.getValue(varName);
            if (varValue == null) {
                varValue = "";  // 将null值转换为空字符串
            }

            result = result.replace(fullMatch, varValue);
            FileLogger.i(TAG, "Variable resolved: " + fullMatch + " = " + varValue);
        }
        
        //FileLogger.i(TAG, "Final resolved value: " + param + " -> " + result);
        return result;
    }
    
    /**
     * 解析参数数组中的变量引用
     * 
     * @param params 原始参数数组
     * @param context 变量上下文
     * @return 解析后的参数数组
     * @throws VariableException 当变量未定义时抛出异常
     */
    public static String[] resolveVariables(String[] params, VariableContext context) throws VariableException {
        if (params == null || params.length == 0) {
            return params;
        }
        
        if (context == null) {
            FileLogger.w(TAG, "VariableContext is null, returning original parameters");
            return params;
        }
        
        // 检查是否需要解析
        if (!containsVariableReference(params)) {
            return params;
        }

        String[] resolved = new String[params.length];
        for (int i = 0; i < params.length; i++) {
            resolved[i] = resolveVariable(params[i], context);
        }
        
        return resolved;
    }
    
    /**
     * 验证变量名是否有效
     * 
     * @param varName 变量名
     * @return true表示变量名有效
     */
    public static boolean isValidVariableName(String varName) {
        if (varName == null || varName.trim().isEmpty()) {
            return false;
        }
        
        // 变量名必须以字母或下划线开头，后面可以跟字母、数字、下划线
        return varName.matches("[a-zA-Z_][a-zA-Z0-9_]*");
    }
}
