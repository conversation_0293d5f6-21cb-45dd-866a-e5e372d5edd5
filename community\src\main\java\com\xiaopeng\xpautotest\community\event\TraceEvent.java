package com.xiaopeng.xpautotest.community.event;

import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class TraceEvent {
    private static final String TAG = "TraceEvent";

    public static class StartEvent {
    }

    public static class EndEvent {
    }

    public static class BaseEvent {
        private final Long scriptId;
        private final int stepId;
        private final int traceId;
        private final String src;
        private final String expectedValue;
        private boolean finished;
        private boolean found;

        public BaseEvent(Long scriptId, int stepId, int traceId, String src, String expectedValue) {
            this.scriptId = scriptId;
            this.stepId = stepId;
            this.traceId = traceId;
            this.src = src;
            this.expectedValue = expectedValue;
            this.finished = false;
            this.found = false;
        }

        public Long getScriptId() {
            return scriptId;
        }

        public int getStepId() {
            return stepId;
        }

        public int getTraceId() {
            return traceId;
        }

        public String getType() {
            return "";
        }

        public String getSrc() {
            return src;
        }

        public String getExpectedValue() {
            return expectedValue;
        }

        public boolean isFinished() {
            return finished;
        }

        public void setFinished(boolean finished) {
            this.finished = finished;
        }

        public boolean isFound() {
            return found;
        }

        public void setFound(boolean found) {
            this.found = found;
        }

        public boolean compareValue(double value) {
            try {
                if (!this.getExpectedValue().contains("~")) {
                    return Double.parseDouble(this.getExpectedValue()) == value;
                }
                if (this.getExpectedValue().startsWith("~")) {
                    return Double.parseDouble(this.getExpectedValue().substring(1)) > value;
                }
                if (this.getExpectedValue().endsWith("~")) {
                    return Double.parseDouble(this.getExpectedValue().substring(0, this.getExpectedValue().length() - 1)) < value;
                }
                String[] parts = this.getExpectedValue().split("~");
                if (parts.length == 2) {
                    double lowerBound = Double.parseDouble(parts[0]);
                    double upperBound = Double.parseDouble(parts[1]);
                    return value >= lowerBound && value <= upperBound;
                }
            } catch (Exception e) {
                FileLogger.e(TAG, this.getType() + " compareValue: " + this.getExpectedValue() + " " + value);
            }
            return false;
        }
    }

    public static class CanEvent extends BaseEvent {
        private final int channel;
        private final int messageID;
        private final int startBit;
        private final int length;
        private final double resolution;
        private final int offset;

        public CanEvent(Long scriptId, int stepId, int traceId, String src, String channel, String messageID, int startBit, int length, double resolution, int offset, String expectedValue) {
            super(scriptId, stepId, traceId, src, expectedValue);
            this.channel = Integer.parseInt(channel);
            this.messageID = Integer.parseInt(messageID.substring(2), 16);
            this.startBit = startBit;
            this.length = length;
            this.resolution = resolution;
            this.offset = offset;
        }

        public String getType() {
            return "can";
        }

        public int getChannel() {
            return channel;
        }

        public int getMessageID() {
            return messageID;
        }

        public int getStartBit() {
            return startBit;
        }

        public int getLength() {
            return length;
        }

        public double getResolution() {
            return resolution;
        }

        public int getOffset() {
            return offset;
        }
    }

    public static class LinEvent extends BaseEvent {
        private final int channel;
        private final int messageID;
        private final int startBit;
        private final int length;
        private final double resolution;
        private final int offset;

        public LinEvent(Long scriptId, int stepId, int traceId, String src, String channel, String messageID, int startBit, int length, double resolution, int offset, String expectedValue) {
            super(scriptId, stepId, traceId, src, expectedValue);
            this.channel = Integer.parseInt(channel.substring(2), 16);
            this.messageID = Integer.parseInt(messageID.substring(2), 16);
            this.startBit = startBit;
            this.length = length;
            this.resolution = resolution;
            this.offset = offset;
        }

        public String getType() {
            return "lin";
        }

        public int getChannel() {
            return channel;
        }

        public int getMessageID() {
            return messageID;
        }

        public int getStartBit() {
            return startBit;
        }

        public int getLength() {
            return length;
        }

        public double getResolution() {
            return resolution;
        }

        public int getOffset() {
            return offset;
        }
    }

    public static class SomeIPEvent extends BaseEvent {
        private final int nodeID;
        private final int serviceID;
        private final int elementID;
        private final int startByte;
        private final int length;
        private final int maxLength;

        public SomeIPEvent(Long scriptId, int stepId, int traceId, String src, String nodeID, String serviceID, String elementID, int startByte, int length, int maxLength, String expectedValue) {
            super(scriptId, stepId, traceId, src, expectedValue);
            this.nodeID = Integer.parseInt(nodeID.substring(2), 16);
            this.serviceID = Integer.parseInt(serviceID.substring(2), 16);
            this.elementID = Integer.parseInt(elementID.substring(2), 16);
            this.startByte = startByte;
            this.length = length;
            this.maxLength = maxLength;
        }

        public String getType() {
            return "someip";
        }

        public int getNodeID() {
            return nodeID;
        }

        public int getServiceID() {
            return serviceID;
        }

        public int getElementID() {
            return elementID;
        }

        public int getStartByte() {
            return startByte;
        }

        public int getLength() {
            return length;
        }

        public int getMaxLength() {
            return maxLength;
        }
    }

    public static class ResultEvent {
        private final Long scriptId;
        private final int stepId;
        private final int traceId;
        private final String type; // can, lin, someip
        private final String src; // 信号源
        private final double value; // 实际值
        private final String result; // OK, NA, NG, ERROR
        private final String message; // 详细信息

        public ResultEvent(Long scriptId, int stepId, int traceId, String type, String src, double value, String result, String message) {
            this.scriptId = scriptId;
            this.stepId = stepId;
            this.traceId = traceId;
            this.type = type;
            this.src = src;
            this.value = value;
            this.result = result;
            this.message = message;
        }

        public Long getScriptId() {
            return scriptId;
        }

        public int getStepId() {
            return stepId;
        }

        public int getTraceId() {
            return traceId;
        }

        public String getType() {
            return type;
        }

        public String getSrc() {
            return src;
        }

        public double getValue() {
            return value;
        }

        public String getResult() {
            return result;
        }

        public String getMessage() {
            return message;
        }

        public String toString() {
            return "ResultEvent{" +
                    "scriptId=" + scriptId +
                    ", stepId=" + stepId +
                    ", traceId=" + traceId +
                    ", type='" + type + '\'' +
                    ", src='" + src + '\'' +
                    ", value=" + value +
                    ", result='" + result + '\'' +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
}
