package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.executor.bean.IActionFactory;
import com.xiaopeng.executor.bean.TechType;

import java.util.HashMap;
import java.util.Map;

import androidx.core.util.Supplier;

public class AccessibilityFactory implements IActionFactory {
    private final AccessibilityConfig config;
    public AccessibilityFactory(AccessibilityConfig config) {
        this.config = config;
        registerActions();
    }

    private final Map<String, Supplier<IAction<AccessibilityConfig>>> actionMap = new HashMap<>();
    private final Map<String, IAction<?>> actionInstanceMap = new HashMap<>();

    private void registerActions() {
        register("delay", DelayAction::new);
        register("trace", TraceAction::new);
        register("vdt", VDTAction::new);
        register("shell", ShellAction::new);
        register("startPackage", StartPackageAction::new);
        register("startActivity", StartActivityAction::new);
        register("Into", StartActivityAction::new);
        register("Click", ClickAction::new);
        register("AdbClick", AdbClickAction::new);
        register("ClickByText", ClickByTextAction::new);
        register("ClickByTextNew", ClickByTextNewAction::new);
        register("ClickByTextContains", ClickByTextContainsAction::new);
        register("ClickById", ClickByIdAction::new);
        register("ClickByRPath", ClickByRPathAction::new);
        register("ClickByIcon", ClickByIconAction::new);
        register("CheckTextById", CheckTextByIDAction::new);
        register("CheckIsNumberById", CheckIsNumberByIDAction::new);
        register("CheckTextNotEqualById", CheckTextNotEqualByIDAction::new);
        register("WaitId", WaitIdAction::new);
        register("WaitStr", WaitTextAction::new);
        register("WaitStrContains", WaitTextContainsAction::new);
        register("WaitIcon", WaitIconAction::new);
        register("InputText", InputTextAction::new);
        register("Swipe", SwipeAction::new);
        register("Slide", SwipeAction::new);
        register("BackToHome", BackToHomeAction::new);
        register("ScreenCap", ScreenCapAction::new);
        register("CheckLogcatContains", CheckLogcatContainsAction::new);
        register("UIDump", UIDumpAction::new);
        register("SendBroadcast", SendBroadcastAction::new);
        register("SpeechByText", SpeechByTextAction::new);
        register("ScrollTo", ScrollToAction::new);
        register("ClickProgressById", ClickProgressByIdAction::new);
        register("DragProgressById", DragProgressByIdAction::new);
        register("LongClick", LongClickAction::new);
        register("SetTemperatureById", SetTemperatureByIdAction::new);
        register("CheckToast", CheckToastAction::new);
        register("CheckSwitchById", CheckSwitchByIdAction::new);
    }

    private void register(String actionName, Supplier<IAction<AccessibilityConfig>> supplier) {
        actionMap.put(actionName.toLowerCase(), supplier);
    }

    @Override
    public IAction<?> createAction(String actionName) {
        Supplier<IAction<AccessibilityConfig>> supplier = actionMap.get(actionName.toLowerCase());
        if (supplier == null) throw new IllegalArgumentException("Unknown action. actionName: " + actionName);
        IAction<AccessibilityConfig> action = supplier.get();
        action.init(config); // 注入配置
        return action;
    }

    public IAction<?> getAction(String actionName) {
        actionName = actionName.toLowerCase();
        if (actionInstanceMap.containsKey(actionName)) {
            return actionInstanceMap.get(actionName);
        } else {
            IAction<?> action = createAction(actionName);
            actionInstanceMap.put(actionName, action);

            return action;
        }
    }

    public boolean containsAction(String actionName) {
        return actionMap.containsKey(actionName.toLowerCase());
    }

    @Override
    public boolean supports(TechType techType) {
        return techType == TechType.ACCESSIBILITY_SERVICE;
    }
}
