package com.xiaopeng.executor.action;

import com.xiaopeng.executor.bean.ActionConfig;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.executor.bean.IActionFactory;
import com.xiaopeng.executor.bean.TechType;
import com.xiaopeng.executor.bean.VariableContext;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;

import java.util.HashMap;
import java.util.Map;

public class SmartActionExecutor {

    private static SmartActionExecutor instance;
    private final Map<TechType, IActionFactory> factories = new HashMap<>();

    // 当前执行上下文的变量上下文（线程局部变量）
    private static final ThreadLocal<VariableContext> currentVariableContext = new ThreadLocal<>();

    public void registerFactory(TechType type, IActionFactory factory) {
        factories.put(type, factory);
    }

    /**
     * 获取指定技术类型的工厂
     *
     * @param techType 技术类型
     * @return 对应的工厂实例，如果不存在返回null
     */
    public IActionFactory getFactory(TechType techType) {
        return factories.get(techType);
    }

    public static synchronized SmartActionExecutor getInstance() {
        if (instance == null) {
            instance = new SmartActionExecutor();
        }
        return instance;
    }

    /**
     * 设置当前线程的变量上下文
     *
     * @param variableContext 变量上下文
     */
    public static void setCurrentVariableContext(VariableContext variableContext) {
        currentVariableContext.set(variableContext);
    }

    /**
     * 获取当前线程的变量上下文
     *
     * @return 变量上下文，如果没有设置则返回null
     */
    public static VariableContext getCurrentVariableContext() {
        return currentVariableContext.get();
    }

    /**
     * 清除当前线程的变量上下文
     */
    public static void clearCurrentVariableContext() {
        currentVariableContext.remove();
    }

    /**
     * 指定技术实现执行动作
     * */
    public TestResult execute(String actionName, TechType techType, ActionContext context) throws ActionException {
        IActionFactory factory = factories.get(techType);
        if (factory == null) throw new IllegalStateException("Factory not registered! techType: " + techType);

        IAction<?> action = factory.getAction(actionName);

        // 自动包装失败原因处理
        IAction<?> wrappedAction = wrapWithFailureReason(action);
        return wrappedAction.execute(context);
    }

    /**
     * 自动选择技术实现：按优先级选择技术实现，优先级高的先尝试
     */
    public TestResult execute(String actionName, ActionContext context) throws ActionException {
        // 变量相关的Action优先级最高
        TechType[] priority = {TechType.VARIABLE, TechType.CAR_API, TechType.ACCESSIBILITY_SERVICE};
        for (TechType tech : priority) {
            IActionFactory factory = factories.get(tech);
            if (factory != null && factory.containsAction(actionName)) {
                IAction<?> action = factory.getAction(actionName);
                // 自动包装失败原因处理
                IAction<?> wrappedAction = wrapWithFailureReason(action);
                return wrappedAction.execute(context);
            }
        }
        throw new ActionException("No available implementation! actionName: " + actionName, FailureCode.AB001);
    }

    /**
     * 包装Action以支持失败原因收集 
     */
    private <T extends ActionConfig> IAction<T> wrapWithFailureReason(IAction<T> action) {
        // 如果已经是包装过的，直接返回
        if (action instanceof EnhancedActionWrapper) {
            return action;
        }

        // 包装原始Action
        return new EnhancedActionWrapper<>(action);
    }
}
