package com.xiaopeng.executor.bean;

/**
 * 执行停止异常
 * 
 * 用于区分手动停止和普通步骤失败：
 * - 普通步骤失败：返回false，场景可能重试
 * - 手动停止：抛出此异常，场景应该立即终止，不进行重试
 * 
 * 手动停止时直接终止整个场景执行，忽略重试机制
 */
public class ExecutionStoppedException extends RuntimeException {
    
    public ExecutionStoppedException(String message) {
        super(message);
    }
    
    public ExecutionStoppedException(String message, Throwable cause) {
        super(message, cause);
    }
}
