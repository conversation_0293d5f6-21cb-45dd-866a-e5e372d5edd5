package com.xiaopeng.xpautotest.carmanager;

import android.car.Car;
import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.os.IBinder;

import com.xiaopeng.lib.utils.ThreadUtils;
import com.xiaopeng.xpautotest.carmanager.impl.AtlController.AtlControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.AvasController.AvasControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.AvmController.AvmControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.BcmController.BcmControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.BmsController.BmsControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.EpsController.EpsControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.HudController.HudControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.HvacController.HvacControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.LluController.LluControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.MsmController.MsmControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.McuController.McuControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.VcuController.VcuControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.AmpController.AmpControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.EspController.EspControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.ScuController.ScuControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.TBoxController.TboxControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.TpmsController.TpmsControllerFactory;
import com.xiaopeng.xpautotest.carmanager.impl.XpuController.XpuControllerFactory;
import com.xiaopeng.xpautotest.community.utils.Log;

import java.util.HashMap;

public class CarClientWrapper {
    private static final String TAG = CarClientWrapper.class.getSimpleName();
    /**
     * AMP manage service
     */
    public static final String XP_AMP_SERVICE = Car.XP_AMP_SERVICE;
    /**
     * AVAS manage service
     */
    public static final String XP_AVAS_SERVICE = Car.XP_AVAS_SERVICE;
    /**
     * BCM manage service
     */
    public static final String XP_BCM_SERVICE = Car.XP_BCM_SERVICE;
    /**
     * BMS manage service
     */
    public static final String XP_BMS_SERVICE = Car.XP_BMS_SERVICE;
    /**
     * EPS manage service
     */
    public static final String XP_EPS_SERVICE = Car.XP_EPS_SERVICE;
    /**
     * ESP manage service
     */
    public static final String XP_ESP_SERVICE = Car.XP_ESP_SERVICE;
    /**
     * ICM manage service
     */
    public static final String XP_ICM_SERVICE = Car.XP_ICM_SERVICE;
    /**
     * SCU manage service
     */
    public static final String XP_SCU_SERVICE = Car.XP_SCU_SERVICE;
    /**
     * TPMS manage service
     */
    public static final String XP_TPMS_SERVICE = Car.XP_TPMS_SERVICE;
    /**
     * VCU manage service
     */
    public static final String XP_VCU_SERVICE = Car.XP_VCU_SERVICE;
    /**
     * MSM manage service
     */
    public static final String XP_MSM_SERVICE = Car.XP_MSM_SERVICE;
    /**
     * LLU manage service
     */
    public static final String XP_LLU_SERVICE = Car.XP_LLU_SERVICE;
    /**
     * ATL manage service
     */
    public static final String XP_ATL_SERVICE = Car.XP_ATL_SERVICE;
    /**
     * MCU manage service
     */
    public static final String XP_MCU_SERVICE = Car.XP_MCU_SERVICE;
    /**
     * MCU manage service
     */
    public static final String XP_CIU_SERVICE = Car.XP_CIU_SERVICE;
    /**
     * TBOX manage service
     */
    public static final String XP_TBOX_SERVICE = Car.XP_TBOX_SERVICE;

    /**
     * SFS manage service
     */
    public static final String XP_SFS_SERVICE = "xp_sfs";

    /**
     * DIAGNOSTIC manage service
     */
    public static final String XP_DIAGNOSTIC_SERVICE = Car.XP_DIAGNOSTIC_SERVICE;
    /**
     * XPU manage service
     */
    public static final String XP_XPU_SERVICE = Car.XP_XPU_SERVICE;

    public static final String XP_CAR_INFO_SERVICE = "xp_CarInfoService";

    /**
     * AVM manage service
     */
    public static final String XP_AVM_SERVICE = Car.XP_AVM_SERVICE;

    /**
     * TBOX manage service
     */
    public static final String XP_HUD_SERVICE = Car.XP_HUD_SERVICE;

    /**
     * User Scenario Service
     */
    public static final String XP_USER_SCENARIO_SERVICE = "xp_UserScenarioService";

    public static final String XP_DCDC_SERVICE = Car.XP_DCDC_SERVICE;

    public static final String XP_CDC_SERVICE = Car.XP_CDC_SERVICE;

    public static final String XP_REMIND_SERVICE = Car.XP_REMIND_SERVICE;
    public static final String XP_CAMERA_AUTH_SERVICE = "xp_CameraAuthService";
    public static final String XP_VPM_SERVICE = Car.XP_VPM_SERVICE;

    public static final String XP_AUDIO_SERVICE = Car.AUDIO_SERVICE;

    public static final String XP_HVAC_SERVICE = Car.HVAC_SERVICE;

    static final String[] CAR_SVC_ARRAY = new String[]{
            XP_AMP_SERVICE,
            XP_MCU_SERVICE,
            XP_VCU_SERVICE,
            XP_BCM_SERVICE,
            XP_BMS_SERVICE,
            XP_ESP_SERVICE,
            XP_MSM_SERVICE,
            XP_EPS_SERVICE,
//            XP_ICM_SERVICE,
            XP_LLU_SERVICE,
            XP_ATL_SERVICE,
            XP_AVAS_SERVICE,
            XP_SCU_SERVICE,
            XP_TPMS_SERVICE,
//            XP_CIU_SERVICE,
            XP_TBOX_SERVICE,
//            XP_DIAGNOSTIC_SERVICE,
            XP_XPU_SERVICE,
//            XP_SFS_SERVICE,
            XP_AVM_SERVICE,
//            XP_USER_SCENARIO_SERVICE,
//            XP_DCDC_SERVICE,
//            XP_CDC_SERVICE,
//            XP_REMIND_SERVICE,
//            XP_CAMERA_AUTH_SERVICE,
//            XP_VPM_SERVICE,
            XP_HUD_SERVICE,
//            XP_AUDIO_SERVICE,
            XP_HVAC_SERVICE,
    };


    private static volatile CarClientWrapper sInstance = null;

    /**
     * @return the singleton of CarClientWrapper
     */
    public static CarClientWrapper getInstance() {
        if (sInstance == null) {
            synchronized (CarClientWrapper.class) {
                if (sInstance == null) {
                    sInstance = new CarClientWrapper();
                }
            }
        }
        return sInstance;
    }

    private CarClientWrapper() {
    }

    private final Object mCarClientReady = new Object();
    private Car mCarClient;
    private boolean mIsCarSvcConnected = false;

    private final Object mControllerLock = new Object();

    private final HashMap<String, BaseCarController<?, ?>> mControllers = new HashMap<>();

    private final ServiceConnection mCarConnectionCb = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onCarServiceConnected");
            initCarControllers();
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onCarServiceDisconnected");
            mIsCarSvcConnected = false;
//            reconnectToCar();
        }
    };

    /**
     * Connect to car service async
     */
    public void connectToCar(Context context) {
        if (!mIsCarSvcConnected) {
            mCarClient = Car.createCar(context, mCarConnectionCb);
            mCarClient.connect();
            Log.d(TAG, "Start to connect Car service");
        }
    }

    private void reconnectToCar(Context context) {
        ThreadUtils.postMainThread(() -> {
            Log.d(TAG, "reconnect to car service");
            connectToCar(context);
        }, 200L);
    }

    /**
     * Disconnect current connected car service
     */
    public void disconnect() {
        if (mIsCarSvcConnected && mCarClient != null) {
            mCarClient.disconnect();
        }
    }

    /**
     * @return True indicates we have connected to car service
     */
    public boolean isCarServiceConnected() {
        synchronized (mCarClientReady) {
            while (!mIsCarSvcConnected) {
                try {
                    Log.d(TAG, "Waiting car service connected");
                    mCarClientReady.wait();
                } catch (InterruptedException e) {
                    Log.e(TAG, e.getMessage());
                }
            }
            return true;
        }
    }

    /**
     * Get car service manager by module name
     *
     * @param serviceName service module name
     * @return car service manager object
     */
    public BaseCarController getController(String serviceName) {
        Log.i(TAG, "getController: " + serviceName);
        final Car carClient = getCarClient();
        BaseCarController controller;
        synchronized (mControllerLock) {
            controller = mControllers.get(serviceName);
            if (controller == null) {
                controller = createCarController(serviceName, carClient);
                mControllers.put(serviceName, controller);
            }
        }
        return controller;
    }

    private Car getCarClient() {
        synchronized (mCarClientReady) {
            while (!mIsCarSvcConnected) {
                try {
                    Log.d(TAG, "Waiting car service connecting");
                    mCarClientReady.wait();
                } catch (InterruptedException e) {
                    Log.e(TAG, "car client ready interrupted!", e);
                }
            }
            return mCarClient;
        }
    }

    private void initCarControllers() {
        ThreadUtils.execute(() -> {
            Log.d(TAG, "initCarControllers start");
            synchronized (mControllerLock) {
                for (String serviceName : CAR_SVC_ARRAY) {
                    BaseCarController controller = mControllers.get(serviceName);
                    if (controller == null) {
                        controller = createCarController(serviceName, mCarClient);
                        mControllers.put(serviceName, controller);
                    } else {
                        Log.d(TAG, "re-initCarControllers");
                        controller.disconnect();
                        controller.initCarManager(mCarClient);
                    }
                }
            }
            Log.d(TAG, "initCarControllers end");
            synchronized (mCarClientReady) {
                mIsCarSvcConnected = true;
                mCarClientReady.notifyAll();
            }
        });
    }

    private BaseCarController createCarController(String serviceName, final Car carClient) {
        BaseCarController controller;
        switch (serviceName) {
            case XP_VCU_SERVICE:
                controller = VcuControllerFactory.createCarController(carClient);
                break;
            case XP_MCU_SERVICE:
                controller = McuControllerFactory.createCarController(carClient);
                break;
            case XP_BCM_SERVICE:
                controller = BcmControllerFactory.createCarController(carClient);
                break;
            case XP_HVAC_SERVICE:
                controller = HvacControllerFactory.createCarController(carClient);
                break;
            case XP_MSM_SERVICE:
                controller = MsmControllerFactory.createCarController(carClient);
                break;
            case XP_ATL_SERVICE:
                controller = AtlControllerFactory.createCarController(carClient);
                break;
            case XP_AMP_SERVICE:
                controller = AmpControllerFactory.createCarController(carClient);
                break;
            case XP_ESP_SERVICE:
                controller = EspControllerFactory.createCarController(carClient);
                break;
            case XP_SCU_SERVICE:
                controller = ScuControllerFactory.createCarController(carClient);
                break;
            case XP_TPMS_SERVICE:
                controller = TpmsControllerFactory.createCarController(carClient);
                break;
            case XP_TBOX_SERVICE:
                controller = TboxControllerFactory.createCarController(carClient);
                break;
            case XP_XPU_SERVICE:
                controller = XpuControllerFactory.createCarController(carClient);
                break;
            case XP_EPS_SERVICE:
                controller = EpsControllerFactory.createCarController(carClient);
                break;
            case XP_AVAS_SERVICE:
                controller = AvasControllerFactory.createCarController(carClient);
                break;
            case XP_AVM_SERVICE:
                controller = AvmControllerFactory.createCarController(carClient);
                break;
            case XP_BMS_SERVICE:
                controller = BmsControllerFactory.createCarController(carClient);
                break;
            case XP_HUD_SERVICE:
                controller = HudControllerFactory.createCarController(carClient);
                break;
            case XP_LLU_SERVICE:
                controller = LluControllerFactory.createCarController(carClient);
                break;
            default:
                throw new IllegalArgumentException("Can not create controller for " + serviceName);
        }

        controller.initCarManager(carClient);
        return controller;
    }
}
