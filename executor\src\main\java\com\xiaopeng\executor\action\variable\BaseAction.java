package com.xiaopeng.executor.action.variable;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.executor.bean.VariableContext;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;

/**
 * 变量Action基类
 * 
 * 为所有变量相关的Action提供通用功能：
 * 1. 统一的配置管理
 * 2. 变量上下文访问
 * 3. 通用的工具方法
 */
public abstract class BaseAction implements IAction<VariableConfig> {
    
    protected static final String BASE_TAG = "BaseAction";
    
    protected VariableConfig config;
    
    @Override
    public void init(VariableConfig config) {
        this.config = config;
    }
    
    @Override
    public abstract TestResult execute(ActionContext context) throws ActionException;
    
    /**
     * 获取变量上下文
     * 
     * @param context ActionContext
     * @return VariableContext，如果不可用则抛出异常
     * @throws ActionException 当变量上下文不可用时
     */
    protected VariableContext getVariableContext(ActionContext context) throws ActionException {
        VariableContext variableContext = context.getVariableContext();
        if (variableContext == null) {
            throw new ActionException("VariableContext is not available", FailureCode.AB001);
        }
        return variableContext;
    }
    
    /**
     * 获取全局变量上下文
     * 
     * @return VariableContext，如果不可用则抛出异常
     * @throws ActionException 当变量上下文不可用时
     */
    protected VariableContext getGlobalVariableContext() throws ActionException {
        if (config == null) {
            throw new ActionException("VariableConfig is not initialized", FailureCode.AB001);
        }
        
        VariableContext variableContext = config.getVariableContext();
        if (variableContext == null) {
            throw new ActionException("Global VariableContext is not available", FailureCode.AB001);
        }
        return variableContext;
    }
    
    /**
     * 处理字符串值（去掉引号等）
     * 
     * @param value 原始值
     * @return 处理后的值
     */
    protected String processStringValue(String value) {
        if (value == null) {
            return "";
        }
        
        String trimmed = value.trim();
        
        // 处理引号包围的字符串
        if (trimmed.startsWith("\"") && trimmed.endsWith("\"") && trimmed.length() >= 2) {
            return trimmed.substring(1, trimmed.length() - 1);
        }
        
        return trimmed;
    }
    
    /**
     * 验证字符串参数
     * 
     * @param param 参数值
     * @param paramName 参数名称
     * @throws ActionException 当参数为空时
     */
    protected void validateStringParam(String param, String paramName) throws ActionException {
        if (param == null || param.trim().isEmpty()) {
            throw new ActionException(paramName + " cannot be null or empty", FailureCode.SI001);
        }
    }
    
    /**
     * 安全获取字符串参数
     * 
     * @param context ActionContext
     * @param paramName 参数名称（用于错误消息）
     * @return 字符串参数
     * @throws ActionException 当参数为空时
     */
    protected String getRequiredStringParam(ActionContext context, String paramName) throws ActionException {
        String param = context.getStringParam();
        validateStringParam(param, paramName);
        return param;
    }
}
