package com.xiaopeng.xpautotest.client.websocket;

import com.xiaopeng.xpautotest.community.utils.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import okhttp3.*;

public class WebSocketManager {
    private static final String TAG = "WebSocketManager";
    private final String wsUrl;
    private final String authToken; // Authentication token
    private static final long HEARTBEAT_INTERVAL = 30; // 30 seconds
    private static final int MAX_RETRIES = 5;
    private static final long INITIAL_BACKOFF = 1000; // 1 second

    private WebSocket webSocket;
    private OkHttpClient client;
    private ScheduledExecutorService scheduler;
    private CustomWebSocketListener listener;
    private int retryCount = 0;

    public WebSocketManager(String wsUrl, String authToken, CustomWebSocketListener listener) {
        this.wsUrl = wsUrl;
        this.authToken = authToken; // Store the authentication token
        this.listener = listener;

        client = new OkHttpClient.Builder()
                .pingInterval(HEARTBEAT_INTERVAL, TimeUnit.SECONDS)
                .build();

        scheduler = Executors.newScheduledThreadPool(1);
        connectWebSocket();
    }

    private void connectWebSocket() {
        Request request = new Request.Builder()
                .url(wsUrl)
                .addHeader("Authorization", "Token " + authToken) // Add token to header
                .build();
        webSocket = client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                retryCount = 0; // Reset retry count
                startHeartbeat();
                listener.onOpen(webSocket, response);
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                handleMessage(text);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                stopHeartbeat();
                listener.onClosed(webSocket, code, reason);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                stopHeartbeat();
                listener.onFailure(webSocket, t, response);
                reconnect();
            }
        });
    }

    public void sendMessage(String message) {
        if (webSocket == null) {
            listener.onFailure(null, new Exception("WebSocket is not initialized"), null);
            return;
        }

        boolean success = webSocket.send(message);
        if (!success) {
            listener.onFailure(webSocket, new Exception("Failed to send message"), null);
            reconnect(); // Attempt reconnection if sending fails
        }
    }

    private void handleMessage(String text) {
        try {
            JSONObject json = new JSONObject(text);
            String type = json.optString("type");

            if ("heartbeat".equals(type)) {
                // Handle heartbeat message if needed
                Log.i(TAG, "Received heartbeat");
            } else if ("error".equals(type)) {
                String errorMessage = json.optString("error", "Unknown error");
                listener.onFailure(webSocket, new Exception(errorMessage), null);
            } else {
                listener.onMessage(webSocket, json);
            }
        } catch (JSONException e) {
            listener.onFailure(webSocket, e, null);
        }
    }

    private void startHeartbeat() {
        if (scheduler.isShutdown() || scheduler.isTerminated()) {
            Log.w(TAG, "Scheduler is shut down, cannot start heartbeat");
            return;
        }

        scheduler.scheduleAtFixedRate(() -> {
            try {
                JSONObject json = new JSONObject();
                json.put("type", "heartbeat");
                webSocket.send(json.toString());
                Log.i(TAG, "Sent heartbeat");
            } catch (JSONException e) {
                listener.onFailure(webSocket, e, null);
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
    }

    private void stopHeartbeat() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdownNow();
        }
    }

    public void reconnect() {
        if (retryCount >= MAX_RETRIES) {
            listener.onFailure(webSocket, new Exception("Max retries reached"), null);
            return;
        }

        long backoffTime = INITIAL_BACKOFF * (1L << retryCount); // Exponential backoff
        retryCount++;
        Log.i(TAG, "Reconnecting in " + backoffTime + " ms (attempt " + retryCount + ")");

        if (scheduler.isShutdown() || scheduler.isTerminated()) {
            Log.w(TAG, "Scheduler is shut down, cannot reconnect!");
            return;
        }
        scheduler.schedule(this::connectWebSocket, backoffTime, TimeUnit.MILLISECONDS);
    }

    public void close() {
        stopHeartbeat();
        if (webSocket != null) {
            webSocket.close(1000, "User initiated");
        }
        client.dispatcher().executorService().shutdown();
        Log.i(TAG, "WebSocket closed");
    }
}