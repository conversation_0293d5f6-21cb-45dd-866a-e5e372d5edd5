package com.xiaopeng.xpautotest.helper;

import android.os.Handler;
import android.os.Looper;

import com.google.gson.reflect.TypeToken;
import com.xiaopeng.xpautotest.community.utils.CompressUtils;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileUtils;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.bean.ReportableEntity;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.utils.GsonUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

import okhttp3.ResponseBody;

public class TestDataStorageHelper {
    private static final String TAG = "TestDataStorageHelper";

    private static TestDataStorageHelper instance;

    private final Executor executor = Executors.newSingleThreadExecutor();

    private TestDataStorageHelper() {
    }

    public static TestDataStorageHelper getInstance() {
        if (instance == null) {
            instance = new TestDataStorageHelper();
        }
        return instance;
    }

    // 保存结果（追加模式）
    public void saveTask(TestTaskBean taskInfo, Consumer<Boolean> callback) {
        executor.execute(() -> {
            try {
                String json = GsonUtils.toJson(taskInfo, TAG);
                try (FileWriter file = new FileWriter(getFilePath(Constant.AUTOTEST_TEST_TASK_FILE))) {
                    file.write(json);
                    file.flush();
                    callback.accept(true);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (Exception e) {
                Log.e(TAG, "Save failed", e);
                callback.accept(false);
            }
        });
    }

    // 加载全部结果（异步）
    public void loadTask(Consumer<TestTaskBean> callback) {
        executor.execute(() -> {
            TestTaskBean taskInfo = loadTaskSync();
            new Handler(Looper.getMainLooper()).post(() -> callback.accept(taskInfo));
        });
    }

    // 同步加载方法
    public TestTaskBean loadTaskSync() {
        try{
            String fileString = FileUtils.readFileContent(Constant.AUTOTEST_TEST_TASK_FILE);
            if (fileString == null || fileString.isEmpty()) {
                Log.e(TAG, "Load failed: file is empty! " + Constant.AUTOTEST_TEST_TASK_FILE);
                return new TestTaskBean();
            }
            return  GsonUtils.fromJson(fileString, new TypeToken<TestTaskBean>(){}.getType());
        } catch (Exception e) {
            Log.e(TAG, "Load failed", e);
            return new TestTaskBean();
        }
    }

    public void saveResult(TestResultEntity result, Consumer<Boolean> callback) {
        executor.execute(() -> {
            try {
                List<TestResultEntity> existing = loadAllResultsSync();
                existing.add(result);
                String json = GsonUtils.toJson(existing, TAG);

                try (FileWriter file = new FileWriter(Constant.AUTOTEST_RESULT_FILE)) {
                    file.write(json);
                    file.flush();
                    callback.accept(true);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (Exception e) {
                Log.e(TAG, "Save failed", e);
                callback.accept(false);
            }
        });
    }

    public void saveMapResult(TestResultEntity result, Consumer<Boolean> callback) {
        executor.execute(() -> {
            try {
                Map<Long, TestResultEntity> existing = loadAllMapResultsSync();
                existing.put(result.getScriptId(), result);
                String json = GsonUtils.toJson(existing, TAG);

                try (FileWriter file = new FileWriter(getFilePath(Constant.AUTOTEST_RESULT_FILE))) {
                    file.write(json);
                    file.flush();
                    callback.accept(true);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (Exception e) {
                Log.e(TAG, "Save failed", e);
                callback.accept(false);
            }
        });
    }

    // 加载全部结果（异步）
    public void loadAllResults(Consumer<List<TestResultEntity>> callback) {
        executor.execute(() -> {
            List<TestResultEntity> results = loadAllResultsSync();
            new Handler(Looper.getMainLooper()).post(() -> callback.accept(results));
        });
    }

    public Map<Long, TestResultEntity> loadAllMapResultsSync() {
        try{
            String fileString = FileUtils.readFileContent(Constant.AUTOTEST_RESULT_FILE);
            Log.d(TAG, "loadAllResultsSync: fileString=" + fileString);
            if (fileString == null || fileString.isEmpty()) {
                return new HashMap<Long, TestResultEntity>();
            }
            return  GsonUtils.fromJson(fileString, new TypeToken<Map<Long, TestResultEntity>>(){}.getType());
        } catch (Exception e) {
            Log.e(TAG, "Load failed", e);
            return new HashMap<Long, TestResultEntity>();
        }
    }

    // 同步加载方法
    private List<TestResultEntity> loadAllResultsSync() {
        try{
            String fileString = FileUtils.readFileContent(Constant.AUTOTEST_RESULT_FILE);
            Log.d(TAG, "loadAllResultsSync: fileString=" + fileString);
            if (fileString == null || fileString.isEmpty()) {
                return new ArrayList<>();
            }
            return  GsonUtils.fromJson(fileString, new TypeToken<List<TestResultEntity>>(){}.getType());
        } catch (Exception e) {
            Log.e(TAG, "Load failed", e);
            return new ArrayList<>();
        }
    }

    public Map<Long, TestResultEntity> loadAllResultsToMap() {
        Map<Long, TestResultEntity> testResultMap = new HashMap<>();
        try {
            List<TestResultEntity> testResultList = loadAllResultsSync();
            Log.i(TAG, "loadTestResult: testResultList=" + testResultList);
            if (testResultList != null) {
                for (TestResultEntity testResult : testResultList) {
                    testResultMap.put(testResult.getScriptId(), testResult);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "loadTestResult failed: " + e.getMessage());
        }
        return testResultMap;
    }

    private static File getFilePath(String fileFullName) {
        File file = new File(fileFullName);
        File parentFile = file.getParentFile();
        if (parentFile != null && !parentFile.exists()) {
            parentFile.mkdirs();
        }
        return file;
    }

    /**
     * 保存脚本文件（覆盖写入）
     * @param body 响应体
     * @param callback 回调函数，参数为保存是否成功
     */
    public void saveScripts(ResponseBody body, Consumer<Boolean> callback) {
        executor.execute(() -> {
            File file = getFilePath(Constant.AUTOTEST_SCRIPT_ZIP_FILE);
            try (InputStream inputStream = body.byteStream();
                 OutputStream outputStream = Files.newOutputStream(file.toPath())) {

                byte[] buffer = new byte[4096];
                int read;
                while ((read = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, read);
                }
                outputStream.flush();
                Log.d(TAG, "File saved: " + file.getAbsolutePath());
                CompressUtils.unzip(file, file.getParent());
                Log.d(TAG, "File unzipped: " + file.getAbsolutePath());
                callback.accept(true);
            } catch (IOException e) {
                Log.e(TAG, "Save failed", e);
                callback.accept(false);
            }
        });
    }

    public void savePendingReportsSync(List<ReportableEntity> pendingReports) {
            try {
                String json = GsonUtils.toJson(pendingReports, TAG);
                try (FileWriter file = new FileWriter(Constant.AUTOTEST_SCRIPT_PENDING_REPORT_FILE)) {
                    file.write(json);
                    file.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } catch (Exception e) {
                Log.e(TAG, "Save failed", e);
            }
    }

    public void removePendingReportSync(ReportableEntity report) {
        Log.i(TAG, "removePendingReportSync: report=" + report);
        List<ReportableEntity> pendingReports = loadPendingReportsSync();
        pendingReports.remove(report);
        savePendingReportsSync(pendingReports);
    }

    public List<ReportableEntity> loadPendingReportsSync() {
        try{
            String fileString = FileUtils.readFileContent(Constant.AUTOTEST_SCRIPT_PENDING_REPORT_FILE);
            if (fileString == null || fileString.isEmpty()) {
                return new ArrayList<>();
            }
            return  GsonUtils.fromJson(fileString, new TypeToken<List<ReportableEntity>>(){}.getType());
        } catch (Exception e) {
            Log.e(TAG, "Load failed", e);
            return new ArrayList<>();
        }
    }

    // 清空历史记录
    public void clearHistory(Runnable onComplete) {
        executor.execute(() -> {
            FileUtils.deleteFile(Constant.AUTOTEST_TEST_TASK_FILE);
            FileUtils.deleteFile(Constant.AUTOTEST_SCRIPT_LIST_FILE);
            FileUtils.deleteFile(Constant.AUTOTEST_SCRIPT_ZIP_FILE);
            FileUtils.deleteFile(Constant.AUTOTEST_RESULT_FILE);
            FileUtils.deleteFile(Constant.AUTOTEST_SCRIPT_PENDING_REPORT_FILE);
            new Handler(Looper.getMainLooper()).post(onComplete);
        });
    }

    public void clearResult(Runnable onComplete) {
        executor.execute(() -> {
            FileUtils.deleteFile(Constant.AUTOTEST_RESULT_FILE);
            FileUtils.deleteFile(Constant.AUTOTEST_SCRIPT_PENDING_REPORT_FILE);
            new Handler(Looper.getMainLooper()).post(onComplete);
        });
    }

    public void clearResultSync() {
        executor.execute(() -> {
            FileUtils.deleteFile(Constant.AUTOTEST_RESULT_FILE);
            FileUtils.deleteFile(Constant.AUTOTEST_SCRIPT_PENDING_REPORT_FILE);
        });
    }
}
