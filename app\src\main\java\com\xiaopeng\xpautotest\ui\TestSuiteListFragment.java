package com.xiaopeng.xpautotest.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import com.xiaopeng.xpautotest.BuildConfig;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.adapter.TestSuiteAdapter;
import com.xiaopeng.xpautotest.manager.TestManager;
import com.xiaopeng.xpautotest.model.ITestDataState;
import com.xiaopeng.xpautotest.utils.ApiRouterUtils;
import com.xiaopeng.xpautotest.viewmodel.SuiteViewModel;
import com.xiaopeng.xui.widget.XImageView;

import java.util.ArrayList;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class TestSuiteListFragment extends Fragment {
    private static final String TAG = "TestSuiteListFragment";
    private SuiteViewModel viewModel;
    private TestSuiteAdapter adapter;
    private XImageView btnExecute;
    private XImageView btnDebug;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.i("onCreateView(" + getClass().getSimpleName() + "):" + this.hashCode());

        return inflater.inflate(R.layout.at_fragment_tab,container,false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.i("TestSuiteListFragment", "TestSuiteListFragment onViewCreated: ");

        btnExecute = view.findViewById(R.id.btn_execute);
        btnExecute.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (BuildConfig.DEBUG) {
                    Log.i(TAG, "onClick: btnExecute");
                }
                if (viewModel.getTestSuites().getValue() == null || viewModel.getTestSuites().getValue().isEmpty()) {
                    Log.e(TAG, "onClick: test suites is null");
                    // 弹出toast提示
                    Toast.makeText(getContext(), getContext().getString(R.string.executor_no_task), Toast.LENGTH_SHORT).show();
                    return;
                }

                viewModel.startTest(new TestManager.ITestDataCallBack<ITestDataState<Long>>() {
                    @Override
                    public void onUpdate(ITestDataState<Long> state) {
                        if (state instanceof ITestDataState.Success) {
                            Long executionId = ((ITestDataState.Success<Long>) state).getData();
                            if (viewModel.getAutoStartSuiteItem() != null) {
                                ((MainActivity) getContext()).startSuiteTesting(viewModel.getAutoStartSuiteItem(), executionId);
                            } else {
                                ((MainActivity) getContext()).startAllTesting(viewModel.getTestSuites().getValue(), executionId);
                            }
                            Log.i(TAG, "start test successful, executionId: " + executionId);
                        } else if (state instanceof ITestDataState.Error) {
                            Throwable error = ((ITestDataState.Error<Long>) state).getError();
                            Log.e(TAG, "start all test error: " + error.getMessage());
                        }
                    }
                });
            }
        });

        // 初始化RecyclerView
        RecyclerView rvCollections = view.findViewById(R.id.rv_test_suite_list);
        rvCollections.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new TestSuiteAdapter(new ArrayList<>(), collection ->
                viewModel.selectTestSuite(collection)
        );
        rvCollections.setAdapter(adapter);
        Log.i("TestSuiteListFragment", "adapter item count: ");

        // 观察数据变化
        viewModel.getTestSuites().observe(getViewLifecycleOwner(), suiteItems -> {
            adapter.updateData(suiteItems, viewModel.getCurrentSuiteId(), viewModel.getLoadMode());
            viewModel.resetLoadMode();
        });

        // 观察自动执行事件
        viewModel.getRequestAutoStart().observe(getViewLifecycleOwner(), shouldStart -> {
            if (shouldStart != null && shouldStart) {
                // 只在 shouldStart 为 true 时行动
                if (btnExecute != null) {
                    btnExecute.performClick();
                }
                viewModel.onAutoStartEventConsumed();
            }
        });

        btnDebug = view.findViewById(R.id.btn_debug);
        btnDebug.setVisibility(ApiRouterUtils.isFactoryMode() ? View.GONE : View.VISIBLE);
        btnDebug.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (BuildConfig.DEBUG) {
                    Log.i(TAG, "onClick: btnDebug");
                }
                ((MainActivity) getContext()).startDebugModeService();
            }
        });
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onCreate: ");
        }

        // 获取共享ViewModel
        viewModel = new ViewModelProvider(requireActivity()).get(SuiteViewModel.class);
    }

    @Override
    public void onStart() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onStart(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onStart();
    }

    public void onResume() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onResume(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onResume();
//        tryUpdateLan();
    }

    public void onPause() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onPause(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onPause();
    }

    @Override
    public void onStop() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onStop(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
        super.onStop();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.i(TAG, "onDestroyView: ");
    }

    public void onActivityNewIntent(boolean isUpdate) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onActivityNewIntent(" + getClass().getSimpleName() + "):" + this.hashCode());
        }
    }
}
