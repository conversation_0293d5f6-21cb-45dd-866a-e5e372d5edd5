package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TraceResultEntity implements Parcelable, ReportableEntity {
    private long taskExecutionId;
    public Long scriptId;
    public int stepId;
    public int traceId;
    public String type;
    public String src;
    public double value;

    public String result;
    public String message;
    private long timestamp;

    public TraceResultEntity(long taskExecutionId, long scriptId, int stepId, int traceId, String type, String src, double value, String result, String message) {
        this.taskExecutionId = taskExecutionId;
        this.scriptId = scriptId;
        this.stepId = stepId;
        this.traceId = traceId;
        this.type = type;
        this.src = src;
        this.value = value;
        this.result = result;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }

    protected TraceResultEntity(Parcel in) {
        taskExecutionId = in.readLong();
        scriptId = in.readLong();
        stepId = in.readInt();
        traceId = in.readInt();
        type = in.readString();
        src = in.readString();
        value = in.readDouble();
        result = in.readString();
        message = in.readString();
        timestamp = in.readLong();
    }

    public static final Creator<TraceResultEntity> CREATOR = new Creator<TraceResultEntity>() {
        @Override
        public TraceResultEntity createFromParcel(Parcel in) {
            return new TraceResultEntity(in);
        }

        @Override
        public TraceResultEntity[] newArray(int size) {
            return new TraceResultEntity[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(taskExecutionId);
        dest.writeLong(scriptId);
        dest.writeInt(stepId);
        dest.writeInt(traceId);
        dest.writeString(type);
        dest.writeString(src);
        dest.writeDouble(value);
        dest.writeString(result);
        dest.writeString(message);
        dest.writeLong(timestamp);
    }

    @Override
    public long getTaskExecutionId() {
        return taskExecutionId;
    }

    public long getScriptId() {
        return scriptId;
    }
}
