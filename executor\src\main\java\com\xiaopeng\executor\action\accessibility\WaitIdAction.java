package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class WaitIdAction extends BaseAction {
    private static final String TAG = "WaitIdAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String sourceId = (String) context.getStringParam();
        long timeout = context.getIntParam();
        if (sourceId == null) {
            throw new ActionException("source id is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "resourceId: " + sourceId + ", timeout: " + timeout);
        boolean result = this.service.waitForNodeById(sourceId, timeout) != null;
        if (!result) {
            return TestResult.failure("Failed to find node with id: '" + sourceId);
        }
        return TestResult.success("Successfully found node with id: '" + sourceId);
    }
}
