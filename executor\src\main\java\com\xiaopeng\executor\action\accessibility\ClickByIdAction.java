package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class ClickByIdAction extends BaseAction {
    private static final String TAG = "ClickByIdAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String sourceId = (String) context.getStringParam();
        if (sourceId == null) {
            throw new ActionException("source id is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "resourceId: " + sourceId);
        boolean result = this.service.clickNodeById(sourceId);
        if (!result) {
            return TestResult.failure("Failed to click by id: " + sourceId);
        }
        return TestResult.success("Clicked by id: " + sourceId + " successfully.");
    }
}
