package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class VDTAction extends BaseAction {
    private static final String TAG = "VDTAction";
    // 不支持disableMCU\rs\mcuUpdate\icmjson\loadjson\recordmsg\injectcan\dumpcan\listprops\setmultiprop
    private static final String[] VDT_METHOD_NAMES = {"setprop", "reportprop", "reportpropfile", "sendmsg", "listenprop", "logctrl", "setpropraw", "errorcode"};

    public boolean containsMethod(String methodName) {
        return Arrays.asList(VDT_METHOD_NAMES).contains(methodName);
    }
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String methodName = context.getStringParam();
        if (methodName == null) {
            throw new ActionException("VDT method is null!", FailureCode.SI001);
        }
        String paramName = context.getStringParam();
        if (paramName == null) {
            throw new ActionException("VDT param is null!", FailureCode.SI001);
        }
        String value = context.getStringParam();
        FileLogger.i("VDTAction", "execute methodName: " + methodName + ", paramName: " + paramName + ", value: " + value);
        if (methodName.equals("getprop")) {
            return shell("vdt getprop " + paramName, value);
        }
        if (containsMethod(methodName)) {
            if (value == null) {
                throw new ActionException(methodName + " value is null!", FailureCode.SI001);
            }
            return shell("vdt " + methodName + " " + paramName + " " + value, null);
        } else {
            throw new ActionException("VDT method is not supported!",FailureCode.SI001);
        }
    }

    public TestResult shell(String command, String expectValue) {
        CMDUtils.CMD_Result cmdResult = CMDUtils.runCMD(command, true, true);
        if (cmdResult == null) {
            String errorMsg = "getprop cmd result is null";
            FileLogger.e(TAG, errorMsg);
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
            return TestResult.failure(errorMsg);
        }
        if (cmdResult.resultCode != 0) {
            String errorMsg = "getprop cmd result is error! " + cmdResult.error;
            FileLogger.e(TAG, errorMsg);
            return TestResult.failure(errorMsg);
        }
        if (expectValue != null && !expectValue.equals(getRealValue(cmdResult.success))) {
            String errorMsg = "getprop cmd result is not equal! " + cmdResult.success;
            FileLogger.e(TAG, errorMsg);
            return TestResult.failure(errorMsg);
        }

        String successMsg = "shell command success: " + cmdResult.success;
        FileLogger.d(TAG, successMsg);
        return TestResult.success(successMsg);
    }

    public String getRealValue(String input) {
        Pattern pattern = Pattern.compile("=\\s*(\\d+),");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
}
