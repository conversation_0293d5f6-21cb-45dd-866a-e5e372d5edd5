package com.xiaopeng.executor.core;

import java.util.List;

/**
 * 条件分支类
 * 
 * 封装单个elseif分支的条件和执行单元：
 * 1. 条件存储：保存elseif的条件ExecutableUnit
 * 2. 单元管理：管理该分支的ExecutableUnit列表
 * 3. 分支标识：提供分支的唯一标识和描述
 */
public class ConditionalBranch {
    
    private final ExecutableUnit condition;
    private final List<ExecutableUnit> units;
    private final String branchType; // "elseif" 或其他类型
    
    /**
     * 构造函数
     * 
     * @param condition 分支条件
     * @param units 分支内的执行单元列表
     * @param branchType 分支类型
     */
    public ConditionalBranch(ExecutableUnit condition, List<ExecutableUnit> units, String branchType) {
        this.condition = condition;
        this.units = units;
        this.branchType = branchType;
    }
    
    /**
     * 获取分支条件
     * 
     * @return 条件ExecutableUnit
     */
    public ExecutableUnit getCondition() {
        return condition;
    }
    
    /**
     * 获取分支执行单元列表
     * 
     * @return 执行单元列表
     */
    public List<ExecutableUnit> getUnits() {
        return units;
    }
    
    /**
     * 获取分支类型
     * 
     * @return 分支类型字符串
     */
    public String getBranchType() {
        return branchType;
    }
    
    /**
     * 获取分支标识
     * 
     * @return 分支的唯一标识
     */
    public String getBranchId() {
        return branchType + "[" + (condition != null ? condition.getUnitId() : "empty") + "]";
    }
    
    /**
     * 检查分支是否为空
     * 
     * @return 如果分支没有执行单元则返回true
     */
    public boolean isEmpty() {
        return units == null || units.isEmpty();
    }
    
    /**
     * 获取分支内执行单元的数量
     * 
     * @return 执行单元数量
     */
    public int getUnitCount() {
        return units != null ? units.size() : 0;
    }
    
    @Override
    public String toString() {
        return "ConditionalBranch{" +
                "branchType='" + branchType + '\'' +
                ", condition=" + (condition != null ? condition.getUnitId() : "null") +
                ", unitCount=" + getUnitCount() +
                '}';
    }
}
