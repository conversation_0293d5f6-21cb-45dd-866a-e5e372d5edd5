package com.xiaopeng.utils;

import androidx.annotation.NonNull;

import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.constant.Constant.ValueType;
import com.xiaopeng.constant.Constant.ValueFlag;

import java.lang.reflect.Array;
import java.util.function.Function;


public class ValueTypeUtils {
    private static final String TAG = "ValueTypeUtils";

    public static String getValueType(@NonNull String value) {
        value = value.trim();
        // 对布尔类型的值做判断
        if (value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")) {
            return ValueType.BOOLEAN;
        }
        // 对列表类型的值做判断
        if (value.endsWith("]")) {
            if (value.startsWith("byte[")) {
                return ValueType.BYTE_VECTOR;
            } else if (value.startsWith("int[")) {
                return ValueType.INT_VECTOR;
            } else if (value.startsWith("float[")) {
                return ValueType.FLOAT_VECTOR;
            } else if (value.startsWith("long[")) {
                return ValueType.LONG_VECTOR;
            } else {
                return ValueType.UNKNOWN;
            }
        }

        if (value.contains(ValueFlag.LIST)) {
            // 对可能是多个值中之一的情况做判断
            value = value.split(ValueFlag.LIST)[0];
        } else if (value.contains(ValueFlag.RANGE)) {
            // 对范围值的判断
            if (value.startsWith(ValueFlag.RANGE) || value.endsWith(ValueFlag.RANGE)) {
                value = value.replace(ValueFlag.RANGE, "");
            } else {
                // 如果是范围值，取第一个值
                value = value.split(ValueFlag.RANGE)[0];
            }
        }
        value = value.trim();
        try {
            Integer.parseInt(value);
            return ValueType.INT;
        } catch (NumberFormatException e1) {
            try {
                Float.parseFloat(value);
                return ValueType.FLOAT;
            } catch (NumberFormatException ignored) {

            }
        }
        FileLogger.w(TAG, "getValueType: value is not a number or boolean, return string");
        return ValueType.STRING;
    }

    @SuppressWarnings("unchecked")
    private static <T> T convertArray(String value, String methodName, Function<String, Object> parser, Class<?> componentType) {
        if (value == null || value.isEmpty()) {
            return (T) Array.newInstance(componentType, 0);
        }
        String realValue = value.substring(value.indexOf('[') + 1, value.lastIndexOf(']')).trim();
        String[] values = realValue.split(ValueFlag.LIST);
        Object array = Array.newInstance(componentType, values.length);

        for (int i = 0; i < values.length; i++) {
            String trimmed = values[i].trim();
            try {
                Object element = parser.apply(trimmed);
                Array.set(array, i, element);
            } catch (NumberFormatException e) {
                FileLogger.e(TAG, methodName + ": Invalid value: " + trimmed);
                return (T) Array.newInstance(componentType, 0);
            }
        }
        return (T) array;
    }

    public static byte[] strToByteArray(String value) {
        return convertArray(value, "strToByteArray", Byte::parseByte, byte.class);
    }

    public static int[] strToIntArray(String value) {
        return convertArray(value, "strToIntArray", Integer::parseInt, int.class);
    }

    public static float[] strToFloatArray(String value) {
        return convertArray(value, "strToFloatArray", Float::parseFloat, float.class);
    }

    public static long[] strToLongArray(String value) {
        return convertArray(value, "strToLongArray", Long::parseLong, long.class);
    }
}
