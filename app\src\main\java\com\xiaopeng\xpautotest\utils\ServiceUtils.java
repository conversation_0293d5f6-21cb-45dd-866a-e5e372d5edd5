package com.xiaopeng.xpautotest.utils;

import android.app.ActivityManager;
import android.content.Context;

import java.util.List;

public class ServiceUtils {

    /**
     * 判断服务是否正在运行
     *
     * @param context      上下文
     * @param serviceClass 服务的Class对象
     * @return 如果服务正在运行，则返回true；否则返回false
     */
    public static boolean isServiceRunning(Context context, Class<?> serviceClass) {
        if (context == null || serviceClass == null) {
            return false;
        }

        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (manager == null) {
            return false;
        }

        List<ActivityManager.RunningServiceInfo> runningServices = manager.getRunningServices(Integer.MAX_VALUE);
        if (runningServices == null || runningServices.isEmpty()) {
            return false;
        }

        for (ActivityManager.RunningServiceInfo service : runningServices) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }
} 