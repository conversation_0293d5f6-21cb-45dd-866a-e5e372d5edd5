package com.xiaopeng.xpautotest.system;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.hardware.display.DisplayManager;
import android.media.Image;
import android.media.ImageReader;
import android.media.projection.MediaProjection;
import android.os.Handler;
import android.util.DisplayMetrics;

import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.community.event.FileUploadEvent;
import org.greenrobot.eventbus.EventBus;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.ExecutorService;
import java.io.BufferedReader;
import java.io.InputStreamReader;


public class ScreenCapture {
    private static final String TAG = "ScreenCapture";
    private MediaProjection mediaProjection;
    private ImageReader imageReader;
    private Handler handler;
    private ExecutorService executor;
    private DisplayMetrics metrics;
    private Context context;

    public ScreenCapture(Context context) {
        this.context = context;

//        handler = new Handler(Looper.getMainLooper());
//        executor = Executors.newSingleThreadExecutor();
//        metrics = new DisplayMetrics();
//
//        Display display = null;
//        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
//            display = context.getDisplay();
//        }
//        if (display != null) {
//            display.getMetrics(metrics);
//        }

        // 初始化ImageReader
//        imageReader = ImageReader.newInstance(
//                metrics.widthPixels,
//                metrics.heightPixels,
//                PixelFormat.RGBA_8888,
//                2
//        );

        // 请求屏幕捕捉权限
//        MediaProjectionManager projectionManager =
//                (MediaProjectionManager) context.getSystemService(MEDIA_PROJECTION_SERVICE);
//        mediaProjection = projectionManager.getMediaProjection(resultCode, data);
//        if (projectionManager != null) {
//            Intent intent = projectionManager.createScreenCaptureIntent();
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            startActivity(intent);
//        }
    }

    public void startCapture(String fileName) {
        if (mediaProjection == null || imageReader == null) return;

        mediaProjection.createVirtualDisplay(
                "ScreenCapture",
                metrics.widthPixels,
                metrics.heightPixels,
                metrics.densityDpi,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.getSurface(),
                null,
                handler
        );

        imageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
            @Override
            public void onImageAvailable(ImageReader reader) {
                Image image = null;
                try {
                    image = reader.acquireLatestImage();
                    if (image != null) {
                        processImage(image,fileName);
                    }
                } finally {
                    if (image != null) {
                        image.close();
                    }
                }
            }
        }, handler);
    }

    private void processImage(final Image image,String fileName) {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Bitmap bitmap = imageToBitmap(image);
                    saveBitmap(bitmap,fileName);
                } catch (Exception e) {
                    Log.e(TAG, "Process image error: " + e.getMessage());
                }
            }
        });
    }

    private Bitmap imageToBitmap(Image image) {
        Image.Plane[] planes = image.getPlanes();
        ByteBuffer buffer = planes[0].getBuffer();
        int pixelStride = planes[0].getPixelStride();
        int rowStride = planes[0].getRowStride();
        int rowPadding = rowStride - pixelStride * image.getWidth();

        return Bitmap.createBitmap(
                image.getWidth() + rowPadding / pixelStride,
                image.getHeight(),
                Bitmap.Config.ARGB_8888
        );
    }

    private void takeScreenshot(String fileName) {
        handler.post(new Runnable() {
            @Override
            public void run() {
                if (imageReader != null) {
                    Bitmap bitmap = Bitmap.createBitmap(
                            metrics.widthPixels,
                            metrics.heightPixels,
                            Bitmap.Config.ARGB_8888
                    );
                    Canvas canvas = new Canvas(bitmap);
                    canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);

                    // Perform additional drawing operations on the canvas if needed

                    saveBitmap(bitmap,fileName); // Save the bitmap after drawing
                }
            }
        });
    }

    public static boolean saveBitmap(Bitmap bitmap,String timeStampString) {
        return saveToPublicDirectory(bitmap, timeStampString);
    }

    public static String screenCap(String timeStampString, int screenWidth, int screenHeight, int displayId, String screenName) {
        long startTime = System.currentTimeMillis();
        FileLogger.i(TAG, "Attempting synchronous screenshot for displayId: " + displayId);
        File screenshotDirectory = new File(Constant.AUTOTEST_IMAGE_PATH);
        if (!screenshotDirectory.exists()) {
            if (!screenshotDirectory.mkdirs()) {
                String message = "Failed to create screenshot directory: " + screenshotDirectory.getAbsolutePath();
                FileLogger.e(TAG, message);
                FailureContextHolder.setFailureIfNotSet(FailureCode.EI006);
                return null;
            }
        }

        String fileName;
        if (timeStampString == null || timeStampString.isEmpty()) {
            timeStampString =  String.valueOf(startTime);
            FileLogger.w(TAG, "Timestamp was null or empty, generated new one: " + timeStampString);
        }

        // 截图文件名中加上屏幕尺寸信息
        String screenSuffix = "";
        if (displayId > 0) {
            // 非默认屏幕，使用_screenName_格式
            screenSuffix = "_" + screenName;
        }
        fileName = Constant.SCREENSHOT_PREFIX + screenWidth + "." + screenHeight + screenSuffix + "_" + timeStampString + Constant.SCREENSHOT_SUFFIX;

        File targetFile = new File(screenshotDirectory, fileName);
        String filePath = targetFile.getAbsolutePath();

        // 构建screencap命令，如果指定了displayId则添加-d参数
        String command;
        if (displayId >= 0) {
            command = "/system/bin/screencap -d " + displayId + " -p " + filePath;
        } else {
            command = "/system/bin/screencap -p " + filePath;
        }

        CMDUtils.CMD_Result cmdResult = CMDUtils.runCMD(command, true, true);
        long duration = System.currentTimeMillis() - startTime; // Calculate duration

        if (cmdResult != null) {
            if (cmdResult.success != null && !cmdResult.success.isEmpty()) {
                FileLogger.d(TAG, "Screencap stdout:\n" + cmdResult.success);
            }
            if (cmdResult.error != null && !cmdResult.error.isEmpty()) {
                FileLogger.e(TAG, "Screencap stderr:\n" + cmdResult.error);
            }
            if (cmdResult.resultCode == 0 && targetFile.exists() && targetFile.length() > 0) {
                FileLogger.i(TAG, "Screencap successfully saved to: " + targetFile.getName() + " (Took " + duration + "ms)");
                // 发送EventBus事件
                EventBus.getDefault().post(new FileUploadEvent(targetFile.getAbsolutePath()));
                return fileName;
            } else {
                FileLogger.e(TAG, "Screencap failed. Exit code: " + cmdResult.resultCode + " (Took " + duration + "ms)");
                FailureContextHolder.setFailureIfNotSet(FailureCode.EI005);
                return null;
            }
        } else {
            FileLogger.e(TAG, "CMDUtils.runCMD returned null for screencap command. (Took " + duration + "ms)");
            FailureContextHolder.setFailureIfNotSet(FailureCode.EI005);
            return null;
        }
    }

    // Android 9 及以下版本保存到公共目录
    private static boolean saveToPublicDirectory(Bitmap bitmap, String timeStampString) {
        boolean result  = false;
        File directory = new File(Constant.AUTOTEST_IMAGE_PATH);

        if (!directory.exists() && !directory.mkdirs()) {
            Log.e(TAG, "Failed to create directory");
            return result;
        }
        File file = new File(directory, Constant.SCREENSHOT_PREFIX+timeStampString+Constant.SCREENSHOT_SUFFIX);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            result = bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
            if (result){
                Log.d(TAG, "Screenshot saved to: " + file.getAbsolutePath());
                // 发送EventBus事件
                EventBus.getDefault().post(new FileUploadEvent(file.getAbsolutePath()));
            }
            return result;
        } catch (IOException e) {
            FileLogger.e(TAG, "Failed to save screenshot: " + e.getMessage());
        }
        return result;
    }

    public void clear() {
        if (mediaProjection != null) {
            mediaProjection.stop();
        }
        if (imageReader != null) {
            imageReader.close();
        }
        executor.shutdown();
    }

}
