package com.xiaopeng.executor.core;

import com.xiaopeng.xpautotest.community.test.TestStep;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.ArrayList;
import java.util.List;

/**
 * 条件解析器
 * 
 * 负责识别和解析if-else语法结构：
 * 1. 语法识别：识别If、ElseIf、Else、EndIf关键字
 * 2. 缩进处理：基于缩进层级确定代码块边界
 * 3. 条件解析：将条件行解析为ExecutableUnit对象
 * 4. 分支构建：构建分支内的ExecutableUnit列表
 * 5. 验证检查：验证语法正确性和完整性
 */
public class ConditionalParser {
    
    private static final String TAG = "ConditionalParser";
    
    // 条件关键字常量
    public static final String IF_KEYWORD = "If";
    public static final String ELSEIF_KEYWORD = "ElseIf";
    public static final String ELSE_KEYWORD = "Else";
    public static final String ENDIF_KEYWORD = "EndIf";
    
    /**
     * 解析条件块
     * 
     * @param lines 脚本行列表
     * @param startIndex 开始解析的行索引（If行）
     * @param scriptId 脚本ID
     * @param phaseType 阶段类型
     * @return 解析结果，包含ConditionalUnit和结束行索引
     */
    public static ParseResult parseConditionalBlock(List<String> lines, int startIndex, 
                                                   long scriptId, String phaseType) {
        try {
            // 解析If条件
            String ifLine = lines.get(startIndex).trim();
            TestStep ifConditionStep = parseConditionLine(ifLine, scriptId, startIndex + 1, phaseType);
            ExecutableUnit ifCondition = new StepUnit(ifConditionStep);
            
            List<ExecutableUnit> ifUnits = new ArrayList<>();
            List<ConditionalBranch> elseIfBranches = new ArrayList<>();
            List<ExecutableUnit> elseUnits = null;
            
            int currentIndex = startIndex + 1;
            String currentBranchType = "if";
            List<ExecutableUnit> currentUnits = ifUnits;
            
            // 解析条件块内容
            while (currentIndex < lines.size()) {
                String line = lines.get(currentIndex).trim();
                
                if (line.isEmpty() || line.startsWith("#")) {
                    currentIndex++;
                    continue;
                }
                
                // 检查关键字
                if (isConditionalKeyword(line)) {
                    if (line.startsWith(ELSEIF_KEYWORD)) {
                        // 保存当前分支，开始新的elseif分支
                        if ("elseif".equals(currentBranchType)) {
                            // 这不是第一个elseif，需要保存前一个elseif分支
                            // 这种情况在实际解析中会被正确处理
                        }
                        
                        TestStep elseIfConditionStep = parseConditionLine(line, scriptId, currentIndex + 1, phaseType);
                        ExecutableUnit elseIfCondition = new StepUnit(elseIfConditionStep);
                        List<ExecutableUnit> elseIfUnits = new ArrayList<>();
                        elseIfBranches.add(new ConditionalBranch(elseIfCondition, elseIfUnits, "elseif"));
                        
                        currentBranchType = "elseif";
                        currentUnits = elseIfUnits;
                        
                    } else if (line.startsWith(ELSE_KEYWORD)) {
                        // 开始else分支
                        elseUnits = new ArrayList<>();
                        currentBranchType = "else";
                        currentUnits = elseUnits;
                        
                    } else if (line.startsWith(ENDIF_KEYWORD)) {
                        // 结束条件块
                        break;
                    }
                } else {
                    // 普通步骤，添加到当前分支
                    try {
                        TestStep step = new TestStep(scriptId, currentIndex + 1, line, "", "", phaseType);
                        StepUnit stepUnit = new StepUnit(step);
                        currentUnits.add(stepUnit);
                    } catch (Exception e) {
                        FileLogger.e(TAG, "Failed to parse step at line " + currentIndex + ": " + line, e);
                        throw new RuntimeException("Failed to parse conditional block", e);
                    }
                }
                
                currentIndex++;
            }
            
            // 验证语法完整性
            if (currentIndex >= lines.size() || !lines.get(currentIndex).trim().startsWith(ENDIF_KEYWORD)) {
                throw new RuntimeException("Missing EndIf for conditional block starting at line " + startIndex);
            }
            
            // 创建ConditionalUnit
            ConditionalUnit conditionalUnit = new ConditionalUnit(ifCondition, ifUnits, elseIfBranches, elseUnits);
            
            FileLogger.i(TAG, "Successfully parsed conditional block: " + conditionalUnit.getUnitId());
            
            return new ParseResult(conditionalUnit, currentIndex);
            
        } catch (Exception e) {
            FileLogger.e(TAG,e.getMessage());
            throw new RuntimeException("Conditional parsing failed", e);
        }
    }
    
    /**
     * 解析条件行
     * 
     * @param line 条件行内容
     * @param scriptId 脚本ID
     * @param stepId 步骤ID
     * @param phaseType 阶段类型
     * @return 条件TestStep对象
     */
    public static TestStep parseConditionLine(String line, long scriptId, int stepId, String phaseType) {
        try {
            // 提取条件部分（去掉If/ElseIf关键字）
            String conditionContent;
            if (line.startsWith(IF_KEYWORD)) {
                conditionContent = line.substring(IF_KEYWORD.length()).trim();
            } else if (line.startsWith(ELSEIF_KEYWORD)) {
                conditionContent = line.substring(ELSEIF_KEYWORD.length()).trim();
            } else {
                throw new IllegalArgumentException("Invalid condition line: " + line);
            }
            
            if (conditionContent.isEmpty()) {
                throw new IllegalArgumentException("Empty condition in line: " + line);
            }

            // 检查是否为变量比较表达式
            if (isVariableCompareExpression(conditionContent)) {
                // 变量比较表达式：构建完整的命令行
                String commandLine = "VariableCompare " + conditionContent;
                return new TestStep(scriptId, stepId, commandLine, "变量比较", "", phaseType);
            } else {
                // 普通Action条件：条件内容作为action名
                return new TestStep(scriptId, stepId, conditionContent, "条件判断", "", phaseType);
            }

        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to parse condition line: " + line, e);
            throw new RuntimeException("Failed to parse condition", e);
        }
    }
    
    /**
     * 检查是否为变量比较表达式
     *
     * @param conditionContent 条件内容
     * @return 如果是变量比较表达式则返回true
     */
    public static boolean isVariableCompareExpression(String conditionContent) {
        // 检查是否包含变量引用（$开头）和比较操作符（== 或 !=）
        return conditionContent.contains("$") &&
               (conditionContent.contains("==") || conditionContent.contains("!="));
    }

    /**
     * 检查是否为条件关键字
     *
     * @param line 行内容
     * @return 如果是条件关键字则返回true
     */
    public static boolean isConditionalKeyword(String line) {
        if (line == null || line.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = line.trim();
        return trimmed.startsWith(IF_KEYWORD) ||
               trimmed.startsWith(ELSEIF_KEYWORD) ||
               trimmed.startsWith(ELSE_KEYWORD) ||
               trimmed.startsWith(ENDIF_KEYWORD);
    }
    
    /**
     * 检查是否为If关键字
     * 
     * @param line 行内容
     * @return 如果是If关键字则返回true
     */
    public static boolean isIfKeyword(String line) {
        return line != null && line.trim().startsWith(IF_KEYWORD);
    }
    
    /**
     * 解析结果类
     */
    public static class ParseResult {
        private final ConditionalUnit conditionalUnit;
        private final int endIndex;
        
        public ParseResult(ConditionalUnit conditionalUnit, int endIndex) {
            this.conditionalUnit = conditionalUnit;
            this.endIndex = endIndex;
        }
        
        public ConditionalUnit getConditionalUnit() {
            return conditionalUnit;
        }
        
        public int getEndIndex() {
            return endIndex;
        }
    }
}
