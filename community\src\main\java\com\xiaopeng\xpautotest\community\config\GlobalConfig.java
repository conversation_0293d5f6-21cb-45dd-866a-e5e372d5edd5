package com.xiaopeng.xpautotest.community.config;

import com.xiaopeng.xpautotest.community.utils.Constant;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class GlobalConfig {
    private static GlobalConfig instance;
    private List<String> blackActivityNames;

    private GlobalConfig() {
        // Initialize with a thread-safe collection
        blackActivityNames = new CopyOnWriteArrayList<>();
    }

    public static synchronized GlobalConfig getInstance() {
        if (instance == null) {
            instance = new GlobalConfig();
        }
        return instance;
    }

    public synchronized List<String> getBlackActivityNames() {
        return blackActivityNames;
    }

    public synchronized void setBlackActivityNames(List<String> blackActivityNames) {
        if (blackActivityNames != null && !blackActivityNames.isEmpty()) {
            this.blackActivityNames.clear();
            this.blackActivityNames.addAll(blackActivityNames);
        } else {
            this.blackActivityNames = Constant.BLACK_ACTIVITY_NAMES;
        }
    }
}
