package com.xiaopeng.utils;

import com.xiaopeng.constant.Constant.ValueFlag;
import com.xiaopeng.constant.Constant.ValueType;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class ValueCompareUtils {
    private static final String TAG = "ValueCompareUtils";

    public static boolean compareValue(Object realValue, String expectValue, String valueType) {
        if (realValue == null || expectValue == null || valueType == null) {
            FileLogger.e(TAG, "compareValue: realValue, expectValue or valueType is null");
            return false;
        }
        // 统一将获取到的值转换成字符串
        String realValueStr = String.valueOf(realValue).trim();
        // 1.非范围和非列表的情况
        if (!expectValue.contains(ValueFlag.RANGE) && !expectValue.contains(ValueFlag.LIST)) {
            return realValueStr.equals(expectValue);
        }
        // 2.对期望值是vector的情况进行处理
        if (valueType.endsWith(ValueType.VECTOR)) {
            // 对于获取到的值和期望值，统一去掉空格来判断
            realValueStr = realValueStr.replace(" ", "");
            expectValue = expectValue.replace(" ", "");
            switch (valueType) {
                case ValueType.BYTE_VECTOR:
                    return (ValueType.BYTE + realValueStr).equals(expectValue);
                case ValueType.INT_VECTOR:
                    return (ValueType.INT + realValueStr).equals(expectValue);
                case ValueType.FLOAT_VECTOR:
                    return (ValueType.FLOAT + realValueStr).equals(expectValue);
                case ValueType.LONG_VECTOR:
                    return (ValueType.LONG + realValueStr).equals(expectValue);
                default:
                    // 如果是其他类型的vector，直接返回false
                    FileLogger.e(TAG, "compareValue Unsupported vector valueType: " + valueType);
                    return false;
            }
        }
        // 3.处理contains的情况,这块要放在vector类型比较之后,因为分隔符用的都是,
        if (expectValue.contains(ValueFlag.LIST)) {
            // 对于包含逗号的情况，检查 realValueStr 是否在 expectValue 中
            String[] expectValues = expectValue.split(ValueFlag.LIST);
            for (String expect : expectValues) {
                if (realValueStr.equals(expect.trim())) {
                    return true;
                }
            }
            return false;
        }
        // 4.处理范围比较的情况
        if (expectValue.startsWith(ValueFlag.RANGE)) {
            switch (valueType) {
                case ValueType.INT:
                    return Integer.parseInt(realValueStr) <= Integer.parseInt(expectValue.substring(1));
                case ValueType.FLOAT:
                    return Float.parseFloat(realValueStr) <= Float.parseFloat(expectValue.substring(1));
                default:
                    FileLogger.e(TAG, "compareValue Unsupported valueType: " + valueType);
                    return false;
            }
        } else if (expectValue.endsWith(ValueFlag.RANGE)) {
            switch (valueType) {
                case ValueType.INT:
                    return Integer.parseInt(realValueStr) >= Integer.parseInt(expectValue.substring(0, expectValue.length() - 1));
                case ValueType.FLOAT:
                    return Float.parseFloat(realValueStr) >= Float.parseFloat(expectValue.substring(0, expectValue.length() - 1));
                default:
                    FileLogger.e(TAG, "compareValue Unsupported valueType: " + valueType);
                    return false;
            }
        } else {
            String[] parts = expectValue.split(ValueFlag.RANGE);
            if (parts.length != 2) {
                FileLogger.e(TAG, "compareValue: Invalid expected value format: " + expectValue);
                return false;
            }
            switch (valueType) {
                case ValueType.INT:
                    int valueInt = Integer.parseInt(realValueStr);
                    int lowerBoundInt = Integer.parseInt(parts[0]);
                    int upperBoundInt = Integer.parseInt(parts[1]);
                    return valueInt >= lowerBoundInt && valueInt <= upperBoundInt;
                case ValueType.FLOAT:
                    float valueFloat = Float.parseFloat(realValueStr);
                    float lowerBoundFloat = Float.parseFloat(parts[0]);
                    float upperBoundFloat = Float.parseFloat(parts[1]);
                    return valueFloat >= lowerBoundFloat && valueFloat <= upperBoundFloat;
                default:
                    FileLogger.e(TAG, "compareValue Unsupported valueType: " + valueType);
                    return false;
            }
        }
    }

    public static boolean isNumber(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        str = str.trim();
        try {
            double num = Double.parseDouble(str);
            // 如果转换成功，检查是否存在非法字符
            // 例如，"123a" 会被 parseInt 接受但实际上不是有效数字
            // 所以我们需要确保转换后的字符串与原字符串匹配
            String cleanStr = String.valueOf(num);
            // 对于整数，Double.toString 会添加 ".0"，所以需要特殊处理
            if (cleanStr.endsWith(".0") && !str.contains(".")) {
                cleanStr = cleanStr.substring(0, cleanStr.length() - 2);
            }
            // 检查科学计数法格式
            if (cleanStr.contains("E") || cleanStr.contains("e")) {
                String[] parts = cleanStr.split("[Ee]");
                if (parts.length == 2) {
                    cleanStr = parts[0] + "e" + parts[1];
                }
            }
            return cleanStr.equals(str);
        } catch (Exception e) {
            e.printStackTrace();
            FileLogger.e(TAG, "isNumber: Exception: " + e.getMessage());
            return false;
        }
    }
}
