package com.xiaopeng.executor;

import android.content.Context;
import com.xiaopeng.executor.core.ScriptUnit;
import com.xiaopeng.executor.bean.ExecutionStoppedException;
import com.xiaopeng.executor.bean.ExecutorContext;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;
import com.xiaopeng.xpautotest.community.test.TestScript;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.HashSet;
import java.util.Set;

/**
 * 测试执行器
 *
 * 基于新的ExecutableUnit架构实现的测试执行器：
 * 1. 继承BaseTestExecutor，复用所有基础设施代码
 * 2. 使用ScriptUnitPhaseUnitSceneUnitStepUnit的层次化执行架构
 * 3. 支持Precondition/Procedure/PostCondition阶段结构
 * 4. 支持if-else条件判断等高级控制结构
 * 5. 保持与原有API的100%兼容性
 *
 */
public class TestExecutor extends BaseTestExecutor {

    private static final String TAG = "TestExecutor";

    // 非实质性测试Action的集合（只进行变量操作，不涉及实际业务逻辑）
    private static final Set<String> NON_SUBSTANTIAL_ACTIONS = new HashSet<>();
    static {
        NON_SUBSTANTIAL_ACTIONS.add("GetValueByFeature");
        NON_SUBSTANTIAL_ACTIONS.add("Set");
        NON_SUBSTANTIAL_ACTIONS.add("VariableCompare");
    }

    public TestExecutor(Context context, String packageName) {
        super(context, packageName);
    }

    /**
     * 运行测试脚本 - 使用结构化架构实现
     *
     * 使用ExecutableUnit架构执行：
     * 1. 将TestScript包装为ScriptUnit
     * 2. 按阶段分组执行（PreconditionProcedurePostCondition）
     * 3. 支持场景内的条件判断和控制结构
     * 4. 保持与原有API的完全兼容性
     *
     * @param testScript 测试脚本
     * @return 执行结果状态码
     */
    @Override
    protected int runTestScript(TestScript testScript) {
        if (testScript == null) {
            FileLogger.e(TAG, "TestScript is null!");
            return CaseExecuteState.FAILURE;
        }
        
        try {
            // 准备执行上下文
            ExecutorContext context = this.prepareExecutionContext();

            // 使用新架构：将TestScript包装为ScriptUnit
            ScriptUnit scriptUnit = new ScriptUnit(testScript);

            // 执行脚本，传递上下文
            boolean result = scriptUnit.execute(context);

            // 根据执行情况决定返回值
            if (result) {
                // 执行成功，检查是否应该返回SKIPPED
                if (shouldReturnSkipped(context)) {
                    FileLogger.i(TAG, "Script execution succeeded but returning SKIPPED: " + context.getExecutionSummary());
                    return CaseExecuteState.SKIPPED;
                } else {
                    return CaseExecuteState.SUCCESS;
                }
            } else {
                // 执行失败
                return CaseExecuteState.FAILURE;
            }

        } catch (ExecutionStoppedException e) {
            // 手动停止：返回执行失败
            FileLogger.i(TAG, "Script execution stopped manually: " + e.getMessage());
            return CaseExecuteState.FAILURE;
        } catch (Exception e) {
            // 其他异常：记录错误日志和完整堆栈
            FileLogger.e(TAG, "Script execution failed with unexpected exception: " + e.getClass().getSimpleName() + " - " + e.getMessage(), e);
            return CaseExecuteState.FAILURE;
        } finally {
            FileLogger.i(TAG, "Script execution completed");
        }
    }

    /**
     * 判断是否应该返回SKIPPED状态
     *
     * @param context 执行器上下文
     * @return true表示应该返回SKIPPED
     */
    private boolean shouldReturnSkipped(ExecutorContext context) {
        // 条件1：没有执行过Procedure阶段的step
        if (!context.hasProcedureStepExecuted()) {
            FileLogger.i(TAG, "Should return SKIPPED: No Procedure steps executed");
            return true;
        }

        // 条件2：Procedure 阶段的脚本中有if-else但分支内步骤都没执行
        if (context.hasProcedureConditionalStructure() && !context.hasProcedureConditionalStepExecuted()) {
            FileLogger.i(TAG, "Should return SKIPPED: Has conditional structure but no conditional steps executed");
            return true;
        }

        // 条件3：Procedure阶段执行的step的Action全是非实质性测试Action
        Set<String> executedActions = context.getExecutedActions();
        if (!executedActions.isEmpty() && NON_SUBSTANTIAL_ACTIONS.containsAll(executedActions)) {
            FileLogger.i(TAG, "Should return SKIPPED: Only non-substantial actions executed in Procedure: " + executedActions);
            return true;
        }

        return false;
    }
}
