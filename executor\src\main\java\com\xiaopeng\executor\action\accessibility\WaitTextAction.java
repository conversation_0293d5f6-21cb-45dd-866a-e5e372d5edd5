package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class WaitTextAction extends BaseAction {
    private static final String TAG = "WaitTextAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String text = (String) context.getStringParam();
        long timeout = context.getIntParam();
        if (text == null) {
            throw new ActionException("text is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "text: " + text + ", timeout: " + timeout);
        boolean result = this.service.waitForNodeByText(text, timeout) != null;
        if (!result) {
            return TestResult.failure("Failed to find node with text: '" + text);
        }
        return TestResult.success("Successfully found node with text: '" + text);
    }
}
