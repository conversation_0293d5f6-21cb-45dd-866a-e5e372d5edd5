package com.xiaopeng.xpautotest.helper.log.processor;

import com.xiaopeng.xpautotest.helper.log.model.LogProcessContext;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessorResult;

/**
 * 日志处理器接口
 * 定义统一的日志处理规范，包括处理、压缩、上传等操作
 */
public interface ILogProcessor {

    /**
     * 处理日志
     * @param context 日志处理上下文
     * @return 日志处理器结果
     */
    LogProcessorResult process(LogProcessContext context);

    /**
     * 获取处理器支持的日志类型
     * @return 日志类型
     */
    LogProcessorResult.LogType getSupportedLogType();

    /**
     * 检查是否应该处理此日志
     * @param context 日志处理上下文
     * @return true表示应该处理，false表示跳过
     */
    boolean shouldProcess(LogProcessContext context);
}
