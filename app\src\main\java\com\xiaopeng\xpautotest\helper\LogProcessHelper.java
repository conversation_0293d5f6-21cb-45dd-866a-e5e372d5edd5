package com.xiaopeng.xpautotest.helper;

import com.xiaopeng.xpautotest.bean.TestStartedEntity;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.helper.log.LogProcessorManager;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessContext;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessResult;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessorResult;

import java.util.List;

/**
 * 日志处理处理器
 * 专门负责测试任务执行完毕后归集必要的日志
 * 无状态处理器，可以直接实例化使用
 */
public class LogProcessHelper {
    private static final String TAG = "LogProcessHandler";

    private final LogProcessorManager processorManager;

    public LogProcessHelper() {
        this.processorManager = LogProcessorManager.createDefault();
    }



    /**
     * 处理所有日志，返回结果
     * 确保任何异常都不会影响调用方的主流程
     *
     * @param startTimestamp 测试开始时间戳
     * @param endTimestamp 测试结束时间戳
     * @param taskExecutionInfo 任务执行信息
     * @return 日志处理结果，永不返回null
     */
    public LogProcessResult processAllLogs(long startTimestamp, long endTimestamp,
                                           TestStartedEntity taskExecutionInfo) {
        try {
            if (taskExecutionInfo == null) {
                FileLogger.w(TAG, "Cannot process logs. TaskExecutionInfo is invalid.");
                return LogProcessResult.failure();
            }

            LogProcessContext context = LogProcessContext.create(
                    startTimestamp, endTimestamp, taskExecutionInfo);

            List<LogProcessorResult> results = processorManager.processAllLogs(context);

            if (results == null || results.isEmpty()) {
                FileLogger.w(TAG, "No log processing results");
                return LogProcessResult.success(null, null);
            }

            // 提取处理结果
            return extractResults(results);

        } catch (Exception e) {
            // 最外层异常保护，确保日志处理异常不影响主流程
            FileLogger.e(TAG, "Critical error in log processing, continuing with main flow", e);
            return LogProcessResult.failure();
        }
    }

    /**
     * 从处理结果列表中提取具体的路径信息
     * @param results 日志处理结果列表
     * @return 提取后的结果
     */
    private LogProcessResult extractResults(List<LogProcessorResult> results) {
        String fileLogPath = null;
        String cduLogPath = null;
        boolean hasErrors = false;

        for (LogProcessorResult result : results) {
            if (result.isSuccess()) {
                switch (result.getLogType()) {
                    case FILE_LOG:
                        fileLogPath = result.getOssPath();
                        break;
                    case CDU_LOG:
                        cduLogPath = result.getOssPath();
                        break;
                }
            } else {
                hasErrors = true;
                FileLogger.w(TAG, result.getLogType() + " processing failed: " + result.getErrorMessage());
            }
        }

        return LogProcessResult.partial(fileLogPath, cduLogPath, hasErrors);
    }


}
