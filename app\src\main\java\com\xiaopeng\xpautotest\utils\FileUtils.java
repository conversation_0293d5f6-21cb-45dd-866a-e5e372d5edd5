package com.xiaopeng.xpautotest.utils;

import android.graphics.Bitmap;
import android.text.TextUtils;

import com.xiaopeng.xpautotest.constant.Constant;
import com.xiaopeng.xpautotest.community.utils.Log;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

public class FileUtils {
    private static final String TAG = "FileUtils";

    /**
     * 读取文本数据
     *
     * @return String, 读取到的文本内容，失败返回null
     */
    public static String readFile(String filePath) {
        File file = TextUtils.isEmpty(filePath) ? null : new File(filePath);
        return readFile(file);
    }

    /**
     * 读取文本数据
     *
     * @return String, 读取到的文本内容，失败返回null
     */
    public static String readFile(File file) {
        if (file == null || !file.exists()) {
            Log.e(TAG, "---file is not exist! file = " + file);
            return null;
        }
        FileInputStream fis = null;
        String content = null;
        try {
            fis = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            while (true) {
                int readLength = fis.read(buffer);
                if (readLength == -1) {
                    break;
                }
                arrayOutputStream.write(buffer, 0, readLength);
            }
            fis.close();
            arrayOutputStream.close();
            content = arrayOutputStream.toString("utf-8");

        } catch (Exception e) {
            Log.e(TAG, "---error = " + e.getMessage());
        } finally {
            safeClose(fis);
        }
        Log.i(TAG, "---read file, file = " + file);
        return content;
    }

    public static void safeClose(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                Log.e(TAG, "---error = " + e.getMessage());
            }
        }
    }

    public static boolean saveBitmap(Bitmap bitmap, String filePath) {
        boolean success = false;
        if (bitmap != null && !TextUtils.isEmpty(filePath)) {
            FileOutputStream out = null;
            try {
                File file = new File(filePath);
                if (file.exists()) {
                    Log.e(TAG, "saveBitmap: bitmap exists");
                    return false;
                }
                File parentFile = file.getParentFile();
                if (parentFile !=null && !parentFile.exists()) {
                    boolean mkDirsResult = parentFile.mkdirs();
                    Log.d(TAG, "saveBitmap: mkdirsResult = " + mkDirsResult);
                }
                if (!file.exists()) {
                    boolean createFileResult = file.createNewFile();
                    Log.d(TAG, "saveBitmap: createFileResult = " + createFileResult);
                }
                out = new FileOutputStream(file);
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
                success = true;
                Log.e(TAG, "saveBitmap: 保存成功: filePath = " + filePath);
            } catch (Exception e) {
                Log.e(TAG, "saveBitmap: save fail --> " + e.getMessage());
            } finally {
                safeClose(out);
            }
        }
        return success;
    }

    public static void saveDataToFile(String fileNamePath, String data) {
        BufferedWriter writer = null;
        File file = new File(fileNamePath);
        //如果文件不存在，则新建一个
        File parentFile = file.getParentFile();
        if (parentFile != null && !parentFile.exists()) {
            boolean mkDirsResult = parentFile.mkdirs();
            Log.d(TAG, "saveDataToFile: mkdirsResult = " + mkDirsResult);
        }
        if (!file.exists()) {
            try {
                boolean createFileResult = file.createNewFile();
                Log.d(TAG, "saveDataToFile: createFileResult = " + createFileResult);
            } catch (IOException e) {
                Log.e(TAG, "---error = " + e.getMessage());
            }
        }
        //写入
        try {
            writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file, false), StandardCharsets.UTF_8));
            writer.write(data);
        } catch (IOException e) {
            Log.e(TAG, "---error = " + e.getMessage());
        } finally {
            try {
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "---error = " + e.getMessage());
            }
        }
        Log.d(TAG, "file write success！");
    }

    public static void deleteFolder(File fileOrDirectory) {
        if (fileOrDirectory.isDirectory()) {
            File[] files = fileOrDirectory.listFiles();
            if (files == null) {
                Log.e(TAG, "deleteFolder: listFiles is null! file=" + fileOrDirectory.getAbsolutePath());
                return;
            }
            for (File child : files) {
                deleteFolder(child);
            }
        }
        boolean result = fileOrDirectory.delete();
        if (!result) {
            Log.e(TAG, "deleteFolder: delete file failed! file=" + fileOrDirectory.getAbsolutePath());
        }
    }
}
