package com.xiaopeng.xpautotest.accessibility;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.app.ActivityOptions;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import androidx.annotation.RequiresApi;
import android.provider.Settings;
import android.view.accessibility.AccessibilityManager;
import android.view.accessibility.AccessibilityNodeInfo;
import android.graphics.Rect;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.system.ScreenCapture;
import com.xiaopeng.executor.action.accessibility.GestureCallback;
import java.util.List;
import java.util.Locale;
import java.util.ArrayList;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.screen.ScreenManager;



public class AccessibilityHelper {
    private static String TAG = "AccessibilityHelper";
    private static AccessibilityHelper instance;
    private Context context;
    private AccessibilityManager mAccessibilityManager;
    private BaseAccessibilityService service;

    public static final String ACCESSIBILITY_SWITCH = "accessibility_switch";
    public static final int ENABLE = 1;
    public static final int DISABLE = 0;
    // 屏幕管理器
    private ScreenManager screenManager;

    public static final String GESTURE_TYPE_CLICK = "CLICK";
    public static final String GESTURE_TYPE_DRAG = "DRAG";
    private static final int DEFAULT_DRAG_DURATION_MS_INTERNAL = 300;

    // 私有构造方法
    private AccessibilityHelper(Context context) {
        this.context = context;//.getApplicationContext();
        mAccessibilityManager = (AccessibilityManager) context.getSystemService(Context.ACCESSIBILITY_SERVICE);

        // 获取屏幕管理器实例（在bindService时完成完整初始化）
        this.screenManager = ScreenManager.getInstance();
        this.screenManager.initialize(context);
    }

    // 单例模式
    public static synchronized AccessibilityHelper getInstance() {
        if (instance == null) {
            FileLogger.e(TAG, "AccessibilityHelper is null");
        }
        return instance;
    }

    public static synchronized AccessibilityHelper getInstance(Context context) {
        if (instance == null) {
            instance = new AccessibilityHelper(context);
        }
        return instance;
    }

    // 绑定服务实例
    public void bindService(BaseAccessibilityService service) {
        this.service = service;
        this.service.init(context);
        FileLogger.i(TAG, "bindService " + this.service);



        screenManager.updateScreenSize(screenManager.getCurrentScreenId());
    }

    // 检查服务是否启用
    public boolean isServiceEnabled(String serviceName) {
//        ActivityManager activityManager = (ActivityManager)this.context.getSystemService(Context.ACTIVITY_SERVICE);
//        List<ActivityManager.RunningServiceInfo>
//                runningServices = activityManager.getRunningServices(100);
//
//        for (int i = 0;i<runningServices.size();i++){
//            ComponentName service = runningServices.get(i).service;
//            if (service.getClassName().contains(BaseAccessibilityService.class.getName())){
//                FileLogger.i(TAG,"AccessibilityService is running");
//                return true;
//            }
//        }
//        FileLogger.e(TAG,"AccessibilityService is not running");
//        return false;
        List<AccessibilityServiceInfo> accessibilityServices =
//                mAccessibilityManager.getInstalledAccessibilityServiceList();
        mAccessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK);
        FileLogger.i(TAG, "accessibilityServices:" + accessibilityServices);
        for (AccessibilityServiceInfo info : accessibilityServices) {
            FileLogger.d(TAG, "info:" + info);
            if (info.getId().equals(serviceName)) {
                FileLogger.i(TAG, "AccessibilityService is running: " + serviceName);
                return true;
            }
        }
        FileLogger.i(TAG,"AccessibilityService is not running, will enable it");
        return false;
    }

    // 跳转到无障碍设置界面
    public void openAccessibilitySettings() {
        Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    /**
     * 直接启用无故障服务
     */
    public void enableAccessibilityService(String serviceName) {
        String enabledServices = Settings.Secure.getString(
                context.getContentResolver(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
        );
        if (enabledServices == null || enabledServices.isEmpty()) {
            enabledServices = serviceName;
        } else if (!enabledServices.contains(serviceName)) {
            enabledServices = enabledServices + ":" + serviceName;
        }
        boolean service_enable_success = Settings.Secure.putString(
                context.getContentResolver(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                enabledServices
        );

        boolean switch_enable_success = Settings.Global.putInt(context.getContentResolver(),
                ACCESSIBILITY_SWITCH, ENABLE);
        FileLogger.i(TAG, "enable AccessibilityService for " + serviceName+
                ", service_enable_success: " + service_enable_success +
                ", switch_enable_success: " + switch_enable_success);
    }

    /**
     * 获取当前运行的Activity
     * */
    public String getCurrentActivity() {
        if (service == null) {
            FileLogger.e(TAG, "BaseAccessibilityService is not bound to AccessibilityHelper (service is null) in getCurrentActivity.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            return null;
        }
        return service.getCurrentActivity();
    }
    public String getCurrentToast() {
        if (service == null) {
            FileLogger.e(TAG, "BaseAccessibilityService is not bound to AccessibilityHelper (service is null) in getCurrentToast.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            return null;
        }
        return service.getCurrentToast();
    }

    public boolean startPackage(String packageName, double waitTime) {
//        FileLogger.i(TAG, "begin startPackage " + packageName);

        String currentActivityName = getCurrentActivity();
        if (currentActivityName == null) {
            FileLogger.e(TAG, "currentActivityName is null");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
            return false;
        }
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(packageName);
        if (intent == null) {
            FileLogger.e(TAG,"getLaunchIntentForPackage returned null for package: " + packageName);
            FailureContextHolder.setFailureIfNotSet(FailureCode.EI009);
            return false;
        }
        ComponentName component = intent.getComponent();
        if (component == null) {
            FileLogger.e(TAG, "getComponent returned null for package: " + packageName);
            FailureContextHolder.setFailureIfNotSet(FailureCode.EI010);
            return false;
        }
        String targetActivityName = component.getClassName();
        FileLogger.d(TAG, "targetActivityName = " + targetActivityName + " , currentActivityName = " + currentActivityName);
        if (targetActivityName.equals(currentActivityName)) {
            FileLogger.i(TAG,"already in " + packageName + ", focusing on current activity");
            // 将无障碍服务的焦点转到当前屏幕的Activity
            if (service != null) {
                service.focusOnCurrentActivity(screenManager.getCurrentScreenId());
            }
            return true;
        }

        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        boolean success = startActivityOnCurrentScreen(intent, "package: " + packageName);
        if (!success) {
            return false;
        }
        // 等待一段时间，确保Activity启动完成
        if (waitTime > 0) {
            SystemClock.sleep((long) (waitTime * 1000));
        }
        return true;
    }

    public boolean startActivity(String packageName, String activityName, double waitTime) {
//        FileLogger.i(TAG, "begin startActivity " + packageName);
        String currentActivityName = getCurrentActivity();

        Intent intent = new Intent();
        intent.setComponent(new ComponentName(
                packageName,          // 包名
                activityName // Activity 类名
        ));
        FileLogger.d(TAG, "targetActivityName = " + activityName + " , currentActivityName = " + currentActivityName);
        if (activityName.equals(currentActivityName)) {
            FileLogger.i(TAG,"already in " + packageName + ", focusing on current activity");
            // 将无障碍服务的焦点转到当前屏幕的Activity
            if (service != null) {
                service.focusOnCurrentActivity(screenManager.getCurrentScreenId());
            }
            return true;
        }
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        boolean success = startActivityOnCurrentScreen(intent, "activity: " + packageName + "/" + activityName);
        if (!success) {
            return false;
        }
        // 等待一段时间，确保Activity启动完成
        if (waitTime > 0) {
            SystemClock.sleep((long) (waitTime * 1000));
        }
        return true;
    }
    /**
     * 在当前屏幕启动Activity的公共方法
     *
     * 根据当前屏幕ID和Android版本，选择合适的启动方式：
     * - 如果是默认屏幕(screenId=0)：使用普通启动方式
     * - 如果是非默认屏幕且Android版本支持多屏(>=26)：使用ActivityOptions.setLaunchDisplayId启动
     * - 其他情况：返回失败
     *
     * @param intent 要启动的Intent
     * @param description 描述信息，用于日志记录
     * @return 是否成功启动
     */
    private boolean startActivityOnCurrentScreen(Intent intent, String description) {
        int currentScreenId = screenManager.getCurrentScreenId();

        if (currentScreenId == 0) {
            // 默认屏幕，使用普通启动方式，兼容原逻辑
            context.startActivity(intent);
            FileLogger.i(TAG, "Started " + description + " on default display");
            return true;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 非默认屏幕且Android版本支持多屏，使用多屏启动
            ActivityOptions options = ActivityOptions.makeBasic();
            options.setLaunchDisplayId(currentScreenId);
            //context.startActivity(intent, options.toBundle());
            com.xiaopeng.screen.ScreenTaskManagerFactory.get().startActivity(context,intent,currentScreenId,options.toBundle());
            FileLogger.i(TAG, "Started " + description + " on screenId: " + currentScreenId);
            return true;
        } else {
            // 非默认屏幕但Android版本不支持多屏，返回失败
            FileLogger.e(TAG, "Cannot start " + description + " on screenId: " + currentScreenId +
                    ". Multi-screen launch not supported on Android version " + Build.VERSION.SDK_INT +
                    " (requires API " + Build.VERSION_CODES.O + "+)");
            return false;
        }
    }

    public boolean goToHome() {
        if (service == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            FileLogger.e(TAG, "AccessibilityService is null");
            return false;
        }
        return service.performHomeClick();
    }

    // 通过 ID 查找控件
    public AccessibilityNodeInfo findNodeById(String id) {
        if (service == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            FileLogger.e(TAG, "AccessibilityService is null");
            return null;
        }
        return service.findViewByID(id, screenManager.getCurrentScreenId());
    }

    // 通过文本查找控件
    public AccessibilityNodeInfo findNodeByText(String text) {
        if (service == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            FileLogger.e(TAG, "AccessibilityService is null");
            return null;
        }
        return service.findViewByText(text,false,screenManager.getCurrentScreenId());

    }


    public AccessibilityNodeInfo findNodeByTextContains(String text) {
        if (service == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            FileLogger.e(TAG, "AccessibilityService is null");
            return null;
        }
        return service.findViewByTextContains(text, false, screenManager.getCurrentScreenId());
    }

    // 点击坐标
    public boolean click(int x, int y) {
        x = (int) (screenManager.getScreenWidth() * x / 100);
        y = (int) (screenManager.getScreenHeight() * y / 100);
        return service.performClick(x, y, screenManager.getCurrentScreenId());
    }

    // 点击坐标，绝对坐标
    public boolean clickAbs(int x, int y) {
        return service.performClick(x, y, screenManager.getCurrentScreenId());
    }

    // 递归查找可点击的节点
    public boolean findClickableChildNodeAndClick(AccessibilityNodeInfo rootNode) {
        if (rootNode == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return false;
        }
        // 检查当前节点是否可点击，可点击则直接尝试点击
        if (rootNode.isClickable()) {
            FileLogger.i(TAG, "findClickableChildNodeAndClick: node = " + rootNode);
            boolean result = rootNode.performAction(AccessibilityNodeInfo.ACTION_CLICK);
            if (!result){
                FileLogger.e(TAG, "findClickableChildNodeAndClick: Failed to click on node: " + rootNode);
                FailureContextHolder.setFailureIfNotSet(FailureCode.AB003);
                return false;
            }
            return true;
        }
        // 遍历子节点
        int childCount = rootNode.getChildCount();
        for (int i = 0; i < childCount; i++) {
            AccessibilityNodeInfo childNode = rootNode.getChild(i);
            if (childNode == null) continue;
            // 递归检查子节点
            boolean isChildClickSuccess = findClickableChildNodeAndClick(childNode);
            // 子节点点击成功则返回，否则继续遍历
            if (isChildClickSuccess) {
                childNode.recycle();
                return true;
            }
            childNode.recycle();
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        return false;
    }

    // 点击控件
    public boolean clickNode(AccessibilityNodeInfo node) {
        if (node == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.EI002);
            return false;
        }

        if (node.isClickable()) {
            boolean success = node.performAction(AccessibilityNodeInfo.ACTION_CLICK);
            if (!success) {
                FailureContextHolder.setFailureIfNotSet(FailureCode.AB003);
            }
            return success;
        } else {
            // 尝试查找可点击的子节点
            boolean result = findClickableChildNodeAndClick(node);
            if (result) return true;

            // 尝试查找可点击的父节点
            AccessibilityNodeInfo parent = node.getParent();
            if (parent != null) {
                boolean success = parent.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                parent.recycle();
                if (success) return true;
            }

            FailureContextHolder.setFailureIfNotSet(FailureCode.EI002);
            return false;
        }
    }

    /**
     * 按照文本点击控件
     * */
    public boolean clickNodeByText(String text){
        AccessibilityNodeInfo node = findNodeByText(text, null, true);
        if (node != null) {
            boolean result = clickNode(node);
            node.recycle();
            return result;
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        FileLogger.i(TAG, "clickNodeByText: node is null for text: " + text);
        return false;
    }

    /**
     * 异步点击指定坐标
     * 只执行点击操作，不包含元素查找
     * */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public void performClickAsync(int x, int y, GestureCallback callback) {
        if (service == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            FileLogger.e(TAG, "AccessibilityService is null");
            if (callback != null) {
                callback.onFailure("AccessibilityService is null");
            }
            return;
        }

        FileLogger.i(TAG, "performClickAsync: x=" + x + ", y=" + y);
        // 统一使用带displayId的方法
        int currentScreenId = screenManager.getCurrentScreenId();
        service.performClickAsync(x, y, currentScreenId, callback);
    }

    /**
     * 按照文本查找控件
     * @param text 文本内容
     * @param isClickable 是否可点击
     * @param isVisibleToUser 是否对用户可见
     * @return 返回第一个匹配的节点，或者 null 如果没有找到
     */
    public AccessibilityNodeInfo findNodeByText(String text, Boolean isClickable, Boolean isVisibleToUser) {
        if (service == null) {
            FileLogger.e(TAG, "AccessibilityService is null");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            return null;
        }
        List<AccessibilityNodeInfo> candidates = service.findViewsByText(text, screenManager.getCurrentScreenId());
        if (candidates == null || candidates.isEmpty()) {
            FileLogger.e(TAG, "findNodeByText: No nodes found for text: " + text);
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return null;
        }
        List<AccessibilityNodeInfo> matchingNodes = new ArrayList<>();

        for (AccessibilityNodeInfo node : candidates) {
            if (node == null) continue;

            boolean matches = true;
            if (isClickable != null && node.isClickable() != isClickable) {
                matches = false;
            }
            if (isVisibleToUser != null && node.isVisibleToUser() != isVisibleToUser) {
                matches = false;
            }

            if (matches) {
                matchingNodes.add(node);
            } else {
                node.recycle();
            }
        }

        if (matchingNodes.isEmpty()) {
            FileLogger.w(TAG, "findNodeByText: No matching nodes found for text: " + text +
                    ", isClickable: " + isClickable + ", isVisibleToUser: " + isVisibleToUser);
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return null;
        }
        // 如果有多个匹配的节点，选择第一个作为最佳候选，前序遍历，从上到下，从左到右
        AccessibilityNodeInfo bestCandidate = matchingNodes.get(0);

        for (int i = 1; i < matchingNodes.size(); i++) {
            matchingNodes.get(i).recycle();
        }

        return bestCandidate;
    }

    /**
     * 按照文本包含内容点击控件
     * */
    public boolean clickNodeByTextContains(String text) {
        if (service == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            FileLogger.w(TAG,"clickNodeByTextContains: AccessibilityService is null");
            return false;
        }
        // 使用当前屏幕ID进行查找
        int currentScreenId = screenManager.getCurrentScreenId();
        AccessibilityNodeInfo node = service.findViewByTextContains(text, true, currentScreenId);
        if (node != null) {
            boolean result = clickNode(node);
            node.recycle();
            return result;
        }

        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        FileLogger.w(TAG,"clickNodeByTextContains: node is null");
        return false;
    }

    /**
     * 按照 ID 点击控件
     * */
    public boolean clickNodeById(String id) {
        AccessibilityNodeInfo node = findNodeById(id);
        if (node != null) {
            boolean result = clickNode(node);
            node.recycle();
            return result;
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        FileLogger.w(TAG,"clickNodeById: node is null");
        return false;
    }

    public boolean clickNodeByRPath(String rPath) {
        FileLogger.i(TAG,"clickNodeByRPath: rPath = " + rPath);
        AccessibilityNodeInfo node = findNodeByRPath(rPath);
        if (node == null) {
            FileLogger.e(TAG,"clickNodeByRPath: node is null");
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return false;
        }
        return clickNode(node);
    }

    private AccessibilityNodeInfo findNodeByRPath(String rPath) {
        String[] rPathArray = rPath.split("/");
        if (rPathArray.length < 3) {
            FileLogger.e(TAG,"clickNodeByRPath: rPath is invalid");
            FailureContextHolder.setFailureIfNotSet(FailureCode.SI001);
            return null;
        }
        String rootNodeId = rPathArray[0] + "/" + rPathArray[1];
        AccessibilityNodeInfo rootNode = findNodeById(rootNodeId);
        if (rootNode == null) {
            FileLogger.e(TAG,"findNodeByRPath: node is null");
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return null;
        }
        rPath = rPath.substring(rootNodeId.length() + 1); // 去掉根节点部分
        String[] pathArray = rPath.split("/");
        return searchNodeByDFS(rootNode, pathArray, 0);
    }

    private AccessibilityNodeInfo searchNodeByDFS(AccessibilityNodeInfo node, String[] rPathArray, int index) {
        if (node == null || rPathArray == null || rPathArray.length == 0) {
            FileLogger.e(TAG,"node or rPathArray is null or empty");
            return null;
        };
        String currentRPath = rPathArray[index];
        int currentRPathIndex;
        if (currentRPath.contains("[") && currentRPath.contains("]")) {
            currentRPathIndex = Integer.parseInt(currentRPath.substring(currentRPath.indexOf("[") + 1, currentRPath.indexOf("]")));
            currentRPath = currentRPath.substring(0, currentRPath.indexOf("["));
        } else {
            currentRPathIndex = 1;
        }
        // 如果当前节点的类名不匹配，直接返回null
        int childCount = node.getChildCount();
        FileLogger.d(TAG,"currentRPath = " + currentRPath + ", currentRPathIndex = " + currentRPathIndex + ", index = " + index + ", nodeName = " + node.getClassName() + ", childCount = " + childCount);
        int targetWidgetIndex = 0;
        for (int i = 0; i < childCount; i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child == null) continue;
            // 跳过不匹配的节点
            if (!node.getChild(i).getClassName().toString().equals(currentRPath)) {
                FileLogger.d(TAG,"currentRPath does not match this: " + node.getChild(i).getClassName());
                child.recycle();
                continue;
            }
            // 计算匹配的当前节点的索引
            targetWidgetIndex += 1;
            // 如果当前节点的索引与rPathIndex匹配，继续递归查找下一个节点
            if (targetWidgetIndex == currentRPathIndex) {
                // 如果已经到达最后一个节点，返回当前节点
                if (index == rPathArray.length - 1) {
                    FileLogger.i(TAG,"reached last node in currentRPath: " + currentRPath + ", index = " + index);
                    // 如果已经到达最后一个节点，返回当前节点
                    return child;
                }
                FileLogger.d(TAG,"currentRPath matches, continue to next node" + ", index = " + index);
                // 如果当前节点的类名匹配，继续递归查找下一个节点
                AccessibilityNodeInfo result = searchNodeByDFS(child, rPathArray, index + 1);
                // 如果递归查找成功，回收当前节点
                child.recycle();
                return result;
            }
        }
        node.recycle();
        if (targetWidgetIndex < currentRPathIndex) {
            FileLogger.e(TAG,"currentRPathIndex is out of bounds for currentRPath: " + currentRPath + ", targetWidgetIndex: " + targetWidgetIndex);
            return null;
        }
        FileLogger.e(TAG,"currentRPath does not match");
        return null;
    }

    public String getTextById(String id) {
        AccessibilityNodeInfo node = findNodeById(id);
        if (node == null) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            FileLogger.w(TAG,"getTextById: node is null");
            return "";
        }
        String text = node.getText() != null ? node.getText().toString() : "";
        node.recycle();
        FileLogger.i(TAG,"getTextById: id = " + id + ", text = " + text);
        return text;
    }

    public boolean inputText(String id, String text) {
        AccessibilityNodeInfo node = findNodeById(id);
        if (node != null) {
            boolean result = inputText(node, text);
            node.recycle();
            return result;
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        FileLogger.w(TAG,"inputText: node is null");
        return false;
    }



    // 输入文本
    public boolean inputText(AccessibilityNodeInfo node, String text) {
        if (node == null || !node.isEditable()){
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return false;
        }
        Bundle args = new Bundle();
        args.putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text);
        boolean result = node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, args);
        if (!result) {
            FileLogger.w(TAG, "inputText: Failed to set text on node: " + node);
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB003);
            return false;
        }
        return true;
    }

    // 滑动列表
    public boolean scrollListView(AccessibilityNodeInfo listNode) {
        if (listNode == null || !listNode.isScrollable()) return false;
        return listNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD);
    }

    // 单点滑动
    public boolean swipe(int startX, int startY, int endX, int endY, int duration) {
        if (service == null) return false;
        startX = (int) (screenManager.getScreenWidth() * startX / 100);
        startY = (int) (screenManager.getScreenHeight() * startY / 100);
        endX = (int) (screenManager.getScreenWidth() * endX / 100);
        endY = (int) (screenManager.getScreenHeight() * endY / 100);

        // 统一使用带displayId的方法
        int currentScreenId = screenManager.getCurrentScreenId();
        return service.performSwipe(startX, startY, endX, endY, duration, currentScreenId);
    }
    // 模拟滑动，绝对坐标
    public boolean swipeAbs (int startX, int startY, int endX, int endY,int duration) {
        return service.performSwipe(startX, startY, endX, endY, duration, screenManager.getCurrentScreenId());
    }

    // 模拟点击坐标点
    public boolean tap(int x, int y) {
        return swipe(x, y, x, y, 100);
    }

    /**
     * 异步执行长按操作
     * @param xPercent X坐标百分比
     * @param yPercent Y坐标百分比
     * @param duration 长按持续时间
     * @param callback 手势执行回调
     */
    public void longClickAsync(int xPercent, int yPercent, int duration, GestureCallback callback) {
        if (service == null) {
            FileLogger.w(TAG, "AccessibilityService is null. Cannot perform long click.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            if (callback != null) {
                callback.onFailure("AccessibilityService is null");
            }
            return;
        }
        int x = (int) (screenManager.getScreenWidth() * xPercent / 100);
        int y = (int) (screenManager.getScreenHeight() * yPercent / 100);

        // 统一使用带displayId的方法
        int currentScreenId = screenManager.getCurrentScreenId();
        service.performLongClickAsync(x, y, duration, currentScreenId, callback);
    }

    // 等待控件出现（超时时间单位：毫秒）
    public AccessibilityNodeInfo waitForNodeById(String id, long timeout) {
        long endTime = System.currentTimeMillis() + timeout * 1000;
        while (System.currentTimeMillis() < endTime) {
            AccessibilityNodeInfo node = findNodeById(id);
            if (node != null) return node;
            SystemClock.sleep(300);
        }

        FailureContextHolder.setFailureIfNotSet(FailureCode.EI002);
        return null;
    }

    // 等待文本出现
    public AccessibilityNodeInfo waitForNodeByText(String text, long timeout) {
        long endTime = System.currentTimeMillis() + timeout * 1000;
        while (System.currentTimeMillis() < endTime) {
            AccessibilityNodeInfo node = findNodeByText(text);
            if (node != null) return node;
            SystemClock.sleep(300);
        }

        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        return null;
    }

    public AccessibilityNodeInfo waitForNodeByTextContains(String text, long timeout) {
        long endTime = System.currentTimeMillis() + timeout * 1000;
        while (System.currentTimeMillis() < endTime) {
            AccessibilityNodeInfo node = findNodeByTextContains(text);
            if (node != null) return node;
            SystemClock.sleep(300);
        }
        FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
        return null;
    }

    /**
     * 查找节点
     * @param targetType 查找类型（如 "id"、"text"）
     * @param targetValue 查找值
     * @return 返回找到的节点，如果未找到则返回 null
     */
    public AccessibilityNodeInfo findNode(String targetType, String targetValue) {
        if (service == null) {
            FileLogger.w(TAG, "AccessibilityService is null, cannot find node.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            return null;
        }
        if (targetType == null || targetValue == null) {
            FileLogger.w(TAG, "targetType or targetValue is null. Cannot find node.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.SI001);
            return null;
        }

        AccessibilityNodeInfo node = null;
        if ("id".equalsIgnoreCase(targetType)) {
            node = findNodeById(targetValue);
        } else if ("text".equalsIgnoreCase(targetType)) {
            node = findNodeByText(targetValue);
        } else {
            FileLogger.w(TAG, "Unsupported targetType: " + targetType);
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
        }
        return node;
    }

    public void safeRecycle(AccessibilityNodeInfo... nodes) {
        for (AccessibilityNodeInfo node : nodes) {
            if (node != null) {
                try {
                    node.recycle();
                } catch (IllegalStateException e) {
                    // 忽略已回收的节点
                }
            }
        }
    }

    /**
     * 查找节点并尝试确保其在屏幕上可见。
     * 如果找到节点但不可见，将尝试使用 ACTION_SHOW_ON_SCREEN 操作。
     *
     * @param targetType        用于查找节点的标识符类型（如 "id"、"text"）
     * @param targetValue       标识符对应的值
     * @return 若找到节点则返回 {@link AccessibilityNodeInfo},未找到返回 null，
     *         。
     */
    public AccessibilityNodeInfo findNodeAndEnsureVisible(String targetType, String targetValue) {
        AccessibilityNodeInfo currentNode = findNode(targetType, targetValue);

        if (currentNode != null) {
            if (currentNode.isVisibleToUser()) {
                return currentNode;
            }
            // 如果node不可见, 尝试使用 ACTION_SHOW_ON_SCREEN使其可见
            if (currentNode.getActionList().contains(AccessibilityNodeInfo.AccessibilityAction.ACTION_SHOW_ON_SCREEN)) {
                currentNode.performAction(AccessibilityNodeInfo.AccessibilityAction.ACTION_SHOW_ON_SCREEN.getId());
                SystemClock.sleep(500);
            } else {
                FileLogger.d(TAG, "findNodeAndEnsureVisible: Node found but not visible, and ACTION_SHOW_ON_SCREEN not supported.");
            }
            return currentNode;
        } else {
            FileLogger.i(TAG, String.format("findNodeAndEnsureVisible: Element not found with type '%s' and value '%s'", targetType, targetValue));
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return null;
        }
    }

    /**
     * 异步截图，适用于 Android 11 及以上版本。
     * @param timeStampString 时间戳字符串，用于生成截图文件名
     * @return 如果服务可用则返回 true，否则返回 false
     */
    public boolean screenShotAsyn(String timeStampString) {
        if (service == null) return false;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            service.screenshot(screenManager.getScreenWidth(), screenManager.getScreenHeight(), timeStampString);
        } else {
            // 不可用
            ScreenCapture screenCapture = new ScreenCapture(context);
            screenCapture.startCapture(timeStampString);
        }
        return true;
    }

    /**
     * 同步截图 - 在当前屏幕上截图
     * @param timeStampString 时间戳字符串，用于生成截图文件名
     * @return 返回截图文件的路径，如果服务不可用则返回空字符串
     */
    public String screenShotSyn(String timeStampString) {
        if (service == null){
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            return "";
        }

        // 使用缓存的物理displayId进行截图
        long physicalDisplayId = screenManager.getCachedPhysicalDisplayId();
        String screenName = screenManager.getCurrentScreenName();
        FileLogger.i(TAG, "Taking screenshot on current screen (screenId: " + screenManager.getCurrentScreenId() +
                    ", physical displayId: " + physicalDisplayId + ", screenName: " + screenName + ")");
        return ScreenCapture.screenCap(timeStampString, screenManager.getScreenWidth(), screenManager.getScreenHeight(), (int) physicalDisplayId, screenName);
    }


    // 截图并保存UI结构 - 在当前屏幕上操作
    public TestResult.ActionArtifacts screenShotandDumpUI(String timeStampString) throws ActionException {
        // 如果时间戳没传进来那就生成一个
        if (timeStampString == null) {
           timeStampString = String.valueOf(System.currentTimeMillis());
        }
        int currentScreenId = screenManager.getCurrentScreenId();
        // 截图（会自动使用当前屏幕）
        String imgFileName = screenShotSyn(timeStampString);
        if (imgFileName != null && !imgFileName.isEmpty()) {
            FileLogger.i(TAG, "Screenshot successful: " + imgFileName);
        } else {
            FileLogger.e(TAG, "Screenshot failed on current screen");
        }

        String uiDumpFileName = null;
        if (this.service != null) {
            // UI Dump使用当前屏幕ID和屏幕名称
            String screenName = screenManager.getCurrentScreenName();
            uiDumpFileName = this.service.performUiDump(timeStampString, screenManager.getScreenWidth(), screenManager.getScreenHeight(), currentScreenId, screenName);
            if (uiDumpFileName == null || uiDumpFileName.isEmpty()) {
                FileLogger.e(TAG, "UI dump failed on current screen");
            }
        } else {
            FileLogger.e(TAG, "AccessibilityService instance is null. Cannot perform UI Dump.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
        }

        return new TestResult.ActionArtifacts(imgFileName, uiDumpFileName);
    }

    /**
     * 尝试将指定节点向屏幕中心滚动。
     * 该方法计算滑动手势的轨迹，旨在移动内容使节点更接近屏幕中心。
     *
     * @param nodeToScroll       需要居中的元素节点（AccessibilityNodeInfo 类型）
     * @param scrollDirectionHint 滚动方向提示："vertical"（垂直）或"horizontal"（水平）
     * @param durationMs         滑动手势持续时间（单位：毫秒）
     *
     * @return 若执行了滚动尝试返回 true，
     *         否则返回 false（如节点为空、屏幕尺寸无效等情况）
     */
    public boolean scrollNodeToCenter(AccessibilityNodeInfo nodeToScroll, String scrollDirectionHint, int durationMs) {
        if (nodeToScroll == null) {
            FileLogger.w(TAG, "scrollNodeToCenter: nodeToScroll is null.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return false;
        }

        if (screenManager.getScreenWidth() <= 0 || screenManager.getScreenHeight() <= 0) {
            FileLogger.e(TAG, "scrollNodeToCenter: Invalid screen dimensions. Cannot perform scroll.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
            return false;
        }
        Rect nodeBounds = new Rect();
        nodeToScroll.getBoundsInScreen(nodeBounds);

        if (nodeBounds.width() <= 0 && nodeBounds.height() <= 0 && nodeBounds.left == 0 && nodeBounds.top == 0) {
            FileLogger.w(TAG, "scrollNodeToCenter: Node has invalid bounds, cannot reliably center.");
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB004);
            return false;
        }

        int nodeCurrentCenterX = nodeBounds.centerX();
        int nodeCurrentCenterY = nodeBounds.centerY();

        int screenCenterX = screenManager.getScreenWidth() / 2;
        int screenCenterY = screenManager.getScreenHeight() / 2;
        int deltaX = screenCenterX - nodeCurrentCenterX;
        int deltaY = screenCenterY - nodeCurrentCenterY;

        int startXPercent = 0, startYPercent = 0, endXPercent = 0, endYPercent = 0;
        final int SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS = 40;
        final int MIN_DELTA_TO_SCROLL_PIXELS = 20; 

        if ("vertical".equalsIgnoreCase(scrollDirectionHint)) {
            if (Math.abs(deltaY) < MIN_DELTA_TO_SCROLL_PIXELS) {
                FileLogger.i(TAG, "scrollNodeToCenter (Vertical): Node is already close to vertical center. No scroll needed. DeltaY: " + deltaY);
                return false;
            }

            startXPercent = (screenManager.getScreenWidth() > 0) ? (nodeCurrentCenterX * 100) / screenManager.getScreenWidth() : 50;
            startXPercent = Math.max(0, Math.min(100, startXPercent)); 
            endXPercent = startXPercent;

            if (deltaY > 0) { // 节点位于中心上方，内容需向下移动，滑动手势方向：向下
                startYPercent = 50 - (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
                endYPercent = 50 + (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
            } else { // 节点位于中心下方，内容需要向上移动，滑动手势方向：向上
                startYPercent = 50 + (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
                endYPercent = 50 - (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
            }
        } else if ("horizontal".equalsIgnoreCase(scrollDirectionHint)) {
            if (Math.abs(deltaX) < MIN_DELTA_TO_SCROLL_PIXELS) {
                return false;
            }
            startYPercent = (screenManager.getScreenHeight() > 0) ? (nodeCurrentCenterY * 100) / screenManager.getScreenHeight() : 50;
            startYPercent = Math.max(0, Math.min(100, startYPercent)); 
            endYPercent = startYPercent;

            if (deltaX > 0) { // 节点位于中心左侧，内容需要向右移动，滑动手势方向：向右
                startXPercent = 50 - (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
                endXPercent = 50 + (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
            } else { // 节点位于中心右侧，内容需要向左移动，滑动手势方向：向左
                startXPercent = 50 + (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
                endXPercent = 50 - (SCROLL_VIEWPORT_PERCENTAGE_MAJOR_AXIS / 2);
            }
        }

        startXPercent = Math.max(0, Math.min(100, startXPercent));
        startYPercent = Math.max(0, Math.min(100, startYPercent));
        endXPercent = Math.max(0, Math.min(100, endXPercent));
        endYPercent = Math.max(0, Math.min(100, endYPercent));

        return this.swipe(startXPercent, startYPercent, endXPercent, endYPercent, durationMs);
    }
    /**
     * 通过手势操作设置进度值。
     * @param targetValue 控件的标识符（如进度条/滑块的resource-id）
     * @param percentageToSet 在节点宽度上的水平点击百分比（0.0至100.0）
     * @param gestureType 手势类型，支持 "CLICK" 或 "DRAG"
     * @param dragDurationOptional 可选的拖动持续时间（毫秒），仅在 gestureType 为 "DRAG" 时有效。
     * @return 如果操作成功返回 true，否则返回 false。
     */
    public boolean setProgressByGesture(String targetValue, float percentageToSet, String gestureType, int... dragDurationOptional) throws ActionException {
        AccessibilityNodeInfo node = null;
        String operationDescription = String.format(Locale.ROOT, "setProgressByGesture (type: %s) for node '%s' to %.2f%%", gestureType, targetValue, percentageToSet);

        try {
            if (service == null) {
                FileLogger.e(TAG, operationDescription + " - Error: BaseAccessibilityService is not bound.");
                FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
                return false;
            }
            node = findNode("id", targetValue);
            if (node == null) {
                FileLogger.w(TAG, operationDescription + " - Error: Node not found.");
                FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
                return false;
            }

            Rect boundsInScreen = new Rect();
            node.getBoundsInScreen(boundsInScreen);

            if (boundsInScreen.width() <= 0 || boundsInScreen.height() <= 0) {
                FileLogger.w(TAG, operationDescription + " - Error: Node has invalid bounds: " + boundsInScreen.toShortString());
                FailureContextHolder.setFailureIfNotSet(FailureCode.AB004);
                return false;
            }

            float clampedPercentage = Math.max(0.0f, Math.min(100.0f, percentageToSet));
            int targetX = boundsInScreen.left + (int) (boundsInScreen.width() * (clampedPercentage / 100.0f));
            int targetY = boundsInScreen.centerY();

            targetX = Math.max(boundsInScreen.left, Math.min(boundsInScreen.right - 1, targetX));
            targetY = Math.max(boundsInScreen.top, Math.min(boundsInScreen.bottom - 1, targetY));

            boolean success = false;
            if (GESTURE_TYPE_CLICK.equalsIgnoreCase(gestureType)) {
                FileLogger.i(TAG, operationDescription + " - Executing CLICK at (absX:" + targetX + ", absY:" + targetY + ")");
                success = service.performClick(targetX, targetY, screenManager.getCurrentScreenId());
                if (!success) {
                    FileLogger.e(TAG, operationDescription + " - Click action failed.");
                    FailureContextHolder.setFailureIfNotSet(FailureCode.AB003);
                }
            } else if (GESTURE_TYPE_DRAG.equalsIgnoreCase(gestureType)) {
                int startX = boundsInScreen.left;
                int startY = boundsInScreen.centerY(); 
                startY = Math.max(boundsInScreen.top, Math.min(boundsInScreen.bottom - 1, startY));

                int duration = DEFAULT_DRAG_DURATION_MS_INTERNAL;
                if (dragDurationOptional != null && dragDurationOptional.length > 0 && dragDurationOptional[0] > 0) {
                    duration = dragDurationOptional[0];
                }
                success = service.performSwipe(startX, startY, targetX, targetY, duration, screenManager.getCurrentScreenId());
                if (!success) {
                    FileLogger.e(TAG, operationDescription + " - Drag action failed.");
                    FailureContextHolder.setFailureIfNotSet(FailureCode.AB003);
                }
            }
            return success;

        } catch (Exception e) {
            FileLogger.e(TAG, operationDescription + " - Exception: " + e.getMessage());
            throw  new ActionException(e.getMessage(), FailureCode.AB001, e);
        } finally {
            if (node != null) {
                node.recycle();
            }
        }
    }
}
