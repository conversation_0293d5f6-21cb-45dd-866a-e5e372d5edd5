package com.xiaopeng.executor.action.variable;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.VariableContext;
import com.xiaopeng.executor.core.VariableResolver;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.lib.feature.XpFeature;


/**
 * 特性配置获取动作
 *
 * 实现GetValueByFeatrue关键字：从XpFeature获取配置值并赋给变量
 * 语法：GetValueByFeatrue VARIABLE_NAME "config_key"
 */
public class GetValueByFeatureAction extends BaseAction {

    private static final String TAG = "GetValueByFeatrueAction";
    
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        VariableContext variableContext = getVariableContext(context);

        String variableName = getRequiredStringParam(context, "Variable name");
        String configKey = processStringValue(context.getStringParam());

        validateStringParam(configKey, "Config key");

        if (!VariableResolver.isValidVariableName(variableName)) {
            throw new ActionException("Invalid variable name: " + variableName, FailureCode.SI001);
        }

        String configValue = getConfigValue(configKey);
        variableContext.setValue(variableName, configValue);

        String message = "Variable '" + variableName + "' = '" + configValue + "' from config '" + configKey + "'";
        FileLogger.i(TAG, message);
        return TestResult.success(message);
    }

    /**
     * 从XpFeature获取配置值
     */
    private String getConfigValue(String configKey) {
        try {
            // 直接使用XpFeature API
            boolean value = XpFeature.getBoolean(configKey);
            return String.valueOf(value);
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to get config: " + configKey, e);
            return "";
        }
    }
}
