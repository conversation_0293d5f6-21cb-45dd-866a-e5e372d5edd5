package com.xiaopeng.xpautotest.community.test;

public class SimpleFailureReasonInferrer implements FailureReasonInferrer {

    @Override
    public FailureReasonDetail inferFailureReason(String actionName, String message, Throwable exception) {
        // 只基于关键词的简单匹配，默认为APK BUG
        FailureCode code = inferFromMessage(message);
        return new FailureReasonDetail(code, message, exception);
    }
    
    private FailureCode inferFromMessage(String message) {
        if (message == null) {
            return FailureCode.AB001; // 默认APK BUG
        }

        String lowerMessage = message.toLowerCase();
        // 环境问题关键词
        if (containsAny(lowerMessage, "permission")) {
            return FailureCode.EI007;
        }

        // 默认为APK BUG
        return FailureCode.AB001; // 数据处理异常
    }
    
    private boolean containsAny(String text, String... keywords) {
        for (String keyword : keywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
}
