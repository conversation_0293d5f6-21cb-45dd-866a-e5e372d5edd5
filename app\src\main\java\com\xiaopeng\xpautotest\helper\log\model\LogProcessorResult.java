package com.xiaopeng.xpautotest.helper.log.model;

/**
 * 日志处理器结果类
 * 统一单个日志处理器的处理结果返回格式
 */
public class LogProcessorResult {
    private final boolean success;
    private final String ossPath;
    private final String localPath;
    private final String errorMessage;
    private final LogType logType;

    public enum LogType {
        FILE_LOG("FileLogger日志"),
        CDU_LOG("大屏日志");

        private final String description;

        LogType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    private LogProcessorResult(Builder builder) {
        this.success = builder.success;
        this.ossPath = builder.ossPath;
        this.localPath = builder.localPath;
        this.errorMessage = builder.errorMessage;
        this.logType = builder.logType;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getOssPath() {
        return ossPath;
    }

    public String getLocalPath() {
        return localPath;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public LogType getLogType() {
        return logType;
    }

    public static Builder success(LogType logType) {
        return new Builder().success(true).logType(logType);
    }

    public static Builder failure(LogType logType, String errorMessage) {
        return new Builder().success(false).logType(logType).errorMessage(errorMessage);
    }

    public static class Builder {
        private boolean success;
        private String ossPath;
        private String localPath;
        private String errorMessage;
        private LogType logType;

        private Builder() {}

        public Builder success(boolean success) {
            this.success = success;
            return this;
        }

        public Builder ossPath(String ossPath) {
            this.ossPath = ossPath;
            return this;
        }

        public Builder localPath(String localPath) {
            this.localPath = localPath;
            return this;
        }

        public Builder errorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }

        public Builder logType(LogType logType) {
            this.logType = logType;
            return this;
        }

        public LogProcessorResult build() {
            return new LogProcessorResult(this);
        }
    }

    @Override
    public String toString() {
        return "LogProcessorResult{" +
                "success=" + success +
                ", ossPath='" + ossPath + '\'' +
                ", localPath='" + localPath + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", logType=" + logType +
                '}';
    }
}
