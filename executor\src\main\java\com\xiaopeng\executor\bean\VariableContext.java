package com.xiaopeng.executor.bean;

import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 变量上下文
 * 
 * 负责管理测试脚本执行过程中的变量存储和访问：
 * 1. 全局变量存储：维护脚本执行期间的所有变量
 * 2. 线程安全：支持多线程环境下的变量访问
 * 3. 生命周期管理：支持变量的创建、更新、删除和清理
 */
public class VariableContext {
    
    private static final String TAG = "VariableContext";
    
    // 主变量存储，使用ConcurrentHashMap保证线程安全
    private final Map<String, String> variables = new ConcurrentHashMap<>();
    
    // 备份存储，用于异常恢复
    private final Map<String, String> backup = new ConcurrentHashMap<>();
    
    /**
     * 设置变量值
     * 
     * @param name 变量名
     * @param value 变量值
     */
    public void setValue(String name, String value) {
        if (name == null || name.trim().isEmpty()) {
            FileLogger.w(TAG, "Variable name is null or empty, ignoring setValue");
            return;
        }
        
        String oldValue = variables.get(name);
        variables.put(name, value);
        
        FileLogger.i(TAG, "Variable set: " + name + " = " + value + 
                    (oldValue != null ? " (previous: " + oldValue + ")" : " (new)"));
    }
    
    /**
     * 获取变量值
     * 
     * @param name 变量名
     * @return 变量值，如果变量不存在返回null
     */
    public String getValue(String name) {
        if (name == null || name.trim().isEmpty()) {
            FileLogger.w(TAG, "Variable name is null or empty, returning null");
            return null;
        }
        
        String value = variables.get(name);
        //FileLogger.i(TAG, "Variable get: " + name + " = " + value);
        return value;
    }
    
    /**
     * 检查变量是否存在
     * 
     * @param name 变量名
     * @return true表示变量存在
     */
    public boolean hasVariable(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        return variables.containsKey(name);
    }
    
    /**
     * 清空所有变量
     */
    public void clear() {
        int count = variables.size();
        variables.clear();
        //FileLogger.i(TAG, "All variables cleared (" + count + " variables removed)");
    }

    /**
     * 获取变量数量
     * 
     * @return 当前变量数量
     */
    public int size() {
        return variables.size();
    }
    
    /**
     * 检查是否为空
     * 
     * @return true表示没有任何变量
     */
    public boolean isEmpty() {
        return variables.isEmpty();
    }
    
    /**
     * 创建变量状态检查点（备份当前状态）
     */
    public void createCheckpoint() {
        backup.clear();
        backup.putAll(variables);
        FileLogger.i(TAG, "Checkpoint created with " + backup.size() + " variables");
    }
    
    /**
     * 回滚到上一个检查点
     */
    public void rollback() {
        variables.clear();
        variables.putAll(backup);
        FileLogger.i(TAG, "Rolled back to checkpoint with " + variables.size() + " variables");
    }

    /**
     * 获取变量信息摘要（用于调试）
     * 
     * @return 变量信息摘要字符串
     */
    public String getSummary() {
        if (variables.isEmpty()) {
            return "No variables defined";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Variables (").append(variables.size()).append("): ");
        
        int count = 0;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            if (count > 0) {
                sb.append(", ");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
            count++;
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "VariableContext{" +
                "variables=" + variables.size() +
                ", backup=" + backup.size() +
                '}';
    }
}
