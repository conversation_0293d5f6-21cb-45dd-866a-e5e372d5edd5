package com.xiaopeng.xpautotest.client;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class FileDownloader {

    public interface DownloadCallback {
        void onSuccess(String localFilePath);
        void onFailure(Exception e);
    }

    public static void downloadFileAsync(String fileUrl, String localFilePath, DownloadCallback callback) {
        ExecutorService executor = Executors.newSingleThreadExecutor();

        executor.execute(() -> {
            try {
                boolean result = downloadFile(fileUrl, localFilePath);
                if (result) {
                    if (callback != null) {
                        callback.onSuccess(localFilePath);
                    }
                } else {
                    if (callback != null) {
                        callback.onFailure(new Exception("File download failed"));
                    }
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onFailure(e);
                }
            } finally {
                executor.shutdown();
            }
        });
    }

    public static boolean downloadFile(String fileUrl, String localFilePath) {
        OkHttpClient client = new OkHttpClient();

        Request request = new Request.Builder()
                .url(fileUrl)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                System.err.println("Failed to download file: " + response.code());
                return false;
            }

            ResponseBody body = response.body();
            if (body == null) {
                System.err.println("Response body is null");
                return false;
            }

            try (InputStream inputStream = body.byteStream();
                 FileOutputStream outputStream = new FileOutputStream(new File(localFilePath))) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
                System.out.println("File downloaded successfully to: " + localFilePath);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
