package com.xiaopeng.constant;

public final class Constant {
    public static class CarApi {
        public static final int DEFAULT_CHECK_TIMEOUT = 1; // 默认检查超时时间,单位秒
        public static final int DEFAULT_CHECK_WAIT_TIME = 100; // 默认检查间隔时间,单位毫秒
    }

    public static class ValueFlag {
        public static final String RANGE = "~"; // 范围值标志
        public static final String LIST = ","; // 列表值标志
    }

    public static class ValueType {
        public static final String UNKNOWN = "unknown";
        public static final String BYTE = "byte";
        public static final String INT = "int";
        public static final String FLOAT = "float";
        public static final String LONG = "long";
        public static final String STRING = "string";
        public static final String BOOLEAN = "boolean";
        public static final String VECTOR = "vector";
        public static final String BYTE_VECTOR = BYTE + "_" + VECTOR;
        public static final String INT_VECTOR = INT + "_" + VECTOR;
        public static final String FLOAT_VECTOR = FLOAT + "_" + VECTOR;
        public static final String LONG_VECTOR = LONG + "_" + VECTOR;
    }
}
