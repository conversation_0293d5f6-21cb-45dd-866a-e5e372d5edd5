package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.action.carapi.CarApiConfig;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;

public class BaseAction implements IAction<CarApiConfig> {
    protected CarClientWrapper carClientWrapper;

    @Override
    public void init(CarApiConfig config) {
        this.carClientWrapper = config.getService();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        return TestResult.failure("");
    }
}
