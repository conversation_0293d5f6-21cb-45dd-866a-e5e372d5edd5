package com.xiaopeng.xpautotest.viewmodel;

import com.xiaopeng.xpautotest.BuildConfig;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.bean.ErrorCode;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.manager.TestManager;
import com.xiaopeng.xpautotest.model.ITestDataState;
import com.xiaopeng.xpautotest.model.ITestSuiteItem;
import com.xiaopeng.xpautotest.model.TestCaseItem;
import com.xiaopeng.xpautotest.model.TestModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class SuiteViewModel extends ViewModel implements ISuiteViewModel {
    private static final String TAG = "SuiteViewModel";
    private final MutableLiveData<TestTaskBean> _taskInfo = new MutableLiveData<>();
    public LiveData<TestTaskBean> taskInfo = _taskInfo;
    private final MutableLiveData<List<ITestSuiteItem>> _testSuites = new MutableLiveData<>();
    public LiveData<List<ITestSuiteItem>> testSuites = _testSuites;

    private final MutableLiveData<ITestSuiteItem> _selectedSuite = new MutableLiveData<>();
    public LiveData<ITestSuiteItem> selectedSuite = _selectedSuite;
    // 当前执行的场景ID
    private final MutableLiveData<Long> currentSuiteId = new MutableLiveData<>();
    private final MutableLiveData<String> loadMode = new MutableLiveData<>();

    private final MutableLiveData<List<TestCaseItem>> _currentCases = new MutableLiveData<>();
    public LiveData<List<TestCaseItem>> currentCases = _currentCases;

    private final MutableLiveData<TestCaseItem> _executingCase = new MutableLiveData<>();
    public LiveData<TestCaseItem> executingCase = _executingCase;

    private final MutableLiveData<TestResultEntity> latestResult = new MutableLiveData<>();

    private final MutableLiveData<Boolean> loading = new MutableLiveData<>();
    private final MutableLiveData<ErrorCode> errorCode = new MutableLiveData<>();

    private Long manuallyStoppedTaskId = -1L;
    private final MutableLiveData<Boolean> _requestAutoStart = new MutableLiveData<>(false);
    private boolean isAwaitingPostExecutionHeartbeat = false;

    private TestModel testModel;

    public void load(String tag) {
        FileLogger.i(TAG, "load view model data start.");
        testModel = TestModel.getInstance();
        testModel.setISuiteViewModel(this);
        testModel.load(tag);
        testModel.update();
//        TestManager testManager = TestManager.getInstance();
//        testManager.load("remote");
//        testManager.update();
//        // Load test suites from database
//        List<ITestSuiteItem> data = testManager.getSuiteList();
//        _testSuites.postValue(data);
//        if (data != null && !data.isEmpty()) {
//            _currentCases.postValue(data.get(0).getItems());
//        }
        Log.i(TAG, "load view model data done." + _testSuites.getValue() + ", " + _currentCases.getValue());
    }

    // 重新加载数据
    public void reload(String tag) {
        loadMode.postValue("reload");
        setCurrentSuiteId(0);
        load(tag);
    }

    public void selectTestSuite(ITestSuiteItem testSuiteItem) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "selectTestSuite: " + testSuiteItem.getName());
        }
        setSelectedSuite(testSuiteItem);
        _currentCases.postValue(testSuiteItem.getItems());
        setCurrentSuiteId(testSuiteItem.getId());
    }

    // 数据加载完成后设置默认选中
//    public void initDefaultSelection() {
//        List<ITestSuiteItem> collections = _testSuites.getValue();
//        if (collections != null && !collections.isEmpty()) {
//            int position = 0;
////            if (currentSuiteId.getValue() != null) {
////                for (int i = 0; i < collections.size(); i++) {
////                    if (collections.get(i).getId() == currentSuiteId.getValue()) {
////                        position = i;
////                        collections.get(i).setSelected(true);
////                        break;
////                    }
////                }
////            }
////            if (position == 0) {
////                collections.get(0).setSelected(true);
////            }
//            setSelectedSuite(collections.get(position));
//        }
//    }

    // 设置选中集合
    public void setSelectedSuite(ITestSuiteItem collection) {
        _selectedSuite.setValue(collection);
    }

    /**
     * 添加以下方法供Fragment观察数据变化
     * */
    public LiveData<TestTaskBean> getTaskInfo() {
        return taskInfo;
    }
    public LiveData<List<ITestSuiteItem>> getTestSuites() {
        return testSuites;
    }

    public LiveData<ITestSuiteItem> getSelectedSuite() {
        return selectedSuite;
    }
    public long getCurrentSuiteId() {
        return currentSuiteId.getValue() != null ? currentSuiteId.getValue() : 0;
    }
    public String getLoadMode() {
        return loadMode.getValue() != null ? loadMode.getValue() : "normal";
    }

    public LiveData<List<TestCaseItem>> getCurrentCases() {
        return currentCases;
    }
    public LiveData<TestResultEntity> getLatestResult() { return latestResult; }

    public LiveData<TestCaseItem> getExecutingCase() {
        return executingCase;
    }
    public LiveData<Boolean> getLoading() {
        return loading;
    }
    public LiveData<ErrorCode> getErrorCode() {
        return errorCode;
    }

    public LiveData<Boolean> getRequestAutoStart() {
        return _requestAutoStart;
    }

    public void onAutoStartEventConsumed() {
        _requestAutoStart.setValue(false);
    }

    public void triggerHeartbeatForNextTask() {
        TestModel.getInstance().heartbeat();
    }

    public void setAwaitingPostExecutionHeartbeat(boolean isAwaiting) {
        this.isAwaitingPostExecutionHeartbeat = isAwaiting;
    }

    public void updateScriptResult(TestCaseItem updatedItem) {
        List<TestCaseItem> currentList = new ArrayList<>(Objects.requireNonNull(_currentCases.getValue()));
        int index = currentList.indexOf(updatedItem);
        if (index != -1) {
            currentList.set(index, updatedItem);
            _currentCases.postValue(currentList);
        }
    }

    public void setCurrentSuiteId(long suiteId) {
        currentSuiteId.postValue(suiteId);
    }

    @Override
    public void onTaskInfoUpdate(TestTaskBean taskInfo) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onTaskInfoUpdate");
        }
        errorCode.postValue(null);
        _taskInfo.postValue(taskInfo);
    }

    @Override
    public void onSuiteListUpdate(List<ITestSuiteItem> suiteList) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onSuiteListUpdate: " + suiteList.size());
        }
        _testSuites.postValue(suiteList);
        // 1. 自动更新当前TestCase列表
        long selectedId = getCurrentSuiteId();
        if (selectedId > 0 && suiteList != null && !suiteList.isEmpty()) {
            // 2. 在新的suiteList中查找与当前选中ID相同的Suite
            for (ITestSuiteItem newSuite : suiteList) {
                if (newSuite.getId() == selectedId) {
                    _selectedSuite.postValue(newSuite);
                    _currentCases.postValue(newSuite.getItems());
                    break;
                }
            }
        } else if (suiteList != null && !suiteList.isEmpty()) {
            // 如果之前没有选中的，或ID无效(新拉取的任务)，就默认选中第一个
            _selectedSuite.postValue(suiteList.get(0));
            _currentCases.postValue(suiteList.get(0).getItems());
        } else if (suiteList == null || suiteList.isEmpty()) {
            // 如果suiteList为空，则清空当前选中和用例列表
            _selectedSuite.postValue(null);
            _currentCases.postValue(new ArrayList<>());
        }

        loading.postValue(false);
        // 新的核心决策逻辑
        // 场景: 任务执行完毕后，load("local") 完成，此时应该触发心跳获取新指令
        if (isAwaitingPostExecutionHeartbeat) {
            isAwaitingPostExecutionHeartbeat = false; // 消耗标志
//            triggerHeartbeatForNextTask();
            return; // 等待心跳结果，不做其他操作
        }
        // 场景: 首次启动、手动刷新、或心跳返回后，进行自动启动判断
        checkAndTriggerAutoStart();
    }

    @Override
    public void onLoadCaseFailed(ErrorCode code) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onLoadCaseFailed: " + code);
        }
        loading.postValue(false);
        errorCode.postValue(code);
    }

    @Override
    public void onTriggerAutoStart(TestTaskBean taskInfo) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onTriggerAutoStart: " + taskInfo);
        }

        // 更新任务信息
        _taskInfo.postValue(taskInfo);

        // 自动启动指定场景，则更新当前选中场景ID
        if (taskInfo.isAutoStart()) {
            if (taskInfo.getAutoStartSuiteId() > 0) {
                setCurrentSuiteId(taskInfo.getAutoStartSuiteId());
                Log.i(TAG, "set auto-start suite ID: " + taskInfo.getAutoStartSuiteId());
            }
            _requestAutoStart.postValue(true);
        }
    }

    public void updateResult(TestResultEntity result) {
        // 更新测试结果到前端界面
        Log.d(TAG, "update latestResult: " + result);
        latestResult.postValue(result);
    }

//    private void updateLatestResultStatus(TestResultEntity result) {
//        if (_testSuites.getValue() == null) {
//            return;
//        }
//
//        for (ITestSuiteItem suite : _testSuites.getValue()) {
//            ITestSuiteItem testSuiteItem =  suite;
//            for (TestCaseItem caseItem : testSuiteItem.getItems()) {
//                if (caseItem.getScriptId() == result.getScriptId()) {
//                    caseItem.setStatus(result.getStatus());
//                    Log.d(TAG, "updateLatestResultStatus, update caseItem: " + caseItem);
//                    return;
//                }
//            }
//        }
//    }
    public void resetLoadMode() {
        loadMode.postValue("normal");
    }
    
    public void executeCase(TestCaseItem caseItem) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "executeCase: " + caseItem.getName());
        }
//        testModel.executeCase(caseItem);
    }

    public void stopCase(TestCaseItem caseItem) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "stopCase: " + caseItem.getName());
        }
    }

    public long startTestSync() {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "startTestSync");
        }
        return testModel.startTestSync();
    }

    public void startTest(TestManager.ITestDataCallBack<ITestDataState<Long>> callback) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "startExecution");
        }
        testModel.startTest(callback);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onCleared");
        }
        testModel.clear();
    }

    /**
     * 标记一个被手动停止的任务。
     * @param taskId 任务ID
     */
    public void setManuallyStoppedTaskId(long taskId) {
        this.manuallyStoppedTaskId = taskId;
    }

    private void checkAndTriggerAutoStart() {
        if (testModel.isAutoStartAllowed(manuallyStoppedTaskId)) {
            _requestAutoStart.postValue(true);
        }
    }

    public ITestSuiteItem getAutoStartSuiteItem() {
        TestTaskBean task = _taskInfo.getValue();
        if (task != null) {
            List<ITestSuiteItem> suites = _testSuites.getValue();
            if (suites != null && !suites.isEmpty()) {
                for (ITestSuiteItem suite : suites) {
                    if (suite.getId() == task.getAutoStartSuiteId()) {
                        return suite;
                    }
                }
            }
        }
        return null;
    }
}
