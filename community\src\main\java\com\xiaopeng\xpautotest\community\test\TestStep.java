package com.xiaopeng.xpautotest.community.test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaopeng.xpautotest.community.utils.Constant;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import androidx.annotation.NonNull;

public class TestStep {
    private Long scriptId;
    private int stepId;
    private String action;
    private  String step;
    private String comments;

    private String keywords; // 关键字

    private String stepType; // 步骤类型，可能是"Precondition", "Procedure", "PostCondition"
    // 是否上报
    private boolean isReport = true;
    private JsonNode parameters = null;
    private String[] paramArray;

    private static final Set<String> ACTIONS_TAKING_RAW_ARGS_AS_SINGLE_STRING = new HashSet<>(Arrays.asList(
            "SendBroadcast"
            , "CheckLogcatContains"
    ));

    private static final Pattern ACTION_EXTRACTOR_PATTERN = Pattern.compile("(\\S+)(?:\\s+(.*))?");

    public TestStep(Long scriptId, int stepId, String line, String comments,String keywords, String stepType) {
        this.scriptId = scriptId;
        this.stepId = stepId;
        this.step = line.trim();
        this.comments = comments;
        this.keywords =keywords;
        this.stepType = stepType;

        String trimmedLine = line.trim();
        Matcher actionMatcher = ACTION_EXTRACTOR_PATTERN.matcher(trimmedLine);

        if (actionMatcher.matches()) {
            this.action = actionMatcher.group(1);
            String restOfLine = actionMatcher.group(2);

            if (ACTIONS_TAKING_RAW_ARGS_AS_SINGLE_STRING.contains(this.action)) {
                if (restOfLine != null) {
                    this.paramArray = new String[]{restOfLine};
                } else {
                    this.paramArray = new String[0];
                }
            } else {
                if (restOfLine != null && !restOfLine.isEmpty()) {
                    List<String> partsList = new ArrayList<>();
                    // 正则表达式: 匹配双引号包裹的内容（包括转义引号）或者 匹配非空白字符序列
                    Pattern pattern = Pattern.compile("\"((?:\\\\\"|[^\"])*)\"|\\S+");
                    Matcher matcher = pattern.matcher(restOfLine);
                    while (matcher.find()) {
                        String matchedGroup = matcher.group();
                        if (matchedGroup.startsWith("\"") && matchedGroup.endsWith("\"")) {
                            partsList.add(matchedGroup.substring(1, matchedGroup.length() - 1));
                        } else {
                            partsList.add(matchedGroup);
                        }
                    }
                    this.paramArray = partsList.toArray(new String[0]);
                } else {
                    this.paramArray = new String[0];
                }
            }
        } else {
            // 只有 Action，没有参数
            this.action = trimmedLine;
            this.paramArray = new String[0];
        }

        ObjectMapper mapper = new ObjectMapper();
        if (this.paramArray != null && this.paramArray.length > 0) {
            parameters = mapper.valueToTree(paramArray);
        } else {
            parameters = mapper.createArrayNode();
        }

        if (Arrays.asList(Constant.ACTION_NO_NEED_REPORT).contains(this.action.toLowerCase())) {
            isReport = false;
        }
    }

    // Getters and setters
    public Long getScriptId() {
        return scriptId;
    }
    public int getStepId() {
        return stepId;
    }
    public String getAction() {
        return action;
    }
    public String getStep() {
        return step;
    }
    public String getComments() {
        return comments;
    }

    public String getKeywords() {
        return keywords;
    }

    public String getStepType() {
        return stepType;
    }

    public JsonNode getParameters() {
        return parameters;
    }

    public String[] getParamArray() {
        return paramArray;
    }

    public void setParameters(JsonNode parameters) {
        this.parameters = parameters;
    }

    public String toString() {
        return action + "(" + String.join(", ", paramArray) + ")";
    }

    public boolean isReport() {
        return isReport;
    }


//    public static void main(String[] args) {
//        String[] testLines = {
//            "action button1",                     // 简单参数       [button1]
//            "action \"Hello World\"",     // 带空格的引号参数           [Hello World]
//            "action \"TAG\" \"Error occurred\"", // 多个引号参数          [TAG, Error occurred]
//            "action -p \"path/with space\"", // 混合参数 带反斜杠 空格 双引号    [-p, path/with space]
//            "action 5",                            // 单个数字参数            [5]
//            "action \"Element with \\\"escaped quotes\\\" inside\"", // 带转义引号的参数    [Element with \"escaped quotes\" inside]
//            "onlyAction",                        // 只有 Action               []
//            "action \"\"",                       // 空字符串参数                  []
//            "action \"param1\"\"param2\"",       // 连续引号参数 (会被解析为2个参数)      [param1, param2]
//            "action param1\"noSpace\"param2",     // 引号紧邻非空格字符 (会被解析为1个参数)      [param1"noSpace"param2]
//        };
//
//        for (String line : testLines) {
//            System.out.println("\nInput Line: [" + line + "]");
//
//            String trimmedLine = line.trim();
//            Matcher actionMatcher = ACTION_EXTRACTOR_PATTERN.matcher(trimmedLine);
//            String action;
//            String[] paramArray;
//            if (actionMatcher.matches()) {
//                action = actionMatcher.group(1);
//                String restOfLine = actionMatcher.group(2);
//
//                if (ACTIONS_TAKING_RAW_ARGS_AS_SINGLE_STRING.contains(action)) {
//                    if (restOfLine != null) {
//                        paramArray = new String[]{restOfLine};
//                    } else {
//                        paramArray = new String[0];
//                    }
//                } else {
//                    if (restOfLine != null && !restOfLine.isEmpty()) {
//                        List<String> partsList = new ArrayList<>();
//                        // 正则表达式: 匹配双引号包裹的内容（包括转义引号）或者 匹配非空白字符序列
//                        Pattern pattern = Pattern.compile("\"((?:\\\\\"|[^\"])*)\"|\\S+");
//                        Matcher matcher = pattern.matcher(restOfLine);
//                        while (matcher.find()) {
//                            String matchedGroup = matcher.group();
//                            if (matchedGroup.startsWith("\"") && matchedGroup.endsWith("\"")) {
//                                partsList.add(matchedGroup.substring(1, matchedGroup.length() - 1));
//                            } else {
//                                partsList.add(matchedGroup);
//                            }
//                        }
//                        paramArray = partsList.toArray(new String[0]);
//                    } else {
//                        paramArray = new String[0];
//                    }
//                }
//            } else {
//                // 只有 Action，没有参数
//                action = trimmedLine;
//                paramArray = new String[0];
//            }
//            System.out.println("Parsed Parts: " + Arrays.toString(paramArray));
//        }
//
//    }

}