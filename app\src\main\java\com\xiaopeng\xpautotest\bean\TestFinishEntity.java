package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TestFinishEntity  implements Parcelable, ReportableEntity {
    private long taskExecutionId;
    private long triggerTimestamp;
    private long suiteId;
    private int testCount;
    private int okCount; // 成功的数量
    private int ngCount; // 失败的数量
    private int naCount; // 跳过的数量
    // 存储到oss上的FileLogger日志路径
    private String logPath;
    // 存储到oss上的大屏日志路径
    private String cduLogPath;
    // 本轮测试执行的脚本ID列表
    private ArrayList<Long> scriptIdList;

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public void setCduLogPath(String cduLogPath) {
        this.cduLogPath = cduLogPath;
    }


    public TestFinishEntity(long taskExecutionId, long triggerTimestamp, long suiteId, int testCount, int okCount, int ngCount,int naCount, ArrayList<Long> scriptIdList) {
        this.taskExecutionId = taskExecutionId;
        this.triggerTimestamp = triggerTimestamp;
        this.suiteId = suiteId;
        this.testCount = testCount;
        this.okCount = okCount;
        this.ngCount = ngCount;
        this.naCount = naCount;
        this.scriptIdList = scriptIdList;
    }

    protected TestFinishEntity(Parcel in) {
        taskExecutionId = in.readLong();
        triggerTimestamp = in.readLong();
        suiteId = in.readLong();
        testCount = in.readInt();
        okCount = in.readInt();
        ngCount = in.readInt();
        naCount = in.readInt();
        logPath = in.readString();
        cduLogPath = in.readString();
        scriptIdList = in.readArrayList(Long.class.getClassLoader());
    }

    public static final Creator<TestFinishEntity> CREATOR = new Creator<TestFinishEntity>() {
        @Override
        public TestFinishEntity createFromParcel(Parcel in) {
            return new TestFinishEntity(in);
        }

        @Override
        public TestFinishEntity[] newArray(int size) {
            return new TestFinishEntity[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(taskExecutionId);
        dest.writeLong(triggerTimestamp);
        dest.writeLong(suiteId);
        dest.writeInt(testCount);
        dest.writeInt(okCount);
        dest.writeInt(ngCount);
        dest.writeInt(naCount);
        dest.writeString(logPath);
        dest.writeString(cduLogPath);
        dest.writeList(scriptIdList);
    }

    @Override
    public long getTaskExecutionId() {
        return taskExecutionId;
    }
}
