package com.xiaopeng.xpautotest.utils;

import androidx.annotation.Nullable;
import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class OSSPathUtils {

    private static final String TAG = "OSSPathUtils";

    public static String buildObjectKey(UploadFileTask.FileType fileType,
                                        String originalFileName,
                                        @Nullable String localFilePath) {
        if(originalFileName == null || originalFileName.isEmpty()) {
            FileLogger.w(TAG, "Original file name is null or empty. Cannot construct S3 object key.");
            return "";
        }

        String objectKeyPrefix;
        switch (fileType) {
            case FILE_IMAGE:
                objectKeyPrefix = "img/";
                break;
            case FILE_ZIP:
                objectKeyPrefix = "log/";
                break;
            case FILE_LOG: // 普通日志也放在 log/ 下
                objectKeyPrefix = "log/";
                break;
            case FILE_XML: // 为 XML 文件定义一个前缀
                objectKeyPrefix = "uidump/";
                break;
            case FILE_CDU_LOG: // 大屏日志
                objectKeyPrefix = "cdu_log/";
                break;
            default:
                objectKeyPrefix = "unknown/"; // 未知类型的前缀
                break;
        }

        String datePath;
        long timestampMillis = 0;

        try {
            String fileNameCore = originalFileName;
            int lastDot = originalFileName.lastIndexOf('.');

            if (lastDot > 0) {
                 fileNameCore = originalFileName.substring(0, lastDot);
            }
            String timestampStr;
            int lastUnderscore = fileNameCore.lastIndexOf('_');

            // 去掉后缀，默认文件名最后一个下划线后的部分是时间戳
            // "prefix_ts" -> "ts"; "prefix_coord_ts" -> "ts"
            if (lastUnderscore != -1 && lastUnderscore < fileNameCore.length() - 1) {
                timestampStr = fileNameCore.substring(lastUnderscore + 1);
            } else {
                // 如果没有下划线，或者下划线在最后一个字符之前，那么就认为文件名本身就是时间戳
                timestampStr = fileNameCore;
            }
            timestampMillis = Long.parseLong(timestampStr);

        } catch (NumberFormatException e) {
            FileLogger.w(TAG, "Could not parse timestamp from filename: '" + originalFileName + "'. Error: " + e.getMessage());
        }
        if (timestampMillis > 0) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
            datePath = dateFormat.format(new Date(timestampMillis));
        } else if (localFilePath != null && !localFilePath.isEmpty()) {
            FileLogger.w(TAG, "Failed to get timestamp from filename, falling back to file.lastModified() for: " + originalFileName + " using path: " + localFilePath);
            File localFile = new File(localFilePath);
            if (localFile.exists()) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
                datePath = dateFormat.format(new Date(localFile.lastModified()));
            } else {
                FileLogger.w(TAG, "Fallback file does not exist: " + localFilePath + ". Using current date for datePath.");
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
                datePath = dateFormat.format(new Date()); // 如果文件不存在，使用当前日期
            }
        } else {
            FileLogger.w(TAG, "Failed to get timestamp from filename and no local file path provided for fallback for: " + originalFileName + ". Using current date for datePath.");
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
            datePath = dateFormat.format(new Date()); // 如果两者都失败，使用当前日期
        }

        //
        String vin = SystemPropertiesUtils.getVIN();
        // 如果 VIN 为空，那就返回一个空字符串
        if (vin == null || vin.isEmpty()) {
            FileLogger.w(TAG, "VIN is null or empty. Cannot construct OSS object key.");
            return "";
        }


        StringBuilder sb = new StringBuilder();
        sb.append(objectKeyPrefix);
        sb.append(datePath);
        sb.append("/");
        sb.append(vin);
        sb.append("/");
        sb.append(originalFileName);
        String key = sb.toString();
        return key;
    }
} 