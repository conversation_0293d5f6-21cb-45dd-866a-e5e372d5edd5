package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class BackToHomeAction extends BaseAction {
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        FileLogger.i("BackToHomeAction", "Attempting to go to home screen");
        boolean success = this.service.goToHome();
        if (!success) {
            return TestResult.failure("Failed to go to home screen.");
        }
        return TestResult.success("Successfully navigated to home screen.");
    }
}
