package com.xiaopeng.executor.action.carapi;

import android.content.Context;

import com.xiaopeng.executor.bean.ActionConfig;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IBcmController;

public class CarApiConfig extends ActionConfig {

    private static CarApiConfig instance;
    private CarClientWrapper carClientWrapper;
    private IBcmController mIBcmController = null;

    public static synchronized CarApiConfig getInstance() {
        if (instance == null) {
            synchronized (CarApiConfig.class) {
                if (instance == null) {
                    instance = new CarApiConfig();
                }
            }
        }
        return instance;
    }

    public void initService(Context context) {
        carClientWrapper = CarClientWrapper.getInstance();
        carClientWrapper.connectToCar(context);
        initControllers();
    }

    public CarClientWrapper getService() {
        return carClientWrapper;
    }

    private void initControllers() {
//        mIBcmController = (IBcmController) carClientWrapper.getController(CarClientWrapper.XP_BCM_SERVICE);
//        mIBcmController.registerCallback(context);
    }

    public Object getControllers() {
        return mIBcmController;
    }
}
