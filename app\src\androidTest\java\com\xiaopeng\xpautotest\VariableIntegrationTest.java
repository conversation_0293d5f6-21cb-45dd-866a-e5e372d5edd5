package com.xiaopeng.xpautotest;

import android.content.Context;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.xiaopeng.executor.TestExecutor;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.*;

/**
 * 动态变量功能集成测试
 * 
 * 测试目标：
 * 1. 测试变量定义与引用功能
 * 2. 测试动态变量赋值（GetValueByConf）
 * 3. 测试变量条件比较（==、!=）
 * 4. 测试变量在不同阶段的使用
 * 5. 测试变量重新赋值
 * 6. 测试错误处理和边界情况
 */
@RunWith(AndroidJUnit4.class)
public class VariableIntegrationTest {
    
    private static final String TAG = "VariableIntegrationTest";
    private Context context;
    private TestExecutor testExecutor;
    
    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        testExecutor = new TestExecutor(context, "com.xiaopeng.xpautotest");
        FileLogger.i(TAG, "Test setup completed");
    }
    
    /**
     * 测试基础变量定义与引用
     */
    @Test
    public void test_basic_variable_definition_and_reference() {
        String script = 
            "Procedure:\n" +
            "Scene: 1\n" +
            "    # 定义各种类型的变量\n" +
            "    Set APP_NAME \"智能助手\"\n" +
            "    Set TEMP_VALUE 25.5\n" +
            "    Set IS_ENABLED true\n" +
            "    Set COUNT 10\n" +
            "    shell \"echo 'Variables defined successfully'\"\n" +
            "    \n" +
            "    # 使用变量\n" +
            "    shell \"echo 'App name: $APP_NAME'\"\n" +
            "    shell \"echo 'Temperature: $TEMP_VALUE'\"\n" +
            "    shell \"echo 'Enabled: $IS_ENABLED'\"\n" +
            "    shell \"echo 'Count: $COUNT'\"\n" +
            "    \n" +
            "    # 验证变量值\n" +
            "    If $APP_NAME == \"智能助手\"\n" +
            "        shell \"echo 'App name variable test passed'\"\n" +
            "    Else\n" +
            "        shell \"echo 'App name variable test failed'\"\n" +
            "    EndIf\n";
        
        int result = testExecutor.runTestScript(1L, script);
        assertEquals("Basic variable test should succeed", 0, result);
        FileLogger.i(TAG, "Basic variable definition and reference test completed");
    }
    
    /**
     * 测试动态变量赋值功能
     */
    @Test
    public void test_dynamic_variable_assignment() {
        String script = 
            "Procedure:\n" +
            "Scene: 1\n" +
            "    # 动态获取配置值\n" +
            "    GetValueByFeature FEATURE_SUPPORT \"config_carspeech_response_support_passenger_bluetooth\"\n" +
            "    GetValueByFeature SYSTEM_VERSION \"config_system_version\"\n" +
            "    GetValueByFeature DEVICE_MODEL \"config_device_model\"\n" +
            "    shell \"echo 'Dynamic variables loaded'\"\n" +
            "    \n" +
            "    # 使用动态获取的变量\n" +
            "    shell \"echo 'Feature support: $FEATURE_SUPPORT'\"\n" +
            "    shell \"echo 'System version: $SYSTEM_VERSION'\"\n" +
            "    shell \"echo 'Device model: $DEVICE_MODEL'\"\n" +
            "    \n" +
            "    # 基于动态变量进行条件判断\n" +
            "    If $FEATURE_SUPPORT == \"true\"\n" +
            "        shell \"echo 'Feature is supported'\"\n" +
            "        Set STATUS \"feature_enabled\"\n" +
            "    Else\n" +
            "        shell \"echo 'Feature is not supported'\"\n" +
            "        Set STATUS \"feature_disabled\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    shell \"echo 'Final status: $STATUS'\"\n";
        
        int result = testExecutor.runTestScript(2L, script);
        assertEquals("Dynamic variable assignment test should succeed", 0, result);
        FileLogger.i(TAG, "Dynamic variable assignment test completed");
    }
    
    /**
     * 测试变量条件比较功能
     */
    @Test
    public void test_variable_conditional_comparison() {
        String script = 
            "Procedure:\n" +
            "Scene: 1\n" +
            "    # 定义测试变量\n" +
            "    Set STATUS \"ready\"\n" +
            "    Set COUNT 5\n" +
            "    Set EMPTY_VAR \"\"\n" +
            "    Set BOOLEAN_VAR \"true\"\n" +
            "    shell \"echo 'Test variables defined'\"\n" +
            "    \n" +
            "    # 测试等于比较\n" +
            "    If $STATUS == \"ready\"\n" +
            "        shell \"echo 'Status equals ready - PASS'\"\n" +
            "        Set TEST1_RESULT \"PASS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Status equals ready - FAIL'\"\n" +
            "        Set TEST1_RESULT \"FAIL\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 测试不等于比较\n" +
            "    If $STATUS != \"loading\"\n" +
            "        shell \"echo 'Status not equals loading - PASS'\"\n" +
            "        Set TEST2_RESULT \"PASS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Status not equals loading - FAIL'\"\n" +
            "        Set TEST2_RESULT \"FAIL\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 测试空值比较\n" +
            "    If $EMPTY_VAR == \"\"\n" +
            "        shell \"echo 'Empty variable test - PASS'\"\n" +
            "        Set TEST3_RESULT \"PASS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Empty variable test - FAIL'\"\n" +
            "        Set TEST3_RESULT \"FAIL\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 测试布尔值比较\n" +
            "    If $BOOLEAN_VAR == \"true\"\n" +
            "        shell \"echo 'Boolean variable test - PASS'\"\n" +
            "        Set TEST4_RESULT \"PASS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Boolean variable test - FAIL'\"\n" +
            "        Set TEST4_RESULT \"FAIL\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 汇总测试结果\n" +
            "    shell \"echo 'Test1: $TEST1_RESULT, Test2: $TEST2_RESULT, Test3: $TEST3_RESULT, Test4: $TEST4_RESULT'\"\n";
        
        int result = testExecutor.runTestScript(3L, script);
        assertEquals("Variable conditional comparison test should succeed", 0, result);
        FileLogger.i(TAG, "Variable conditional comparison test completed");
    }
    
    /**
     * 测试变量在不同阶段的使用
     */
    @Test
    public void test_variable_across_different_phases() {
        String script = 
            "Precondition:\n" +
            "Scene: 1\n" +
            "    # 在前置条件中定义变量\n" +
            "    Set GLOBAL_CONFIG \"test_mode\"\n" +
            "    Set INIT_STATUS \"initialized\"\n" +
            "    shell \"echo 'Precondition variables set'\"\n" +
            "\n" +
            "Procedure:\n" +
            "Scene: 1\n" +
            "    # 在主流程中使用前置条件的变量\n" +
            "    shell \"echo 'Using global config: $GLOBAL_CONFIG'\"\n" +
            "    shell \"echo 'Init status: $INIT_STATUS'\"\n" +
            "    \n" +
            "    # 在主流程中定义新变量\n" +
            "    Set EXECUTION_RESULT \"processing\"\n" +
            "    Set STEP_COUNT 3\n" +
            "    \n" +
            "    # 基于前置变量进行条件判断\n" +
            "    If $GLOBAL_CONFIG == \"test_mode\"\n" +
            "        shell \"echo 'Running in test mode'\"\n" +
            "        Set EXECUTION_RESULT \"test_completed\"\n" +
            "    Else\n" +
            "        shell \"echo 'Running in production mode'\"\n" +
            "        Set EXECUTION_RESULT \"prod_completed\"\n" +
            "    EndIf\n" +
            "\n" +
            "PostCondition:\n" +
            "Scene: 1\n" +
            "    # 在后置条件中使用所有阶段的变量\n" +
            "    shell \"echo 'Final config: $GLOBAL_CONFIG'\"\n" +
            "    shell \"echo 'Final result: $EXECUTION_RESULT'\"\n" +
            "    shell \"echo 'Step count: $STEP_COUNT'\"\n" +
            "    \n" +
            "    # 最终验证\n" +
            "    If $EXECUTION_RESULT == \"test_completed\"\n" +
            "        shell \"echo 'Cross-phase variable test - PASS'\"\n" +
            "        Set FINAL_STATUS \"SUCCESS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Cross-phase variable test - FAIL'\"\n" +
            "        Set FINAL_STATUS \"FAILURE\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    shell \"echo 'Final status: $FINAL_STATUS'\"\n";
        
        int result = testExecutor.runTestScript(4L, script);
        assertEquals("Cross-phase variable test should succeed", 0, result);
        FileLogger.i(TAG, "Variable across different phases test completed");
    }

    /**
     * 测试变量重新赋值功能
     */
    @Test
    public void test_variable_reassignment() {
        String script =
            "Procedure:\n" +
            "Scene: 1\n" +
            "    # 初始变量定义\n" +
            "    Set COUNTER 0\n" +
            "    Set MESSAGE \"initial\"\n" +
            "    Set TEMP_ID \"com.xiaopeng.carhvac:id/tv_temp_drv_num\"\n" +
            "    shell \"echo 'Initial values set'\"\n" +
            "    \n" +
            "    # 验证初始值\n" +
            "    shell \"echo 'Counter: $COUNTER, Message: $MESSAGE'\"\n" +
            "    \n" +
            "    # 重新赋值测试\n" +
            "    Set COUNTER 1\n" +
            "    Set MESSAGE \"updated\"\n" +
            "    shell \"echo 'Values updated'\"\n" +
            "    \n" +
            "    # 验证更新后的值\n" +
            "    If $COUNTER == \"1\"\n" +
            "        shell \"echo 'Counter reassignment - PASS'\"\n" +
            "    Else\n" +
            "        shell \"echo 'Counter reassignment - FAIL'\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    If $MESSAGE == \"updated\"\n" +
            "        shell \"echo 'Message reassignment - PASS'\"\n" +
            "    Else\n" +
            "        shell \"echo 'Message reassignment - FAIL'\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 多次重新赋值\n" +
            "    Set TEMP_ID \"com.xiaopeng.carhvac:id/tv_temp_passenger_num\"\n" +
            "    shell \"echo 'Temp ID changed to: $TEMP_ID'\"\n" +
            "    \n" +
            "    Set TEMP_ID \"com.xiaopeng.carhvac:id/tv_temp_rear_num\"\n" +
            "    shell \"echo 'Temp ID changed again to: $TEMP_ID'\"\n" +
            "    \n" +
            "    # 最终验证\n" +
            "    If $TEMP_ID == \"com.xiaopeng.carhvac:id/tv_temp_rear_num\"\n" +
            "        shell \"echo 'Multiple reassignment - PASS'\"\n" +
            "    Else\n" +
            "        shell \"echo 'Multiple reassignment - FAIL'\"\n" +
            "    EndIf\n";

        int result = testExecutor.runTestScript(5L, script);
        assertEquals("Variable reassignment test should succeed", 0, result);
        FileLogger.i(TAG, "Variable reassignment test completed");
    }

    /**
     * 测试变量与实际UI操作的结合
     */
    @Test
    public void test_variable_with_ui_operations() {
        String script =
            "Procedure:\n" +
            "Scene: 1\n" +
            "    # 定义UI相关变量\n" +
            "    Set APP_PACKAGE \"com.xiaopeng.smartcontrol\"\n" +
            "    Set BUTTON_ID \"com.xiaopeng.smartcontrol:id/btn_confirm\"\n" +
            "    Set INPUT_ID \"com.xiaopeng.smartcontrol:id/edit_text\"\n" +
            "    Set INPUT_VALUE \"test_input\"\n" +
            "    Set WAIT_TIMEOUT 5\n" +
            "    shell \"echo 'UI operation variables defined'\"\n" +
            "    \n" +
            "    # 使用变量进行UI操作\n" +
            "    shell \"echo 'Starting app: $APP_PACKAGE'\"\n" +
            "    \n" +
            "    # 模拟等待和点击操作\n" +
            "    If WaitId $BUTTON_ID $WAIT_TIMEOUT\n" +
            "        shell \"echo 'Button found, clicking: $BUTTON_ID'\"\n" +
            "        Set BUTTON_STATUS \"found\"\n" +
            "    Else\n" +
            "        shell \"echo 'Button not found: $BUTTON_ID'\"\n" +
            "        Set BUTTON_STATUS \"not_found\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 根据按钮状态决定后续操作\n" +
            "    If $BUTTON_STATUS == \"found\"\n" +
            "        shell \"echo 'Proceeding with input operation'\"\n" +
            "        shell \"echo 'Input ID: $INPUT_ID, Value: $INPUT_VALUE'\"\n" +
            "        Set OPERATION_RESULT \"success\"\n" +
            "    Else\n" +
            "        shell \"echo 'Skipping input operation'\"\n" +
            "        Set OPERATION_RESULT \"skipped\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 最终状态报告\n" +
            "    shell \"echo 'UI operation completed with result: $OPERATION_RESULT'\"\n" +
            "    \n" +
            "    # 验证操作结果\n" +
            "    If $OPERATION_RESULT != \"error\"\n" +
            "        shell \"echo 'UI operation test - PASS'\"\n" +
            "    Else\n" +
            "        shell \"echo 'UI operation test - FAIL'\"\n" +
            "    EndIf\n";

        int result = testExecutor.runTestScript(7L, script);
        assertEquals("Variable with UI operations test should succeed", 0, result);
        FileLogger.i(TAG, "Variable with UI operations test completed");
    }

    /**
     * 测试变量的边界情况和特殊值
     */
    @Test
    public void test_variable_edge_cases() {
        String script =
            "Procedure:\n" +
            "Scene: 1\n" +
            "    # 测试空字符串变量\n" +
            "    Set EMPTY_STRING \"\"\n" +
            "    Set SPACE_STRING \" \"\n" +
            "    Set NULL_STRING \"null\"\n" +
            "    shell \"echo 'Edge case variables defined'\"\n" +
            "    \n" +
            "    # 测试空字符串比较\n" +
            "    If $EMPTY_STRING == \"\"\n" +
            "        shell \"echo 'Empty string test - PASS'\"\n" +
            "        Set TEST1 \"PASS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Empty string test - FAIL'\"\n" +
            "        Set TEST1 \"FAIL\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 测试空格字符串\n" +
            "    If $SPACE_STRING != \"\"\n" +
            "        shell \"echo 'Space string test - PASS'\"\n" +
            "        Set TEST2 \"PASS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Space string test - FAIL'\"\n" +
            "        Set TEST2 \"FAIL\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 测试特殊字符\n" +
            "    Set SPECIAL_CHARS \"!@#$%^&*()\"\n" +
            "    Set CHINESE_CHARS \"中文测试\"\n" +
            "    Set NUMBERS \"12345\"\n" +
            "    Set DECIMAL \"123.45\"\n" +
            "    \n" +
            "    shell \"echo 'Special chars: $SPECIAL_CHARS'\"\n" +
            "    shell \"echo 'Chinese chars: $CHINESE_CHARS'\"\n" +
            "    shell \"echo 'Numbers: $NUMBERS'\"\n" +
            "    shell \"echo 'Decimal: $DECIMAL'\"\n" +
            "    \n" +
            "    # 测试长字符串\n" +
            "    Set LONG_STRING \"This is a very long string that contains multiple words and should test the variable system's ability to handle longer text content without issues\"\n" +
            "    shell \"echo 'Long string length test'\"\n" +
            "    \n" +
            "    If $LONG_STRING != \"\"\n" +
            "        shell \"echo 'Long string test - PASS'\"\n" +
            "        Set TEST3 \"PASS\"\n" +
            "    Else\n" +
            "        shell \"echo 'Long string test - FAIL'\"\n" +
            "        Set TEST3 \"FAIL\"\n" +
            "    EndIf\n" +
            "    \n" +
            "    # 汇总边界测试结果\n" +
            "    shell \"echo 'Edge case tests: $TEST1, $TEST2, $TEST3'\"\n";

        int result = testExecutor.runTestScript(8L, script);
        assertEquals("Variable edge cases test should succeed", 0, result);
        FileLogger.i(TAG, "Variable edge cases test completed");
    }
}
