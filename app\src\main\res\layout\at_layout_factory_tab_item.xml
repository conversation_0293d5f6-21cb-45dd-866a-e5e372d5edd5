<?xml version="1.0" encoding="utf-8"?>
<com.xiaopeng.xui.widget.XConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tab_layout"
    style="@style/XCatalog.Item"
    android:background="@drawable/x_menubar_item_bg_drawable"
    android:layout_width="match_parent"
    android:layout_height="@dimen/factory_tab_height">

    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_tab_index"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:textSize="@dimen/x_font_title_04_size"
        android:textAlignment="center"
        android:gravity="center"
        android:textColor="@color/x_catalog_bar_text_color_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/tv_tab_text"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="10dp"
        android:textSize="@dimen/x_font_title_04_size"
        android:textAlignment="center"
        android:gravity="center"
        android:textColor="@color/x_menu_bar_text_color_selector"
        app:layout_constraintStart_toEndOf="@id/tv_tab_index"
        app:layout_constraintTop_toTopOf="parent"  />

    <com.xiaopeng.xui.widget.XImageView
        android:id="@+id/iv_tab_status"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</com.xiaopeng.xui.widget.XConstraintLayout>
