package com.xiaopeng.executor;

import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.TestScript;
import com.xiaopeng.xpautotest.community.test.TestStep;

/**
 * 测试执行器接口
 *
 * 定义测试执行器对外提供的核心功能契约：
 * - 脚本执行：支持从脚本ID和步骤内容执行测试
 * - 执行控制：支持暂停、恢复、停止操作
 * - 状态查询：查询当前执行状态
 * - 回调管理：管理执行过程中的回调处理
 */
public interface ITestExecutor {

    /**
     * 运行测试脚本 - 主要对外接口
     *
     * @param scriptId 脚本ID
     * @param steps 测试步骤内容
     * @return 执行结果：0表示成功，非0表示失败
     */
    int runTestScript(long scriptId, String steps);

    /**
     * 暂停执行
     */
    void pauseExecution();

    /**
     * 恢复执行
     */
    void resumeExecution();

    /**
     * 停止执行
     */
    void stopExecution();

    /**
     * 检查是否已暂停
     *
     * @return true表示当前执行已暂停
     */
    boolean isPaused();

    /**
     * 检查是否已停止
     *
     * @return true表示当前执行已停止
     */
    boolean isStopped();

    /**
     * 添加执行处理器
     *
     * @param handler 执行处理器
     */
    void addExecuteHandler(BaseTestExecutor.ExecutionHandler handler);

    /**
     * 移除执行处理器
     */
    void removeExecuteHandler();
}
