<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.xiaopeng.xpautotest"
    android:sharedUserId="android.uid.system"
    android:sharedUserMaxSdkVersion="32"
    android:versionCode="9999"
    android:versionName="1.0.0-SNAPSHOT" >

    <uses-sdk
        android:minSdkVersion="28"
        android:targetSdkVersion="30" />

    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.SUPER_APPLICATION_RUNNING" />
    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    <uses-permission android:name="android.permission.MEDIA_PROJECTION" />
    <uses-permission android:name="com.xiaopeng.permission.OTA_SERVICE" />
    <uses-permission android:name="com.xiaopeng.permission.CAR_SERVICE" />
    <uses-permission android:name="com.xiaopeng.permission.ACTIVITY" />
    <uses-permission android:name="com.xiaopeng.permission.SERVICE" />
    <uses-permission android:name="com.xiaopeng.permission.BROADCAST" />
    <uses-permission android:name="xiaopeng.permission.DATA_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="com.xiaopeng.permission.DATA_SERVICE" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="com.xiaopeng.permission.SYSTEM_DELEGATE" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.XIAOPENG_APP" />

    <permission
        android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.xiaopeng.xpautotest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.xiaopeng.xpautotest.App"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:configChanges="keyboard|keyboardHidden|navigation"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/autotest_AppTheme" >
        <meta-data
            android:name="com.xiaopeng.lib.lib_feature_modules"
            android:value="com.xiaopeng.carcontrol,com.xiaopeng.caraccount,com.xiaopeng.carspeechservice" />

        <activity
            android:name="com.xiaopeng.xpautotest.ui.MainActivity"
            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/autotest_AppTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name="com.xiaopeng.xpautotest.service.TestExecutionService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice" />
        <service
            android:name="com.xiaopeng.xpautotest.service.DebuggingModeService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice" />
        <service
            android:name="com.xiaopeng.xpautotest.accessibility.AutoTestAccessibilityService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/service_config" />
        </service>

        <receiver
            android:name="com.xiaopeng.xpautotest.receiver.StartBroadcastReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.xiaoppeng.xpautotest.OPEN_APP_ACTION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.xiaopeng.xpautotest.service.OSSUploadService"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.xiaopeng.xpautotest.action.START_OSS_UPLOAD_SERVICE" />
            </intent-filter>
        </service>
        <service
            android:name="com.xiaopeng.xpautotest.trace.service.CanDataCollectService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice" >
        </service>
        <service android:name="org.eclipse.paho.android.service.MqttService" />

        <provider
            android:name="com.xiaopeng.lib.framework.netchannelmodule.common.ContextNetStatusProvider"
            android:authorities="com.xiaopeng.xpautotest.netmodule.provider"
            android:exported="false" >
        </provider>
        <provider
            android:name="com.xiaopeng.lib.apirouter.server.ApiPublisherProvider"
            android:authorities="com.xiaopeng.xpautotest.api.publisher"
            android:exported="true" />
        <provider
            android:name="com.xiaopeng.lib.feature.XpFeatureProvider"
            android:authorities="com.xiaopeng.xpautotest.XpFeatureProvider"
            android:exported="false"
            android:initOrder="1100" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.xiaopeng.xpautotest.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>