plugins {
    id 'com.android.library'
}

android {
    namespace 'com.xiaopeng.executor'
    compileSdk 34

    defaultConfig {
        minSdk 28

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        exclude "lib/arm64-v8a/libc++_shared.so"
    }
}

dependencies {

//    implementation 'androidx.appcompat:appcompat:1.7.0'
//    implementation 'com.google.android.material:material:1.12.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation project(':community')
    implementation('com.xiaopeng.lib:lib_utils:1.7.5.9'){
        changing = true
        exclude group: 'org.apache.commons', module: 'commons-compress'
    }
    compileOnly 'com.xiaopeng.jar:carapi:8155_LA_xpdev-SNAPSHOT'
    compileOnly 'com.xiaopeng.jar:xuimanager:2.0.0-SNAPSHOT'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'com.xiaopeng.lib:lib_feature:5.8.0.1'
    implementation 'com.quickbirdstudios:opencv:4.5.3.0'

//    implementation(project(':opencv')) {
//        exclude group: 'org.opencv', module: 'libc++_shared'
//    }
}