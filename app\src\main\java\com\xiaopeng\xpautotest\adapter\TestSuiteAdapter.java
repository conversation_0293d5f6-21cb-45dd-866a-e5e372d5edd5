package com.xiaopeng.xpautotest.adapter;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.xiaopeng.xpautotest.BuildConfig;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.model.ITestSuiteItem;
import com.xiaopeng.xpautotest.model.TestSuiteItem;
import com.xiaopeng.xpautotest.helper.ResourceHelper;
import com.xiaopeng.xui.widget.XImageView;
import com.xiaopeng.xui.widget.XTextView;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class TestSuiteAdapter extends RecyclerView.Adapter<TestSuiteAdapter.ViewHolder> {
    private static final String TAG = "TestSuiteAdapter";
    private List<ITestSuiteItem> testSuites;
    private OnSuiteClickedListener listener;
    private int mSelectedPosition = -1;  // 选中的位置

    public TestSuiteAdapter(List<ITestSuiteItem> testSuites, OnSuiteClickedListener listener) {
        this.testSuites = testSuites;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.at_layout_factory_tab_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "onBindViewHolder: " + position);
        }
        ITestSuiteItem testSuite = testSuites.get(position);
        // 设置选中状态
        holder.itemView.setSelected(testSuite.isSelected());

        holder.mSuiteNameView.setText(testSuite.getName());
        holder.mStatusImageView.setImageResource(ResourceHelper.getCaseExecuteStateIcon(testSuite.getStatus()));

        // 根据选中状态改变样式
//        if (testSuite.isSelected()) {
//            holder.itemView.setBackgroundColor(Color.parseColor("#E3F2FD")); // 选中背景色
//            holder.mSuiteNameView.setTextColor(Color.parseColor("#2196F3"));         // 选中文字颜色
//        } else {
//            holder.itemView.setBackgroundColor(Color.TRANSPARENT);          // 默认背景
//            holder.mSuiteNameView.setTextColor(Color.BLACK);                        // 默认文字颜色
//        }

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                setSelectedPosition(position);
                listener.onSuiteClicked(testSuite);
            }
        });
    }

    /**
     * 刷新全部数据, 用于首次加载或者下拉刷新
     * */
    @SuppressLint("NotifyDataSetChanged")
    public void updateData(List<ITestSuiteItem> testSuites, long currentSuiteId, String mode) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, "updateData, TestSuiteList size: " + testSuites.size());
        }
        // 重新加载时，需重置mSelectedPosition
        if (mode.equals("reload")) {
            mSelectedPosition = -1;
        }
        this.testSuites = testSuites;
        if (testSuites != null && !testSuites.isEmpty()) {
            int position = 0;
            if (currentSuiteId > 0) {
                for (int i = 0; i < testSuites.size(); i++) {
                    if (testSuites.get(i).getId() == currentSuiteId) {
                        position = i;
                        break;
                    }
                }
            }
            setSelectedPosition(position);
        }
        notifyDataSetChanged();
    }

    /**
     * 更新单个数据, 用于局部刷新，比如更新某个item的状态，减少性能消耗
     * */
    public void updateItemData(TestSuiteItem testSuite, int position) {
        testSuites.set(position, testSuite);
        notifyItemChanged(position);
    }

    @Override
    public int getItemCount() {
        if (testSuites == null) {
            return 0;
        }
        return testSuites.size();
    }

    private void setPositionSelected(ViewHolder holder, int position) {
        if (position == mSelectedPosition) {
            holder.mItemView.setSelected(true);
        } else {
            holder.mItemView.setSelected(false);
        }
    }

    public void setSelectedPosition(int position) {
        Log.i(TAG, "setSelectedPosition: " + position + ", mSelectedPosition: " + mSelectedPosition);
        if (mSelectedPosition != position) {
            // 清除之前选中项
            if (mSelectedPosition != -1 && mSelectedPosition < testSuites.size()) {
                testSuites.get(mSelectedPosition).setSelected(false);
                notifyItemChanged(mSelectedPosition);
            }
        }
        // 设置新选中项
        mSelectedPosition = position;
        testSuites.get(position).setSelected(true);
        listener.onSuiteClicked(testSuites.get(position));
        notifyItemChanged(position);
    }

    public void setItemChecked(int position) {
        if (this.mSelectedPosition == position) {
            return;
        }

        this.mSelectedPosition = position;

        notifyItemRangeChanged(0, this.testSuites.size());
//        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private View mItemView;
        XTextView mSuiteIdView, mSuiteNameView;
        XImageView mStatusImageView;
//        XButton mExecuteButton;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            mItemView = itemView;
            mSuiteIdView = itemView.findViewById(R.id.tv_tab_index);
            mSuiteNameView = itemView.findViewById(R.id.tv_tab_text);
            mStatusImageView = itemView.findViewById(R.id.iv_tab_status);

//            mExecuteButton = itemView.findViewById(R.id.button_execute);
        }
    }

    public void setOnClickedListener(OnSuiteClickedListener listener) {
        this.listener = listener;
    }

    public interface OnSuiteClickedListener {
        void onSuiteClicked(ITestSuiteItem testSuite);
    }
}
