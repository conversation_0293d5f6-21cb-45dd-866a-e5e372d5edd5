<?xml version="1.0" encoding="utf-8"?>
<com.xiaopeng.xui.widget.XConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.xiaopeng.xui.widget.XLoading
        android:id="@+id/loading"
        style="@style/XLoading.XLarge"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.xiaopeng.xui.widget.XTextView
        android:id="@+id/textview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:textColor="@color/x_theme_text_01"
        android:textSize="@dimen/x_font_title_03_size"
        android:textAlignment="center"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/loading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</com.xiaopeng.xui.widget.XConstraintLayout>