package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IMcuController;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SetMcuPropAction extends BaseAction {
    private static final String TAG = "SetMcuPropAction";
    IMcuController mIMcuController = null;

    @Override
    public void init(CarApiConfig config) {
        super.init(config);
        initControllers();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String key = (String) context.getStringParam();
        String value = (String) context.getStringParam();
        if (key == null || value == null) {
            throw new ActionException("key or value is null!", FailureCode.SI001);
        }
        boolean res = setMcuProp(key, value);
        FileLogger.i(TAG, "key: " + key + ", value: " + value + ", res: " + res);
        if (res) {
            return TestResult.success("Mcu set property success: " + key);
        } else {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB005);
            return TestResult.failure("Mcu set property failed: " + key);
        }
    }

    public boolean setMcuProp(String key, String value) {
        return mIMcuController.setValue(key, value);
    }

    private void initControllers() {
        mIMcuController = (IMcuController) carClientWrapper.getController(CarClientWrapper.XP_MCU_SERVICE);
    }
}
