package com.xiaopeng.xpautotest.system;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import org.opencv.android.OpenCVLoader;
import org.opencv.android.Utils;
import org.opencv.calib3d.Calib3d;
import org.opencv.core.Core;
import org.opencv.core.CvType;
import org.opencv.core.DMatch;
import org.opencv.core.KeyPoint;
import org.opencv.core.Mat;
import org.opencv.core.MatOfDMatch;
import org.opencv.core.MatOfKeyPoint;
import org.opencv.core.MatOfPoint2f;
import org.opencv.core.Point;
import org.opencv.core.Scalar;
import org.opencv.core.Size;
import org.opencv.core.Rect;
//import org.opencv.features2d.AKAZE;
//import org.opencv.features2d.BRISK;
//import org.opencv.features2d.ORB;
import org.opencv.features2d.SIFT;
import org.opencv.features2d.BFMatcher;
import org.opencv.features2d.DescriptorMatcher;
import org.opencv.features2d.Feature2D;
import org.opencv.imgproc.Imgproc;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


public class ImageRecognition {
    private static final String TAG = "ImageRecognition";

    // 模板匹配的置信度阈值
    private static final double TEMPLATE_THRESHOLD = 0.8;
    // 特征匹配的置信度阈值
    private static final double FEATURE_THRESHOLD = 0.7;
    // 特征匹配的最小优质匹配点数，默认5
    private static final int MIN_GOOD_MATCHES = 5;
    // 图像缩放比例，默认0.8
    private static final double IMAGE_SCALE_FACTOR = 0.8;
    private Mat screenMat;
    private Mat templateMat;
    private static ImageRecognition instance;

    private ImageRecognition() {
        if (!OpenCVLoader.initDebug()) {
            throw new RuntimeException("Failed to initialize OpenCV");
        } else {
            Log.i(TAG, "OpenCV initialized successfully");
        }
    }

    public static synchronized ImageRecognition getInstance() {
        if (instance == null) {
            instance = new ImageRecognition();
        }
        return instance;
    }

    public MatchResult match(String screenImagePath, String templateImagePath) {
        return match(screenImagePath, templateImagePath, null, IMAGE_SCALE_FACTOR, MIN_GOOD_MATCHES);
    }

    public MatchResult fastMatch(String screenImagePath, String templateImagePath, String region, double scaleFactor, int minGoodMatches) {
        Rect cropRegionRect = null;
        if (region != null && !region.isEmpty()) {
            String[] parts = region.split(",");
            if (parts.length == 4) {
                try {
                    int left = Integer.parseInt(parts[0]);
                    int top = Integer.parseInt(parts[1]);
                    int right = Integer.parseInt(parts[2]);
                    int bottom = Integer.parseInt(parts[3]);
                    cropRegionRect = new Rect(left, top, right - left, bottom - top);
                } catch (NumberFormatException e) {
                    String message = "Invalid region format: " + region + ". Expected format: left,top,right,bottom";
                    FileLogger.e(TAG, message, e);
                    return new MatchResult(null, 0, message);
                }
            } else {
                String message = "Region must have 4 comma-separated values: left,top,right,bottom";
                FileLogger.e(TAG, message);
                return new MatchResult(null, 0, message);
            }
        }
        return match(screenImagePath, templateImagePath, cropRegionRect,
                scaleFactor > 0 ? scaleFactor : IMAGE_SCALE_FACTOR,
                minGoodMatches > 0 ? minGoodMatches : MIN_GOOD_MATCHES);
    }

    public MatchResult match(String screenImagePath, String templateImagePath, double scaleFactor) {
        return match(screenImagePath, templateImagePath, null, scaleFactor, MIN_GOOD_MATCHES);
    }

    public MatchResult match(String screenImagePath, String templateImagePath, Rect cropRegionRect) {
        return match(screenImagePath, templateImagePath, cropRegionRect, IMAGE_SCALE_FACTOR, MIN_GOOD_MATCHES);
    }

    public MatchResult match(String screenImagePath, String templateImagePath, Rect cropRegionRect, double scaleFactor) {
        return match(screenImagePath, templateImagePath, cropRegionRect, scaleFactor, MIN_GOOD_MATCHES);
    }

    public MatchResult match(String screenImagePath, String templateImagePath, Rect cropRegionRect, double scaleFactor, int minGoodMatches) {
        String message = "";
        try {
            // 1. 加载屏幕截图
//            Bitmap screenBitmap = BitmapFactory.decodeFile(screenImagePath);
//            screenMat = new Mat();
//            Utils.bitmapToMat(screenBitmap, screenMat);
            screenMat = loadMat(screenImagePath);

            // 2. 加载模板图像
//            loadTemplate(templateImagePath);
            templateMat = loadMat(templateImagePath);

            // 3. 根据选择的算法进行匹配
            Rect matchRect = null;
            double confidence = 0;
            long startTime = System.currentTimeMillis();

            // 如果指定了裁剪区域，则裁剪屏幕图像
            if (cropRegionRect != null) {
                screenMat = cropRegion(screenMat, cropRegionRect);
            }

            // 图像预处理，缩小模板图像以提高匹配速度
            templateMat = resizeImage(templateMat, scaleFactor);
            screenMat = resizeImage(screenMat, scaleFactor);

            // 使用模板匹配算法
            MatchResult result = matchWithTemplate(scaleFactor);
            matchRect = result.matchRect;
            confidence = result.confidence;
            long duration = System.currentTimeMillis() - startTime;

            // 4. 显示结果
            if (matchRect != null) {
                // 在截图上绘制匹配区域
//                Bitmap resultBitmap = drawMatchRect(currentScreenshot, matchRect);

                message = String.format("匹配成功！\n位置: [%d, %d, %d, %d]\n置信度: %.2f\n耗时: %dms",
                        matchRect.x, matchRect.y,
                        matchRect.width, matchRect.height,
                        confidence, duration);
                FileLogger.i(TAG, message);
                result.setMessage(message);
                return result;
            } else {
                FileLogger.w(TAG, "模板匹配失败！未找到匹配区域，使用特征匹配算法进行尝试。");
                startTime = System.currentTimeMillis();
                // 使用特征匹配算法
                MatchResult featureMatchResult = matchWithFeature(SIFT.create(), scaleFactor, minGoodMatches);

                if (featureMatchResult.matchRect == null) {
                    message = "特征匹配失败！未找到匹配区域。";
                    FileLogger.e(TAG, message);
                } else {
                    message = String.format("匹配成功！\n位置: [%d, %d, %d, %d]\n置信度: %.2f\n耗时: %dms",
                            featureMatchResult.matchRect.x, featureMatchResult.matchRect.y,
                            featureMatchResult.matchRect.width, featureMatchResult.matchRect.height,
                            featureMatchResult.confidence, System.currentTimeMillis() - startTime);
                    FileLogger.i(TAG, message);
                    result.setMessage(message);
                    return featureMatchResult;
                }
            }

            // 保存结果图像用于调试
//            saveResultImage();
        } catch (OutOfMemoryError e) {
            message = "内存不足，无法处理图像";
            FileLogger.e(TAG, "内存不足，无法处理图像");
        } catch (IllegalArgumentException e) {
            message = "参数错误，可能是图像路径无效或图像格式不支持";
            FileLogger.e(TAG, "参数错误，可能是图像路径无效或图像格式不支持", e);
        } catch (Exception e) {
            message = "图像匹配过程中发生错误: ";
            FileLogger.e(TAG, message, e);
        }

        return new MatchResult(null, 0, message);
    }

    private void loadTemplate(String templateImagePath) {
        Bitmap templateBitmap = BitmapFactory.decodeFile(templateImagePath);
        templateMat = new Mat();
        Utils.bitmapToMat(templateBitmap, templateMat);
    }

    private Mat loadMat(String imagePath) {
        File file = new File(imagePath);
        if (!file.exists()) {
            imagePath = imagePath.replace(".png", ".jpg");
        }
        Bitmap bitmap = BitmapFactory.decodeFile(imagePath);
        Mat mat = new Mat();
        Utils.bitmapToMat(bitmap, mat);
        return mat;
    }

    public Mat resizeImage(Mat inputImage, double scaleFactor) {
        Mat resizedImage = new Mat();
        Size newSize = new Size(inputImage.width() * scaleFactor, inputImage.height() * scaleFactor);
        Imgproc.resize(inputImage, resizedImage, newSize, 0, 0, Imgproc.INTER_AREA);
        return resizedImage;
    }

    public Mat cropRegion(Mat inputImage, Rect region) {
        // 检查区域是否在图像范围内
        if (region.x >= 0 && region.y >= 0 &&
                region.x + region.width <= inputImage.cols() &&
                region.y + region.height <= inputImage.rows()) {
            // 截取指定区域
            return inputImage.submat(region);
        } else {
            throw new IllegalArgumentException("指定的区域超出图像范围");
        }
    }

    // 模板匹配算法
    private MatchResult matchWithTemplate(double scaleFactor) {
        // 转换为灰度图
        Mat screenGray = new Mat();
        Mat templateGray = new Mat();
        Imgproc.cvtColor(screenMat, screenGray, Imgproc.COLOR_BGR2GRAY);
        Imgproc.cvtColor(templateMat, templateGray, Imgproc.COLOR_BGR2GRAY);
        int templateWidth = templateMat.width();
        int templateHeight = templateMat.height();

        // 模板匹配
        Mat result = new Mat();
        Imgproc.matchTemplate(screenGray, templateGray, result, Imgproc.TM_CCOEFF_NORMED);

        // 寻找最佳匹配位置
        Core.MinMaxLocResult mmr = Core.minMaxLoc(result);

        // 应用阈值
        if (mmr.maxVal < TEMPLATE_THRESHOLD) {
            String message = String.format("template configuration reliability below threshold: %.2f, Threshold: %.2f", mmr.maxVal, TEMPLATE_THRESHOLD);
            FileLogger.w(TAG, message);
            return new MatchResult(null, mmr.maxVal);
        }

        Point matchLoc = mmr.maxLoc;
        int x = (int) (matchLoc.x / scaleFactor);
        int y = (int) (matchLoc.y / scaleFactor);
        Rect rect = new Rect(
                x,
                y,
                x + templateWidth,
                y + templateHeight
        );
        return new MatchResult(rect, mmr.maxVal);
    }

    // 特征匹配算法
    private MatchResult matchWithFeature(Feature2D featureDetector, double scaleFactor, int minGoodMatches) {
        // 转换为灰度图
        Mat screenGray = new Mat();
        Mat templateGray = new Mat();
        Imgproc.cvtColor(screenMat, screenGray, Imgproc.COLOR_BGR2GRAY);
        Imgproc.cvtColor(templateMat, templateGray, Imgproc.COLOR_BGR2GRAY);

        // 检测关键点并计算描述符
        MatOfKeyPoint screenKeyPoints = new MatOfKeyPoint();
        Mat screenDescriptors = new Mat();
        featureDetector.detectAndCompute(screenGray, new Mat(), screenKeyPoints, screenDescriptors);

        MatOfKeyPoint templateKeyPoints = new MatOfKeyPoint();
        Mat templateDescriptors = new Mat();
        featureDetector.detectAndCompute(templateGray, new Mat(), templateKeyPoints, templateDescriptors);

        // 检查描述符是否为空
        if (screenDescriptors.empty() || templateDescriptors.empty()) {
            FileLogger.e(TAG, "无法计算特征描述符");
            return new MatchResult(null, 0);
        }

        // 将描述符转换为 CV_32F 类型（FLANN 匹配器需要浮点型描述符）
        if (screenDescriptors.type() != CvType.CV_32F) {
            screenDescriptors.convertTo(screenDescriptors, CvType.CV_32F);
        }
        if (templateDescriptors.type() != CvType.CV_32F) {
            templateDescriptors.convertTo(templateDescriptors, CvType.CV_32F);
        }

        // 使用 FLANN 匹配器进行特征匹配
        DescriptorMatcher matcher = DescriptorMatcher.create(DescriptorMatcher.FLANNBASED);
        List<MatOfDMatch> matches = new ArrayList<>();
        matcher.knnMatch(templateDescriptors, screenDescriptors, matches, 2);

        // 筛选优质匹配点
        List<DMatch> goodMatches = new ArrayList<>();
        for (MatOfDMatch matOfDMatch : matches) {
            DMatch[] matchPair = matOfDMatch.toArray();
            if (matchPair.length >= 2 && matchPair[0].distance < 0.7 * matchPair[1].distance) {
                goodMatches.add(matchPair[0]);
            }
        }

        // 计算匹配质量（置信度）
        double confidence = (double) goodMatches.size() / matches.size();
        FileLogger.i(TAG, "优质匹配点数量: " + goodMatches.size() + "/" + matches.size() +
                " 置信度: " + confidence);

        if (goodMatches.size() < minGoodMatches) {
            return new MatchResult(null, confidence);
        }

        // 提取匹配点位置
        List<KeyPoint> templateKeyPointsList = templateKeyPoints.toList();
        List<KeyPoint> screenKeyPointsList = screenKeyPoints.toList();

        MatOfPoint2f templatePoints = new MatOfPoint2f();
        MatOfPoint2f screenPoints = new MatOfPoint2f();

        List<Point> templatePointsList = new ArrayList<>();
        List<Point> screenPointsList = new ArrayList<>();

        for (DMatch goodMatch : goodMatches) {
            templatePointsList.add(templateKeyPointsList.get(goodMatch.queryIdx).pt);
            screenPointsList.add(screenKeyPointsList.get(goodMatch.trainIdx).pt);
        }

        templatePoints.fromList(templatePointsList);
        screenPoints.fromList(screenPointsList);

        // 使用单应性矩阵计算位置
        Mat homography = Calib3d.findHomography(templatePoints, screenPoints, Calib3d.RANSAC, 5);

        if (homography.empty()) {
            FileLogger.e(TAG, "无法计算单应性矩阵，可能是匹配点分布不均或数量不足！");
            return new MatchResult(null, confidence);
        }

        // 计算模板在屏幕中的位置
        double[] templateCorners = {
                0, 0,
                templateMat.width(), 0,
                templateMat.width(), templateMat.height(),
                0, templateMat.height()
        };

        Mat transformedCorners = new Mat(4, 1, CvType.CV_32FC2);
        Mat srcCorners = new Mat(4, 1, CvType.CV_32FC2);
        srcCorners.put(0, 0, templateCorners);

        Core.perspectiveTransform(srcCorners, transformedCorners, homography);

        // 计算边界框
        float[] points = new float[8];
        transformedCorners.get(0, 0, points);

        int minX = Integer.MAX_VALUE, minY = Integer.MAX_VALUE;
        int maxX = Integer.MIN_VALUE, maxY = Integer.MIN_VALUE;

        for (int i = 0; i < 4; i++) {
            int x = (int) points[i * 2];
            int y = (int) points[i * 2 + 1];
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
        }

        // 创建边界矩形
        Rect rect = new Rect((int) (minX/scaleFactor), (int) (minY/scaleFactor), (int) (maxX/scaleFactor), (int) (maxY/scaleFactor));
        return new MatchResult(rect, confidence);
    }

    // 在截图上绘制匹配区域
    private Bitmap drawMatchRect(Bitmap original, Rect rect) {
        // 创建可修改的位图副本
        Bitmap result = original.copy(Bitmap.Config.ARGB_8888, true);

        // 转换为Mat进行绘制
        Mat resultMat = new Mat();
        Utils.bitmapToMat(result, resultMat);

        // 绘制矩形
        Imgproc.rectangle(
                resultMat,
                new Point(rect.x, rect.y),
                new Point(rect.width, rect.height),
                new Scalar(0, 255, 0), // 绿色
                4
        );

        // 添加文本
        Imgproc.putText(
                resultMat,
                "Matched",
                new Point(rect.x, rect.y - 10),
                Imgproc.FONT_HERSHEY_SIMPLEX,
                1.5,
                new Scalar(0, 255, 0),
                3
        );

        // 转换回Bitmap
        Utils.matToBitmap(resultMat, result);
        return result;
    }

    // 保存结果图像
//    private void saveResultImage() {
//        try {
//            File directory = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);
//            File file = new File(directory, "airtest_result_" + System.currentTimeMillis() + ".png");
//
//            FileOutputStream fos = new FileOutputStream(file);
//            currentScreenshot.compress(Bitmap.CompressFormat.PNG, 100, fos);
//            fos.close();
//
//            Log.d(TAG, "结果图像已保存: " + file.getAbsolutePath());
//        } catch (Exception e) {
//            Log.e(TAG, "保存图像失败", e);
//        }
//    }

    // 用于存储模板匹配结果
    public static class MatchResult {
        Rect matchRect;
        double confidence;
        boolean matched;
        String message;

        MatchResult(Rect matchRect, double confidence) {
            this.matchRect = matchRect;
            this.confidence = confidence;
            this.matched = matchRect != null;
        }

        MatchResult(Rect matchRect, double confidence, String message) {
            this.matchRect = matchRect;
            this.confidence = confidence;
            this.matched = matchRect != null;
            this.message = message;
        }

        public Rect getMatchRect() {
            return matchRect;
        }

        public int getMatchRectCenterX() {
            return matchRect != null ? matchRect.x + (matchRect.width - matchRect.x) / 2 : -1;
        }

        public int getMatchRectCenterY() {
            return matchRect != null ? matchRect.y + (matchRect.height - matchRect.y) / 2 : -1;
        }

        public double getConfidence() {
            return confidence;
        }

        public boolean isMatched() {
            return matched;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }
    }

    private static class TemplateMatchResult {
        Rect matchRect;
        double confidence;
        boolean matched;
        String message;

        TemplateMatchResult(Rect matchRect, double confidence) {
            this.matchRect = matchRect;
            this.confidence = confidence;
            this.matched = matchRect != null;
        }
    }

    // 用于存储特征匹配结果
    private static class FeatureMatchResult {
        Rect matchRect;
        double confidence;
        boolean matched;
        String message;

        FeatureMatchResult(Rect matchRect, double confidence) {
            this.matchRect = matchRect;
            this.confidence = confidence;
            this.matched = matchRect != null;
        }
    }
}
