package com.xiaopeng.xpautotest.utils;

import android.content.Context;
import android.content.res.AssetManager;

import com.xiaopeng.xpautotest.community.utils.Log;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class AssetsUtils {
    private static final String TAG = "AssetsUtils";

    /**
     * 获取文件内容
     *
     */
    public static String getFileString(Context context, String fileName) {
        StringBuilder stringBuilder = new StringBuilder();
        InputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        try {
            AssetManager assetManager = context.getAssets();
            is = assetManager.open(fileName);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            String line;
            while ((line = br.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            Log.e(TAG, "---error = " + e.getMessage());
        } finally {
            safeClose(br);
            safeClose(isr);
            safeClose(is);
        }
        Log.i(TAG, "getFileString file : " + fileName);
        return stringBuilder.toString();
    }

    private static void safeClose(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                Log.e(TAG, "---error = " + e.getMessage());
            }
        }
    }

    /**
     * 判断assets文件夹下是否存在文件
     */
    public static boolean isFileExist(Context context, String path,String fileName) {
        AssetManager assetManager = context.getAssets();
        try {
            String[] files = assetManager.list(path);
            if (files == null) {
                return false;
            }
            for (String file : files) {
                if (file.equals(fileName)) {
                    return true;
                }
            }
        } catch (IOException e) {
            Log.e(TAG, "---error = " + e.getMessage());
        }
        return false;
    }

    /**
     * 递归搜索指定文件在assets目录下的路径
     *
     * @param context 上下文
     * @param path 当前遍历的路径
     * @param targetFileName 要查找的文件名
     * @return 文件存在时返回文件的路径，否则返回null
     */
    public static String findFilePath(Context context, String path, String targetFileName) {
        AssetManager assetManager = context.getAssets();
        try {
            String[] assets = assetManager.list(path);
            if (assets != null) {
                for (String asset : assets) {
                    String fullPath = path.isEmpty() ? asset : path + "/" + asset;
                    // 检查当前asset是否是我们要找的文件
                    if (asset.equals(targetFileName)) {
                        return fullPath;
                    } else {
                        // 检查当前asset是否是目录
                        String[] subAssets = assetManager.list(fullPath);
                        if (subAssets != null && subAssets.length > 0) {
                            // 如果是目录，递归搜索
                            String result = findFilePath(context, fullPath, targetFileName);
                            if (result != null) {
                                // 如果在子目录中找到了文件，返回结果
                                return result;
                            }
                        }
                        // 如果当前asset不是目录，继续下一个asset
                    }
                }
            }
        } catch (IOException e) {
            // 处理异常
            Log.e(TAG, "---error = " + e.getMessage());
        }
        // 文件未找到
        return null;
    }
}
