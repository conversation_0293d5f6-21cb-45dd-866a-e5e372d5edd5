package com.xiaopeng.xpautotest.client;

import android.car.XpCarFeatures;

import com.xiaopeng.xpautotest.bean.ErrorCode;
import com.xiaopeng.xpautotest.community.utils.CompressUtils;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.bean.VehicleInfo;
import com.xiaopeng.xpautotest.client.api.ApiException;
import com.xiaopeng.xpautotest.client.api.ApiResponse;
import com.xiaopeng.xpautotest.client.api.TaskApiImpl;
import com.xiaopeng.xpautotest.client.api.VehicleApiImpl;
import com.xiaopeng.xpautotest.helper.TestDataStorageHelper;
import com.xiaopeng.xpautotest.utils.SystemPropertiesUtils;
import com.xiaopeng.xpautotest.utils.CarUtils;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ClientApiRepository {
    private static final String TAG = "ClientApiRepository";
//    private static TestRepository instance;
    private final TestApiResponseListener listener;

    public ClientApiRepository(TestApiResponseListener listener) {
        this.listener = listener;

    }

//    private TestRepository() {
//        vehicleInfo = new VehicleInfo(XpCarFeatures.getVinCode(), CarUtils.getCarType(),
//                XpCarFeatures.getSoftwareVersionCode(), XpCarFeatures.getSoftwareVersionCode(), "");
//    }
//
//    public static TestRepository getInstance() {
//        if (instance == null) {
//            instance = new TestRepository();
//        }
//        return instance;
//    }

    public void register(VehicleInfo vehicleInfo) {
        VehicleApiImpl vehicleApi = new VehicleApiImpl();
        Log.i(TAG, "register: " + vehicleInfo);
        vehicleApi.register(vehicleInfo, new Callback<ApiResponse<TestTaskBean>>() {
            @Override
            public void onResponse(Call<ApiResponse<TestTaskBean>> call, Response<ApiResponse<TestTaskBean>> response) {
                if (response.isSuccessful()) {
                    TestTaskBean taskInfo = response.body().getData();
                    FileLogger.i(TAG, "register onResponse: " + taskInfo);
                    listener.onTaskInfoReceived(taskInfo);
                } else {
                    // 业务故障的情况
                    FileLogger.e(TAG, "register onResponse Server error: " + response.code());
                    listener.onRegisterFailure(new ApiException(response.code(), "Server error: "));

                }
            }

            @Override
            public void onFailure(Call<ApiResponse<TestTaskBean>> call, Throwable t) {
                // 无任务的情况
                FileLogger.e(TAG, "register onFailure: " + t.getMessage());
                listener.onRegisterFailure(t);
            }
        });
    }

    public void heartbeat(String vin) {
        VehicleApiImpl vehicleApi = new VehicleApiImpl();
        vehicleApi.heartbeat(vin, new Callback<ApiResponse<TestTaskBean>>() {
            @Override
            public void onResponse(Call<ApiResponse<TestTaskBean>> call, Response<ApiResponse<TestTaskBean>> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "heartbeat " + vin + ", onResponse: " + response.body());
                    TestTaskBean taskInfo = response.body().getData();
                    listener.onHeartbeatTaskInfoReceived(taskInfo);
                } else {
                    // 业务故障的情况，只记录日志就行
                    Log.e(TAG, "heartbeat " + vin + " onResponse Server error: " + response.code());
                    //listener.onFailure(new ApiException(response.code(), "Server error: " + response.code()));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<TestTaskBean>> call, Throwable t) {
                Log.e(TAG, "heartbeat " + vin + ", onFailure: " + t.getMessage());
                listener.onHeartbeatFailure(t);
            }
        });
    }

    public void downloadScript(String url) {
        TaskApiImpl taskApi = new TaskApiImpl();

        taskApi.downloadScript(url, new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                if (response.isSuccessful()) {
                    TestDataStorageHelper.getInstance().saveScripts(response.body(), success -> {
                        if (success) {
                            listener.onScriptsDownloaded();
                        }
                    });
                } else {
                    FileLogger.e(TAG, "downloadScript onResponse Server error: " + response.code());
                    listener.onFailure(new Exception("Server error: " + response.code()));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                FileLogger.e(TAG, "downloadScript onFailure Download failed" + t.getMessage());
                listener.onDownloadFailure(t);
            }
        });
    }

    public void downloadScriptOSS(String url) {
        FileDownloader.downloadFileAsync(
                url,
                Constant.AUTOTEST_SCRIPT_ZIP_FILE,
                new FileDownloader.DownloadCallback() {
                    @Override
                    public void onSuccess(String localFilePath) {
                        CompressUtils.unzip(localFilePath);
                        listener.onScriptsDownloaded();
                    }

                    @Override
                    public void onFailure(Exception e) {
                        listener.onDownloadFailure(new ApiException(ErrorCode.ERROR_API_DOWNLOAD_SCRIPT.getCode(), "download script failed: " + e.getMessage()));
                    }
                }
        );
    }

    public void startTest(TestTaskBean taskInfo) {
        Log.i(TAG, "startTest: " + taskInfo);
        TaskApiImpl taskApi = new TaskApiImpl();

        taskApi.startTest(taskInfo.getId(), taskInfo, new Callback<ApiResponse<Long>>() {
            @Override
            public void onResponse(Call<ApiResponse<Long>> call, Response<ApiResponse<Long>> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "startTest onResponse: " + response.body());
                    long executionId = response.body().getData();  // 获取执行ID
                    listener.onTestStarted(executionId);
                } else {
                    Log.e(TAG, "startTest onResponse Server error: " + response.code());
//                    listener.onFailure(new ApiException(response.code(), "Server error: " + response.code()));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Long>> call, Throwable t) {
                Log.e(TAG, "startTest onFailure: " + t.getMessage());
//                listener.onFailure(t);
            }
        });
    }

    public void stopTest(TestTaskBean taskInfo) {
        TaskApiImpl taskApi = new TaskApiImpl();

        taskApi.stopTest(taskInfo.getExecutionId(), new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "stopTest onResponse: " + response.body());
                } else {
                    Log.e(TAG, "stopTest onResponse Server error: " + response.code());
//                    listener.onFailure(new ApiException(response.code(), "Server error: " + response.code()));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "stopTest onFailure: " + t.getMessage());
//                listener.onFailure(t);
            }
        });
    }

//    public void reportResult(long taskExecutionId, TestResultEntity testResult) {
//        Log.i(TAG, "reportResult: " + taskExecutionId + ", " + testResult);
//        TaskApiImpl taskApi = new TaskApiImpl();
//
//        taskApi.reportResult(taskExecutionId, testResult, new Callback<ApiResponse>() {
//            @Override
//            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
//                if (response.isSuccessful()) {
//                    Log.i(TAG, "reportResult onResponse: " + response.body());
//                } else {
//                    Log.e(TAG, "reportResult onResponse Server error: " + response.code());
//                }
//            }
//
//            @Override
//            public void onFailure(Call<ApiResponse> call, Throwable t) {
//                Log.e(TAG, "reportResult onFailure: " + t.getMessage());
//            }
//        });
//    }
//
//    public void finishTest(TestTaskBean taskInfo) {
//        TaskApiImpl taskApi = new TaskApiImpl();
//
//        taskApi.finishTest(taskInfo.getId(), new Callback<ApiResponse>() {
//            @Override
//            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
//                if (response.isSuccessful()) {
//                    Log.i(TAG, "finishTest onResponse: " + response.body());
//                } else {
//                    Log.e(TAG, "finishTest onResponse Server error: " + response.code());
//                }
//            }
//
//            @Override
//            public void onFailure(Call<ApiResponse> call, Throwable t) {
//                Log.e(TAG, "finishTest onFailure: " + t.getMessage());
//            }
//        });
//    }

    public interface TestApiResponseListener {
        void onTaskInfoReceived(TestTaskBean taskInfo);
        void onRegisterFailure(Throwable t);
        void onScriptsDownloaded();
        void onTestStarted(long executionId);
        void onFailure(Throwable t);
        void onHeartbeatTaskInfoReceived(TestTaskBean taskInfo);
        void onHeartbeatFailure(Throwable t);
        void onDownloadFailure(Throwable t);
    }
}
