package com.xiaopeng.executor.action.variable;

import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.executor.bean.IActionFactory;
import com.xiaopeng.executor.bean.TechType;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 变量动作工厂
 * 
 * 负责创建和管理变量相关的Action：
 * 1. 注册变量相关的Action
 * 2. 提供Action实例的创建和缓存
 * 3. 支持变量功能的统一配置
 */
public class VariableFactory implements IActionFactory {
    
    private final VariableConfig config;
    
    // Action创建器映射
    private final Map<String, Supplier<IAction<VariableConfig>>> actionMap = new HashMap<>();
    
    // Action实例缓存
    private final Map<String, IAction<?>> actionInstanceMap = new HashMap<>();
    
    /**
     * 构造函数
     * 
     * @param config 变量配置
     */
    public VariableFactory(VariableConfig config) {
        this.config = config;
        registerActions();
    }
    
    /**
     * 注册所有变量相关的Action
     */
    private void registerActions() {
        // 注册Set动作
        register("Set", SetVariableAction::new);
        // 注册GetValueByConf动作
        register("GetValueByFeature", GetValueByFeatureAction::new);
        // 注册变量比较动作（用于条件判断）
        register("VariableCompare", VariableCompareAction::new);
    }
    
    /**
     * 注册Action创建器
     * 
     * @param actionName Action名称
     * @param supplier Action创建器
     */
    private void register(String actionName, Supplier<IAction<VariableConfig>> supplier) {
        actionMap.put(actionName.toLowerCase(), supplier);
    }
    
    @Override
    public IAction<?> createAction(String actionName) {
        Supplier<IAction<VariableConfig>> supplier = actionMap.get(actionName.toLowerCase());
        if (supplier == null) {
            throw new IllegalArgumentException("Unknown variable action: " + actionName);
        }
        
        IAction<VariableConfig> action = supplier.get();
        action.init(config); // 注入配置
        return action;
    }
    
    @Override
    public IAction<?> getAction(String actionName) {
        String lowerActionName = actionName.toLowerCase();
        
        // 检查缓存
        if (actionInstanceMap.containsKey(lowerActionName)) {
            return actionInstanceMap.get(lowerActionName);
        }
        
        // 创建新实例并缓存
        IAction<?> action = createAction(actionName);
        actionInstanceMap.put(lowerActionName, action);
        
        return action;
    }
    
    @Override
    public boolean containsAction(String actionName) {
        return actionMap.containsKey(actionName.toLowerCase());
    }
    
    @Override
    public boolean supports(TechType techType) {
        // 变量功能支持所有技术类型，因为它是通用功能
        return techType == TechType.VARIABLE || techType == TechType.ACCESSIBILITY_SERVICE
               || techType == TechType.CAR_API;
    }

    /**
     * 获取变量配置
     * 
     * @return 变量配置
     */
    public VariableConfig getConfig() {
        return config;
    }
    
    /**
     * 获取工厂信息摘要
     *
     * @return 工厂信息
     */
    public String getFactoryInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("VariableFactory{");
        sb.append("actions=").append(actionMap.size());
        sb.append(", cached=").append(actionInstanceMap.size());
        sb.append("}");
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return getFactoryInfo();
    }
}
