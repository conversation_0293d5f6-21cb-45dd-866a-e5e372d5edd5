package com.xiaopeng.xpautotest.community.bean;

public class CaseExecuteState {

    private int status;
    private String message;
    public static final int SUCCESS = 0;
    public static final int PENDING = 1;
    public static final int FAILURE = 2;
    public static final int EXECUTING = 3;
    // 异常重启中断
    public static final int INTERRUPTED = 4;
    public static final int SKIPPED = 5;

    public static String getResultString(int result) {
        switch (result) {
            case SUCCESS:
                return "PASS";
            case PENDING:
                return "PENDING";
            case FAILURE:
                return "FAIL";
            case EXECUTING:
                return "EXECUTING";
            case INTERRUPTED:
                return "INTERUPTED";
            case SKIPPED:
                return "SKIPPED";
            default:
                return "UNKNOWN";
        }
    }

    public boolean isExecuting() {
        return EXECUTING == getStatus();
    }

    public boolean isSuccess() {
        return SUCCESS == getStatus();
    }

    public boolean isFailure() {
        return FAILURE == getStatus();
    }
    public boolean isInterrupted() {
        return INTERRUPTED == getStatus();
    }
    public boolean isSkipped() {
        return SKIPPED == getStatus();
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
