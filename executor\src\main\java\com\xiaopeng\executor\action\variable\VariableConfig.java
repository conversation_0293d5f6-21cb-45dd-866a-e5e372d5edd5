package com.xiaopeng.executor.action.variable;

import com.xiaopeng.executor.bean.ActionConfig;
import com.xiaopeng.executor.bean.VariableContext;

/**
 * 变量动作配置类
 *
 * 为变量相关的Action提供配置支持：
 * 1. 提供VariableContext的访问
 * 2. 管理变量操作的全局配置
 */
public class VariableConfig extends ActionConfig {

    private static final String TAG = "VariableConfig";
    private static VariableConfig instance;

    // 全局变量上下文
    private VariableContext globalVariableContext;

    /**
     * 私有构造函数
     */
    private VariableConfig() {
        // 私有构造函数，防止外部实例化
    }

    /**
     * 获取单例实例
     *
     * @return VariableConfig实例
     */
    public static synchronized VariableConfig getInstance() {
        if (instance == null) {
            instance = new VariableConfig();
        }
        return instance;
    }

    /**
     * 初始化变量服务
     *
     * @param variableContext 全局变量上下文
     */
    public void initService(VariableContext variableContext) {
        this.globalVariableContext = variableContext != null ? variableContext : new VariableContext();
    }
    
    /**
     * 获取全局变量上下文
     * 
     * @return 变量上下文
     */
    public VariableContext getVariableContext() {
        return globalVariableContext;
    }
    
    /**
     * 设置全局变量上下文
     * 
     * @param variableContext 变量上下文
     */
    public void setVariableContext(VariableContext variableContext) {
        this.globalVariableContext = variableContext;
    }
    

    

    
    /**
     * 重置配置到默认状态
     */
    public void reset() {
        if (globalVariableContext != null) {
            globalVariableContext.clear();
        }
    }
    
    /**
     * 获取配置摘要信息
     * 
     * @return 配置摘要
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("VariableConfig{");

        if (globalVariableContext != null) {
            sb.append("variables=").append(globalVariableContext.size());
        } else {
            sb.append("variables=null");
        }

        sb.append("}");
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return getConfigSummary();
    }
}
