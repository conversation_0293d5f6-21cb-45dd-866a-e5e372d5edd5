package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.utils.ValueCompareUtils;
import com.xiaopeng.utils.ValueTypeUtils;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class CheckTextByIDAction extends BaseAction {
    private static final String TAG = "CheckTextByIDAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String sourceId = (String) context.getStringParam();
        String expectedText = (String) context.getStringParam();
        if (sourceId == null || expectedText == null) {
            throw new ActionException("sourceId or expectedText is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "resourceId: " + sourceId + ", expectedText: " + expectedText);
        String actualText = this.service.getTextById(sourceId);
        String valueType = ValueTypeUtils.getValueType(expectedText);
        FileLogger.i(TAG, "actualValue: " + actualText + ", expectedValue: " + expectedText + ", valueType: " + valueType);
        if (!ValueCompareUtils.compareValue(actualText, expectedText, valueType)) {
            return TestResult.failure("Text mismatch for element " + sourceId + ". Actual: '" + actualText + "', Expected: '" + expectedText + "'");
        }
        return TestResult.success("Text check passed for element " + sourceId + ": '" + expectedText + "'");
    }
}
