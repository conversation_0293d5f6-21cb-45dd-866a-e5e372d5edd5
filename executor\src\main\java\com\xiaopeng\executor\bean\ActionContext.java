package com.xiaopeng.executor.bean;

import com.xiaopeng.executor.action.SmartActionExecutor;
import com.xiaopeng.executor.core.VariableResolver;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;


// 动作上下文（封装参数）
public class ActionContext {
    private static final String TAG = "ActionContext";

    private Long scriptId;
    private int stepId;

    public String getTimeStampString() {
        return timeStampString;
    }

    private String timeStampString;
    private Map<String, Object> params;
    private Queue<String> paramQueue = new LinkedList<>();

    // 变量上下文支持
    private VariableContext variableContext;


    public ActionContext(Map<String, Object> params) {
        this.params = params;
    }

    public ActionContext(Long scriptId, int stepId, String[] params) {
        this.scriptId = scriptId;
        this.stepId = stepId;
        // 将数组转换为 Map
        paramQueue.addAll(Arrays.asList(params));
    }

    /**
     * 构造函数（自动变量解析）
     *
     * @param scriptId 脚本ID
     * @param stepId 步骤ID
     * @param timeStampString 时间戳
     * @param params 原始参数数组
     */
    public ActionContext(Long scriptId, int stepId, String timeStampString, String[] params) {
        this.scriptId = scriptId;
        this.stepId = stepId;
        this.timeStampString = timeStampString;

        // 自动获取全局变量上下文并解析变量
        this.variableContext = getGlobalVariableContext();
        String[] resolvedParams = resolveVariables(params);
        paramQueue.addAll(Arrays.asList(resolvedParams));
    }

    /**
     * 构造函数（测试专用，支持自定义变量上下文）
     *
     * @param scriptId 脚本ID
     * @param stepId 步骤ID
     * @param timeStampString 时间戳
     * @param params 原始参数数组
     * @param variableContext 自定义变量上下文（主要用于测试）
     */
    public ActionContext(Long scriptId, int stepId, String timeStampString, String[] params, VariableContext variableContext) {
        this.scriptId = scriptId;
        this.stepId = stepId;
        this.timeStampString = timeStampString;
        this.variableContext = variableContext;

        // 解析变量引用
        String[] resolvedParams = resolveVariables(params);
        paramQueue.addAll(Arrays.asList(resolvedParams));
    }

    /**
     * 解析参数中的变量引用
     *
     * @param params 原始参数数组
     * @return 解析后的参数数组
     * @throws RuntimeException 当变量不存在时抛出异常
     */
    private String[] resolveVariables(String[] params) {
        if (params == null || params.length == 0 || variableContext == null) {
            return params;
        }

        try {
            return VariableResolver.resolveVariables(params, variableContext);
        } catch (VariableException e) {
            FileLogger.e(TAG, "Failed to resolve variables in parameters: " + e.getDetailedMessage());
            // 将VariableException包装为RuntimeException继续向上抛出
            throw new RuntimeException("Variable resolution failed: " + e.getDetailedMessage(), e);
        }
    }

    public boolean getBooleanParam() {
        if (paramQueue.isEmpty()) {
            return false;
        } else {
            return Boolean.parseBoolean(Objects.requireNonNull(paramQueue.poll()));
        }
    }

    public int getIntParam() {
        if (paramQueue.isEmpty()) {
            return -1;
        } else {
            return Integer.parseInt(Objects.requireNonNull(paramQueue.poll()));
        }
    }

    public String getStringParam() {
        if (paramQueue.isEmpty()) {
            return null;
        } else {
            return paramQueue.poll();
        }
    }

    public Double getDoubleParam() {
        if (paramQueue.isEmpty()) {
            return -1.0;
        } else {
            return Double.parseDouble(Objects.requireNonNull(paramQueue.poll()));
        }
    }
    public Float getFloatParam() {
        if (paramQueue.isEmpty()) {
            return -1.0f;
        } else {
            return Float.parseFloat(Objects.requireNonNull(paramQueue.poll()));
        }
    }

    public String getAllParams() {
        StringBuilder sb = new StringBuilder();
        for (String param : paramQueue) {
            sb.append(param).append(" ");
        }
        return sb.toString().trim();
    }

    public Long getScriptId() {
        return scriptId;
    }

    public int getStepId() {
        return stepId;
    }

    public Object getParam(String key) {
        return params.get(key);
    }

    // 添加类型安全的方法（示例）
    public int getIntParam(String key) {
        return (int) params.get(key);
    }

    public String getStringParam(String key) {
        return (String) params.get(key);
    }

    /**
     * 获取变量上下文
     *
     * @return 变量上下文，可能为null
     */
    public VariableContext getVariableContext() {
        if (variableContext == null) {
            variableContext = getGlobalVariableContext();
        }
        return variableContext;
    }

    /**
     * 获取全局变量上下文
     *
     * @return 全局变量上下文，如果没有则返回null
     */
    private VariableContext getGlobalVariableContext() {
        // 通过SmartActionExecutor获取当前线程的变量上下文
        return SmartActionExecutor.getCurrentVariableContext();
    }

    /**
     * 设置变量上下文
     *
     * @param variableContext 变量上下文
     */
    public void setVariableContext(VariableContext variableContext) {
        this.variableContext = variableContext;
    }

    /**
     * 检查是否有变量上下文
     *
     * @return true表示有变量上下文
     */
    public boolean hasVariableContext() {
        return variableContext != null;
    }

    /**
     * 获取原始参数队列的副本（用于调试）
     *
     * @return 参数队列副本
     */
    public Queue<String> getParamQueueCopy() {
        return new LinkedList<>(paramQueue);
    }
}
