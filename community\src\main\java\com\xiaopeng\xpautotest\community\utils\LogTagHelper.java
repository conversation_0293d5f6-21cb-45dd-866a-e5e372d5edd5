package com.xiaopeng.xpautotest.community.utils;

import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.TestScript;
import com.xiaopeng.xpautotest.community.test.TestStep;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 结构化日志标签构建与写入器
 * 负责生成并写入用于日志文件折叠展示的结构化标签
 */
public class LogTagHelper {

    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault());

    public static void logScriptStart(long scriptId,long ts) {
        Date now = new Date(ts);
        String timestamp = TIME_FORMAT.format(now);
        String scriptIdStr = String.valueOf(scriptId);
        String labelJson = String.format("[SCRIPT_START_%s]{\"scriptId\":%s,\"timestamp\":\"%s\",\"ts\":%d}", scriptIdStr, scriptIdStr,timestamp, ts);
        FileLogger.getInstance().logRawText(labelJson);
    }

    public static void logScriptEnd(long scriptId, String resultStatus, long duration) {
        Date now = new Date();
        String timestamp = TIME_FORMAT.format(now);
        long ts = now.getTime();
        String labelJson = String.format("[SCRIPT_END_%d]{\"scriptId\":%d,\"result\":\"%s\",\"durationMs\":%d,\"timestamp\":\"%s\",\"ts\":%d}", scriptId, scriptId, resultStatus, duration, timestamp, ts);
        FileLogger.getInstance().logRawText(labelJson);
    }

    public static void logStepStart(TestStep step, int loop, int sceneIndex, int sceneLoopIndex,long ts) {
        Date now = new Date(ts);
        String timestamp = TIME_FORMAT.format(now);
        int stepId = step != null ? step.getStepId() : -1;
        String action = step != null && step.getAction() != null ? step.getAction() : "";
        StringBuilder paramsJson = new StringBuilder("[");
        Object[] params = step != null ? step.getParamArray() : null;
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                Object p = params[i];
                if (p instanceof String) {
                    String escaped = p.toString().replace("\\", "\\\\").replace("\"", "\\\"");
                    paramsJson.append('"').append(escaped).append('"');
                } else if (p instanceof Number || p instanceof Boolean) {
                    paramsJson.append(p.toString());
                } else if (p == null) {
                    paramsJson.append("null");
                } else {
                    String escaped = p.toString().replace("\\", "\\\\").replace("\"", "\\\"");
                    paramsJson.append('"').append(escaped).append('"');
                }
                if (i < params.length - 1) {
                    paramsJson.append(", ");
                }
            }
        }
        paramsJson.append("]");
        String labelJson = String.format("[STEP_START_%d]{\"stepId\":%d,\"loop\":%d,\"sceneIndex\":%d,\"sceneLoop\":%d,\"action\":\"%s\",\"params\":%s,\"timestamp\":\"%s\",\"ts\":%d}",stepId, stepId, loop, sceneIndex, sceneLoopIndex, action, paramsJson.toString(), timestamp, ts);
        FileLogger.getInstance().logRawText(labelJson);
    }

    public static void logStepEnd(TestStep step, String resultStatus, int loop, int sceneIndex, int sceneLoopIndex, long ts) {
        Date now = new Date(ts);
        String timestamp = TIME_FORMAT.format(now);
        int stepId = step != null ? step.getStepId() : -1;
        String labelJson = String.format("[STEP_END_%d]{\"stepId\":%d,\"loop\":%d,\"sceneIndex\":%d,\"sceneLoop\":%d,\"result\":\"%s\",\"timestamp\":\"%s\",\"ts\":%d}", stepId, stepId, loop, sceneIndex, sceneLoopIndex, resultStatus, timestamp, ts);
        FileLogger.getInstance().logRawText(labelJson);
    }

} 