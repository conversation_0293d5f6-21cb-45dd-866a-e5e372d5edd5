package com.xiaopeng.xpautotest.model;

import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;

import java.util.Objects;

public class TestCaseItem {
    private long taskExecutionId;
    private long caseId;
    private long scriptId;
    private long suiteId;
    private String name;
    private String steps;
    private int status = -1;
    private String result;
    private String lastRunTime;

    public TestCaseItem(long caseId, long scriptId, long suiteId, String name, String result, String lastRunTime) {
        this.caseId = caseId;
        this.scriptId = scriptId;
        this.suiteId = suiteId;
        this.name = name;
        this.result = result;
        this.lastRunTime = lastRunTime;
    }

    // Getters & Setters
//    public long getId() { return caseId; }

    public long getScriptId() { return scriptId; }
    public long getSuiteId() { return suiteId; }
    public String getName() { return name; }
    public String getSteps() { return steps; }
    public void setSteps(String steps) { this.steps = steps; }
    public int getStatus() { return status; }
    public void setStatus(int status) { this.status = status; }
    public boolean isExecuting() { return status == CaseExecuteState.EXECUTING; }
    public boolean isSuccess() { return status == CaseExecuteState.SUCCESS; }
    public boolean isFailure() { return status == CaseExecuteState.FAILURE; }
//    public void setPass(boolean pass) { isPass = pass; }
    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }
    public String getLastRunTime() { return lastRunTime; }
    public void setLastRunTime(String lastRunTime) { this.lastRunTime = lastRunTime; }
    public long getTaskExecutionId() { return taskExecutionId; }
    public void setTaskExecutionId(long executionId) { this.taskExecutionId = executionId; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TestCaseItem that = (TestCaseItem) o;
        return scriptId == that.scriptId &&
                Objects.equals(name, that.name) &&
                Objects.equals(result, that.result) &&
                Objects.equals(lastRunTime, that.lastRunTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(scriptId, name, result, lastRunTime);
    }
}
