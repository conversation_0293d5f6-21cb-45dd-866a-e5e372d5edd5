package com.xiaopeng.xpautotest.utils;

import com.xiaopeng.xpautotest.bean.Source;
import com.xiaopeng.xpautotest.community.utils.Log;

import io.reactivex.BackpressureStrategy;
import io.reactivex.Flowable;
import io.reactivex.Observable;
import io.reactivex.annotations.CheckReturnValue;
import io.reactivex.annotations.NonNull;
import io.reactivex.annotations.SchedulerSupport;
import io.reactivex.exceptions.Exceptions;
import io.reactivex.plugins.RxJavaPlugins;
import io.reactivex.schedulers.Schedulers;

public class Rx2Util {

    public static void init() {
        RxJavaPlugins.setErrorHandler(throwable -> Log.e("Rx2Util", "errorHandler accept", (Exception) throwable));
    }

    @CheckReturnValue
    @NonNull
    public static <T> Flowable<T> getFlowableOnIo(final Source<T> source) {
        return getFlowable(source)
                .subscribeOn(Schedulers.io());
    }

    @CheckReturnValue
    @NonNull
    @SchedulerSupport(SchedulerSupport.NONE)
    public static <T> Flowable<T> getFlowable(final Source<T> source) {
        return getFlowable(source, BackpressureStrategy.BUFFER);
    }

    @CheckReturnValue
    @NonNull
    @SchedulerSupport(SchedulerSupport.NONE)
    public static <T> Flowable<T> getFlowable(final Source<T> source, BackpressureStrategy mode) {
        return Flowable.create(emitter -> {
            try {
                if (!emitter.isCancelled()) {
                    T obj = source.call();
                    if (obj == null) {
                        Log.e("Rx2Util", "getFlowable:" + source);
                        throw new RuntimeException("getFlowable call() return null:" + source);
                    }
                    emitter.onNext(obj);
                }
                if (!emitter.isCancelled()) {
                    emitter.onComplete();
                }
            } catch (Throwable e) {
                emitter.tryOnError(Exceptions.propagate(e));
            }
        }, mode);
    }

    @CheckReturnValue
    @NonNull
    public static <T> Observable<T> getObservableOnIo(final Source<T> source) {
        return getObservable(source)
                .subscribeOn(Schedulers.io());
    }


    @CheckReturnValue
    @NonNull
    @SchedulerSupport(SchedulerSupport.NONE)
    public static <T> Observable<T> getObservable(final Source<T> source) {
        return Observable.create(emitter -> {
            try {
                if (!emitter.isDisposed()) {
                    T obj = source.call();
                    if (obj == null) {
                        Log.e("Rx2Util", "getObservable:" + source);
                        throw new RuntimeException("getObservable call() return null:" + source);
                    }
                    emitter.onNext(obj);
                }
                if (!emitter.isDisposed()) {
                    emitter.onComplete();
                }
            } catch (Exception e) {
                emitter.tryOnError(Exceptions.propagate(e));
            }
        });
    }
}
