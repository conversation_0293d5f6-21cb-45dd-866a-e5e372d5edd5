package com.xiaopeng.xpautotest;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.pm.PackageInfo;
import android.content.Intent;

import com.xiaopeng.datalog.DataLogModuleEntry;
import com.xiaopeng.lib.bughunter.BugHunter;
import com.xiaopeng.lib.framework.module.Module;
import com.xiaopeng.lib.framework.moduleinterface.netchannelmodule.http.IHttp;
import com.xiaopeng.lib.framework.netchannelmodule.NetworkChannelsEntry;
import com.xiaopeng.lib.http.HttpsUtils;
import com.xiaopeng.lib.utils.ThreadUtils;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.constant.Constant;
import com.xiaopeng.xpautotest.manager.ConnectionChangeReceiver;
import com.xiaopeng.xpautotest.utils.AppUtils;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xui.Xui;
import com.xiaopeng.xui.utils.XLogUtils;
import com.xiaopeng.xpautotest.service.OSSUploadService;

import androidx.annotation.CallSuper;

public class App extends Application {
    protected static final String TAG = "xpautotest_App";
    private static Application sAppInstance;
    private static IHttp http;
    private ConnectionChangeReceiver mConnectionChangeReceiver;
    private boolean isSyncInit;

    public static Application getInstance() {
        return sAppInstance;
    }

    public static IHttp getHttp() {
        return http;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        sAppInstance = this;
        isSyncInit = true;
        init();
    }

    @CallSuper
    protected void init() {
        Log.i(TAG, "init");
        syncInit();
        if (isSyncInit) {
            ThreadUtils.execute(this::asyncInit);
        } else {
            asyncInit();
        }
    }

    private void syncInit() {
        Xui.init(this);
        Xui.setFontScaleDynamicChangeEnable(true);//动态字号缩放
        Xui.setDialogFullScreen(true);//全屏遮罩弹窗
        Xui.setSingleToast(true);//单例toast
    }

    @SuppressLint("RestrictedApi")
    protected void asyncInit() {
        Xui.setLogLevel(XLogUtils.LOG_D_LEVEL);//日志级别
        BugHunter.init(this);
        // 埋点组件注册
        Module.register(DataLogModuleEntry.class, new DataLogModuleEntry(getApplicationContext()));

//        CarClientWrapper.getInstance().connectToCar(this);

        if (mConnectionChangeReceiver == null) {
            mConnectionChangeReceiver = ConnectionChangeReceiver.getInstance();
            mConnectionChangeReceiver.registerReceiver();
        }

        // 启动 OSSUploadService ---
        try {
            startService(new Intent(this, OSSUploadService.class));
            Log.i(TAG, "OSSUploadService started.");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start OSSUploadService from App.onCreate()", e);
        }

        // 初始化ViewModel
//        SuiteViewModel viewModel = new ViewModelProvider((ViewModelStoreOwner) this).get(SuiteViewModel.class);
//        viewModel.load();
//        ThreadUtils.postDelayed(ThreadUtils.THREAD_BACKGROUND, FileUtils::deleteVmapOldData, 0);

        printVersionInfo();
    }

    private void initNetWork() {
        HttpsUtils.init(this, true);
        Module.register(NetworkChannelsEntry.class, new NetworkChannelsEntry());
        http = (IHttp) Module.get(NetworkChannelsEntry.class).get(IHttp.class);
        http.config().connectTimeout(Constant.HttpConfig.CONNECT_TIMEOUT)
                .readTimeout(Constant.HttpConfig.READ_TIMEOUT)
                .writeTimeout(Constant.HttpConfig.WRITE_TIMEOUT)
                .dnsTimeout(Constant.HttpConfig.DNS_TIMEOUT)
                .retryCount(1)
                .applicationContext(this)
                .enableTrafficStats()
                .apply();
    }

    private void printVersionInfo() {
        try {
            final String lineEnd = "\n";
            StringBuilder sb = new StringBuilder();
            sb.append("====print xpAutoTest information====").append(lineEnd);
            sb.append("buildVersion : ").append(AppUtils.getAppVersion(getApplicationContext())).append(lineEnd);
            try {
                PackageInfo pkgInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
                sb.append("app version : ").append(pkgInfo.versionName).append(lineEnd);
            } catch (Exception e) {
                Log.i("error:" + e.getMessage());
            }
            Log.i(sb.toString());
        } catch (Exception e) {
            Log.i("error:" + e.getMessage());
        }
    }

    @Override
    public void onTerminate() {
        //停止 OSSUploadService
        try {
            Log.i(TAG, "Stopping OSSUploadService from App.onTerminate()...");
            stopService(new Intent(this, OSSUploadService.class));
            Log.i(TAG, "OSSUploadService stop requested.");
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop OSSUploadService from App.onTerminate()", e);
        }
        ConnectionChangeReceiver.getInstance().unregisterReceiver();
        super.onTerminate();
    }
}
