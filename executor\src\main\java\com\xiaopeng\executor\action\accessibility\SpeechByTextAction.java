package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SpeechByTextAction extends BaseAction {
    private static final String TAG = "SpeechByTextAction";
    private static final String BROADCAST_ACTION = "carspeechservice.ACTION_SEND_TEXT";
    private static final String EXTRA_KEY_TEXT = "text";


    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String commandText = context.getStringParam();
        if (commandText == null || commandText.isEmpty()) {
            String errorMsg = "Voice command text parameter is missing.";
            throw new ActionException(errorMsg, FailureCode.SI001);
        }

        FileLogger.i(TAG, "Executing VoiceCommandAction with command: " + commandText);
        // 每次都唤醒小P，声控成功率高一点
        String fullCommandText = "你好小P," + commandText;

        String command = String.format("am broadcast -a %s --es %s \"%s\"",
                BROADCAST_ACTION,
                EXTRA_KEY_TEXT,
                fullCommandText
        );
        try {
            CMDUtils.CMD_Result cmdResult = CMDUtils.runCMD(command, false, true);

            if (cmdResult.resultCode == 0) {
                String successOutput = cmdResult.success != null ? cmdResult.success : "";
                FileLogger.i(TAG, "Command executed. Result code: 0, Output: " + successOutput + (cmdResult.error != null && !cmdResult.error.isEmpty() ? ", Error stream: " + cmdResult.error : ""));
                boolean success = successOutput.toLowerCase().contains("result=0") || (cmdResult.error == null || cmdResult.error.trim().isEmpty());

                if (success) {
                    return TestResult.success("Voice command executed successfully. Output: " + successOutput);
                } else {
                     FileLogger.w(TAG, "Command had resultCode 0, but output was not clearly indicative of success or an error was reported: " + cmdResult.error);
                     return TestResult.failure("Voice command executed, but output was not clearly indicative of success. Output: " + successOutput);
                }
            } else {
                String errorMsg = String.format("Failed to execute voice command. Result code: %d, Error: %s, Success output: %s",
                        cmdResult.resultCode, cmdResult.error, cmdResult.success);
                FileLogger.e(TAG, errorMsg);
                return TestResult.failure(errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = "Exception while sending voice command: " + e.getMessage();
            FileLogger.e(TAG, errorMsg);
            throw new ActionException(errorMsg, FailureCode.AB001, e);
        }
    }
} 