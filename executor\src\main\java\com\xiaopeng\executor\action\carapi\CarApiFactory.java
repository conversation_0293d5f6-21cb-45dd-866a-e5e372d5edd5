package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.executor.bean.IActionFactory;
import com.xiaopeng.executor.bean.TechType;

import java.util.HashMap;
import java.util.Map;

import androidx.core.util.Supplier;

public class CarApiFactory implements IActionFactory {
    private final CarApiConfig config;
    public CarApiFactory(CarApiConfig config) {
        this.config = config;
        registerActions();
    }

    private final Map<String, Supplier<IAction<CarApiConfig>>> actionMap = new HashMap<>();
    private final Map<String, IAction<?>> actionInstanceMap = new HashMap<>();

    private void registerActions() {
        register("CheckBCMProp", CheckBcmPropAction::new);
        register("CheckHVACProp", CheckHvacPropAction::new);
        register("CheckMSMProp", CheckMsmPropAction::new);
        register("CheckATLProp", CheckAtlPropAction::new);
        register("CheckMCUProp", CheckMcuPropAction::new);
        register("CheckVCUProp", CheckVcuPropAction::new);
        register("CheckAMPProp", CheckAmpPropAction::new);
        register("CheckESPProp", CheckEspPropAction::new);
        register("CheckEPSProp", CheckEpsPropAction::new);
        register("CheckSCUProp", CheckScuPropAction::new);
        register("CheckTBOXProp", CheckTBoxPropAction::new);
        register("CheckTPMSProp", CheckTpmsPropAction::new);
        register("CheckXPUProp", CheckXpuPropAction::new);
        register("CheckAvasProp", CheckAvasPropAction::new);
        register("CheckAvmProp", CheckAvmPropAction::new);
        register("CheckBmsProp", CheckBmsPropAction::new);
        register("CheckHudProp", CheckHudPropAction::new);
        register("CheckLluProp", CheckLluPropAction::new);
        register("SetBCMProp", SetBcmPropAction::new);
        register("SetHVACProp", SetHvacPropAction::new);
        register("SetMSMProp", SetMsmPropAction::new);
        register("SetATLProp", SetAtlPropAction::new);
        register("SetMCUProp", SetMcuPropAction::new);
        register("SetVCUProp", SetVcuPropAction::new);
        register("SetAMPProp", SetAmpPropAction::new);
        register("SetESPProp", SetEspPropAction::new);
        register("SetEPSProp", SetEpsPropAction::new);
        register("SetSCUProp", SetScuPropAction::new);
        register("SetTBOXProp", SetTBoxPropAction::new);
        register("SetXPUProp", SetXpuPropAction::new);
        register("SetAvasProp", SetAvasPropAction::new);
        register("SetAvmProp", SetAvmPropAction::new);
        register("SetHudProp", SetHudPropAction::new);
        register("SetLluProp", SetLluPropAction::new);
    }

    private void register(String actionName, Supplier<IAction<CarApiConfig>> supplier) {
        actionMap.put(actionName.toLowerCase(), supplier);
    }

    @Override
    public IAction<?> createAction(String actionName) {
        Supplier<IAction<CarApiConfig>> supplier = actionMap.get(actionName.toLowerCase());
        if (supplier == null) throw new IllegalArgumentException("Unknown action. actionType: " + actionName);
        IAction<CarApiConfig> action = supplier.get();
        action.init(config); // 注入配置
        return action;
    }

    public IAction<?> getAction(String actionName) {
        if (actionInstanceMap.containsKey(actionName)) {
            return actionInstanceMap.get(actionName);
        } else {
            IAction<?> action = createAction(actionName);
            actionInstanceMap.put(actionName, action);

            return action;
        }
    }

    public boolean containsAction(String actionName) {
        return actionMap.containsKey(actionName);
    }

    @Override
    public boolean supports(TechType techType) {
        return techType == TechType.CAR_API;
    }
}
