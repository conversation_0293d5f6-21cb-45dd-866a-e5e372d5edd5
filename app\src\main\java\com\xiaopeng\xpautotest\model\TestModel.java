package com.xiaopeng.xpautotest.model;

import com.xiaopeng.xpautotest.manager.TestManager;
import com.xiaopeng.xpautotest.viewmodel.ISuiteViewModel;

import java.util.List;

public class TestModel  implements TestManager.ISuiteListCallBack {
    private static final String TAG = "TestModel";
    private ISuiteViewModel mISuiteViewModel;

    public static TestModel getInstance() {
        return SingleHolder.sInstance;
    }

    private static class SingleHolder {
        private static final TestModel sInstance = new TestModel();
    }

    private TestModel() {
        TestManager.getInstance().setISuiteListCallBack(this);
    }

    public void setISuiteViewModel(ISuiteViewModel iSuiteViewModel) {
        mISuiteViewModel = iSuiteViewModel;
    }

    @Override
    public void taskInfoSuccess() {
        mISuiteViewModel.onTaskInfoUpdate(TestManager.getInstance().getTaskInfo());
    }

    @Override
    public void suiteListSuccess() {
        mISuiteViewModel.onSuiteListUpdate(TestManager.getInstance().getSuiteList());
    }

    @Override
    public void loadCaseFailed() {
        mISuiteViewModel.onLoadCaseFailed(TestManager.getInstance().getErrorCode());
    }

    @Override
    public void triggerAutoStart() {
        mISuiteViewModel.onTriggerAutoStart(TestManager.getInstance().getTaskInfo());
    }

    public List<String> getTitleList() {
        List<String> titleList = TestManager.getInstance().getTopicTitleList();
        TestManager.getInstance().update();
        return titleList;
    }

    public List<ITestSuiteItem> getSuiteList() {
        List<ITestSuiteItem> suiteList = TestManager.getInstance().getSuiteList();
        TestManager.getInstance().update();
        return suiteList;
    }

    public void load(String tag) {
        TestManager.getInstance().load(tag);
    }

    public void update() {
        TestManager.getInstance().update();
    }

    public long startTestSync() {
        return TestManager.getInstance().startTestSync();
    }

    public void startTest(TestManager.ITestDataCallBack<ITestDataState<Long>> callback) {
        TestManager.getInstance().startTest(callback);
    }

    public void clear() {
        TestManager.getInstance().clear();
    }

    public void heartbeat() {
        TestManager.getInstance().heartbeat();}


    public boolean isAutoStartAllowed(Long manuallyStoppedTaskId) {
        return TestManager.getInstance().isAutoStartAllowed(manuallyStoppedTaskId);
    }
    }
