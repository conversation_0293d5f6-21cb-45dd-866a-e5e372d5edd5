package com.xiaopeng.xpautotest.helper.log.processor;

import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.event.FileUploadEvent;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessContext;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessorResult;
import com.xiaopeng.xpautotest.utils.OSSPathUtils;
import org.greenrobot.eventbus.EventBus;
import java.io.File;
import java.io.FileOutputStream;
import java.util.zip.ZipOutputStream;

/**
 * 大屏日志处理器
 * 负责提取、压缩和通知上传大屏日志文件
 */
public class CduLogProcessor implements ILogProcessor {
    private static final String TAG = "CduLogProcessor";

    private static final int MAX_LOG_DIRECTORIES = 1;  // 最大检查的日志目录数量
    private static final int MAX_SPLIT_FILES = 100;     // 最大检查的分割文件数量
    private static final String BASE_LOG_PATH = "/data/Log";  // 基础日志路径

    @Override
    public LogProcessorResult process(LogProcessContext context) {
        String executionId = null;
        try {
            executionId = context.getExecutionId();
            if (executionId == null || executionId.trim().isEmpty()) {
                return LogProcessorResult.failure(getSupportedLogType(),
                    "ExecutionId is null or empty").build();
            }

            long startTimestamp = context.getStartTimestamp();
            long endTimestamp = context.getEndTimestamp();

            if (startTimestamp <= 0 || endTimestamp <= 0 || endTimestamp <= startTimestamp) {
                return LogProcessorResult.failure(getSupportedLogType(),
                    "Invalid timestamp range: " + startTimestamp + " to " + endTimestamp).build();
            }

            String ossPath = processCduLogs(executionId, startTimestamp, endTimestamp);

            if (ossPath != null && !ossPath.trim().isEmpty()) {

                return LogProcessorResult.success(getSupportedLogType())
                        .ossPath(ossPath)
                        .build();
            } else {
                FileLogger.w(TAG, "CDU log processing failed or no logs found for executionId: " + executionId);

                return LogProcessorResult.failure(getSupportedLogType(),
                    "CDU log processing failed or no logs found").build();
            }
        } catch (Exception e) {
            String errorMsg = "Failed to process CDU logs for ID: " + executionId + ", error: " + e.getMessage();
            FileLogger.e(TAG, errorMsg, e);
            return LogProcessorResult.failure(getSupportedLogType(), errorMsg).build();
        }
    }

    @Override
    public LogProcessorResult.LogType getSupportedLogType() {
        return LogProcessorResult.LogType.CDU_LOG;
    }

    @Override
    public boolean shouldProcess(LogProcessContext context) {
        // 仅在测试失败时处理CDU日志
        return context.getTaskExecutionInfo() != null &&
               context.getTaskExecutionInfo().getNgCount() > 0;
    }

    /**
     * 处理大屏日志的核心方法
     * @param executionId 执行ID
     * @param startTimestamp 测试开始时间戳
     * @param endTimestamp 测试结束时间戳
     * @return 上传到OSS的路径，失败返回null
     */
    private String processCduLogs(String executionId, long startTimestamp, long endTimestamp) {
        long totalStartTime = System.currentTimeMillis();
        // 1. 创建CDU日志目录
        File cduLogDir = new File(Constant.CDU_LOG_PATH);
        if (!cduLogDir.exists() && !cduLogDir.mkdirs()) {
            FileLogger.e(TAG, "Failed to create CDU log directory: " + cduLogDir.getAbsolutePath());
            return null;
        }

        // 2. 直接使用传入的统一开始时间戳生成文件名
        String timestamp = String.valueOf(startTimestamp);
        
        String zipFileName = executionId + "_" + timestamp + ".zip";
        File zipFile = new File(cduLogDir, zipFileName);
        
        try {
            // 3. 流式处理：边扫描边压缩，直接生成最终ZIP文件
            long streamingStartTime = System.currentTimeMillis();
            ProcessingResult result = processLogFilesStreaming(startTimestamp, endTimestamp, zipFile);
            long streamingTime = System.currentTimeMillis() - streamingStartTime;

            if (!result.hasFiles) {
                FileLogger.w(TAG, "No CDU log files found in test period");
                // 清理可能创建的空zip文件
                if (zipFile.exists()) {
                    zipFile.delete();
                }
                return null;
            }

            FileLogger.i(TAG, "CDU logs compressed: " + getReadableFileSize(result.totalBytesProcessed) +
                " → " + getReadableFileSize(zipFile.length()) + " in " + streamingTime + "ms");

            // 4. 触发文件上传
            EventBus.getDefault().post(new FileUploadEvent(zipFile.getAbsolutePath()));

            // 5. 构造OSS路径
            String ossPath = OSSPathUtils.buildObjectKey(UploadFileTask.FileType.FILE_CDU_LOG, zipFileName, zipFile.getAbsolutePath());

            return ossPath;
            
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - totalStartTime;
            FileLogger.e(TAG, "Error in streaming processing after " + totalTime + "ms: " + e.getMessage(), e);
            // 清理可能创建的不完整zip文件
            if (zipFile.exists()) {
                zipFile.delete();
            }
            return null;
        }
    }

    /**
     * 流式处理日志文件：边扫描边压缩，直接生成最终ZIP文件
     * @param startTimestamp 开始时间戳
     * @param endTimestamp 结束时间戳
     * @param zipFile 目标ZIP文件
     * @return 处理结果（是否有文件 + 总字节数）
     */
    private ProcessingResult processLogFilesStreaming(long startTimestamp, long endTimestamp, File zipFile) {
        File baseLogDir = new File(BASE_LOG_PATH);
        if (!baseLogDir.exists()) {
            FileLogger.w(TAG, "Base log directory not found: " + baseLogDir.getAbsolutePath());
            return new ProcessingResult(false, 0);
        }

        boolean hasFiles = false;
        boolean shouldStopScanningForMain = false;
        boolean shouldStopScanningForSystem = false;
        int totalFilesProcessed = 0;
        long totalBytesProcessed = 0;

        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            // 使用快速压缩级别，平衡速度和压缩率
            zos.setLevel(1);

            // 从log0开始检查，按序号递增
            for (int dirIndex = 0; dirIndex < MAX_LOG_DIRECTORIES; dirIndex++) {
                File logDir = new File(baseLogDir, "log" + dirIndex);

                if (!logDir.exists()) {
                    // 目录不存在，由于轮转的连续性，后续目录也不会存在
                    if (dirIndex == 0) {
                        // log0不存在是特殊情况，需要警告
                        FileLogger.w(TAG, "No CDU logs available");
                    }
                    // 无论是log0还是其他目录不存在，都直接返回
                    return new ProcessingResult(hasFiles, totalBytesProcessed);
                }
                // 处理两种文件类型：main.txt和system.txt
                String[] fileTypes = {"main.txt", "system.txt"};
                boolean[] shouldStopScanning = {shouldStopScanningForMain, shouldStopScanningForSystem};

                for (int i = 0; i < fileTypes.length; i++) {
                    String fileType = fileTypes[i];

                    // 如果该文件类型还没有触发停止扫描
                    if (!shouldStopScanning[i]) {
                        StreamingResult result = processFileSeriesStreaming(
                            logDir, fileType, startTimestamp, endTimestamp, zos);

                        // 更新统计信息
                        if (result.hasFiles) {
                            hasFiles = true;
                            totalFilesProcessed += result.fileCount;
                            totalBytesProcessed += result.bytesProcessed;
                        }

                        // 检查是否需要停止后续目录扫描
                        if (result.shouldStopScanning) {
                            shouldStopScanning[i] = true;

                        }
                    }
                }

                // 更新停止扫描标志
                shouldStopScanningForMain = shouldStopScanning[0];
                shouldStopScanningForSystem = shouldStopScanning[1];

                // 如果两种文件类型都已经触发停止扫描，则完全退出
                if (shouldStopScanningForMain && shouldStopScanningForSystem) {
                    break;
                }

                // 常规优化：如果当前目录中最新的文件都早于测试开始时间，则退出
                if (shouldSkipOlderDirectories(logDir, startTimestamp)) {
                    break;
                }
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "Error in streaming processing", e);
            return new ProcessingResult(false, 0);
        }
        return new ProcessingResult(hasFiles, totalBytesProcessed);
    }

    /**
     * 处理结果类
     */
    private static class StreamingResult {
        boolean hasFiles = false;
        boolean shouldStopScanning = false;
        int fileCount = 0;
        long bytesProcessed = 0;
    }

    /**
     * 整体处理结果类
     */
    private static class ProcessingResult {
        boolean hasFiles = false;
        long totalBytesProcessed = 0;

        ProcessingResult(boolean hasFiles, long totalBytesProcessed) {
            this.hasFiles = hasFiles;
            this.totalBytesProcessed = totalBytesProcessed;
        }
    }

    /**
     * 处理单个文件系列（main.txt或system.txt及其分割文件）
     * @param logDir 日志目录
     * @param baseFileName 基础文件名
     * @param startTimestamp 开始时间戳
     * @param endTimestamp 结束时间戳
     * @param zos ZIP输出流
     * @return 处理结果
     */
    private StreamingResult processFileSeriesStreaming(File logDir, String baseFileName,
                                                     long startTimestamp, long endTimestamp,
                                                     java.util.zip.ZipOutputStream zos) {
        StreamingResult result = new StreamingResult();

        try {
            // 检查基础文件（main.txt, system.txt）
            File baseFile = new File(logDir, baseFileName);

            if (baseFile.exists()) {
                long baseFileTime = baseFile.lastModified();

                if (baseFileTime >= startTimestamp) {
                    // 基础文件在时间范围内，处理它
                    if (addFileToZipStreaming(baseFile, logDir.getName() + "/" + baseFileName, zos)) {
                        result.hasFiles = true;
                        result.fileCount++;
                        result.bytesProcessed += baseFile.length();
                    }
                } else {
                    // 基础文件过旧，分割文件肯定也过旧，直接设置早期退出标志
                    result.shouldStopScanning = true;
                    return result; // 直接返回，不检查分割文件
                }
            } else {
                // 基础文件不存在，分割文件可能也不存在或很旧，设置早期退出标志
                result.shouldStopScanning = true;
                return result; // 直接返回，不检查分割文件
            }

            // 检查分割文件（main.txt.01, main.txt.02, system.txt.1, system.txt.2等）
            for (int i = 1; i <= MAX_SPLIT_FILES; i++) {
                File splitFile = null;

                // 尝试两种命名格式：.01, .02 和 .1, .2
                if (baseFileName.equals("main.txt")) {
                    splitFile = new File(logDir, baseFileName + "." + String.format("%02d", i));
                } else if (baseFileName.equals("system.txt")) {
                    splitFile = new File(logDir, baseFileName + "." + i);
                }

                if (splitFile == null || !splitFile.exists()) {
                    break; // 文件不存在，说明已经检查完所有分割文件
                }

                long fileModifyTime = splitFile.lastModified();

                // 关键优化：如果第一个分割文件就早于测试开始时间，停止后续目录扫描
                if (fileModifyTime < startTimestamp) {
                    if (i == 1) {
                        result.shouldStopScanning = true;
                    }
                    break; // 停止检查当前文件系列的后续文件
                }

                // 检查文件是否在测试开始时间之后修改
                if (fileModifyTime >= startTimestamp) {
                    if (addFileToZipStreaming(splitFile, logDir.getName() + "/" + splitFile.getName(), zos)) {
                        result.hasFiles = true;
                        result.fileCount++;
                        result.bytesProcessed += splitFile.length();
                    }
                }
            }

        } catch (Exception e) {
            FileLogger.e(TAG, "Error processing file series " + baseFileName + " in " + logDir.getName(), e);
        }

        return result;
    }

    /**
     * 将文件添加到ZIP流中
     * @param file 要添加的文件
     * @param entryPath ZIP中的条目路径
     * @param zos ZIP输出流
     * @return 是否成功
     */
    private boolean addFileToZipStreaming(File file, String entryPath, java.util.zip.ZipOutputStream zos) {
        try (java.io.FileInputStream fis = new java.io.FileInputStream(file);
             java.io.BufferedInputStream bis = new java.io.BufferedInputStream(fis, 256 * 1024)) {

            java.util.zip.ZipEntry entry = new java.util.zip.ZipEntry(entryPath);
            entry.setTime(file.lastModified()); // 保持文件时间
            zos.putNextEntry(entry);

            // 针对20MB大文件使用256KB缓冲区，减少系统调用次数
            byte[] buffer = new byte[256 * 1024];
            int length;
            while ((length = bis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }

            zos.closeEntry();
            return true;

        } catch (Exception e) {
            FileLogger.e(TAG, "Error adding file to zip: " + file.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 检查目录中的文件是否都早于测试开始时间
     * 用于优化目录遍历，如果当前目录的文件都过旧，则跳过后续更旧的目录
     * @param logDir 要检查的日志目录
     * @param startTimestamp 测试开始时间戳
     * @return true表示应该跳过后续更旧的目录
     */
    private boolean shouldSkipOlderDirectories(File logDir, long startTimestamp) {
        // 检查目录中最新的几个关键文件
        File mainTxt = new File(logDir, "main.txt");
        File systemTxt = new File(logDir, "system.txt");

        long latestTime = 0;

        // 找到目录中最新的文件时间
        if (mainTxt.exists()) {
            latestTime = Math.max(latestTime, mainTxt.lastModified());
        }
        if (systemTxt.exists()) {
            latestTime = Math.max(latestTime, systemTxt.lastModified());
        }

        // 如果连最新的文件都早于测试开始时间，则可以跳过后续目录
        return latestTime > 0 && latestTime < startTimestamp;
    }

    /**
     * 获取文件大小的可读字符串表示
     * @param size 文件大小（字节）
     * @return 文件大小字符串，如 "1.5 MB"
     */
    public static String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";

        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));

        return String.format("%.1f %s",
                size / Math.pow(1024, digitGroups),
                units[digitGroups]);
    }
}