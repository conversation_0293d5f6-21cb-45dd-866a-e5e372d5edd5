package com.xiaopeng.xpautotest.community;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {
    @Test
    public void addition_isCorrect() {
        assertEquals(4, 2 + 2);
    }

    @Test
    public void test() {
        String line = "            WaitStr \"新版本升级\" 10\n";
        line = line.trim();
//        String[] arr = line.split("\\{|,|\\}");
//        for (int i=0;i < arr.length; i++) {
//            System.out.println(arr[i]);
//        }
        String result = line.replaceFirst(".*\"(.+)\".*?(\\d+)", "$2");
        System.out.println(result);
    }
}