package com.xiaopeng.xpautotest.model;

// 测试数据状态基类
public abstract class ITestDataState<T> {
    private ITestDataState() {}

    // Loading状态
    public static class Loading<T> extends ITestDataState<T> {}

    // Success状态
    public static class Success<T> extends ITestDataState<T> {
        private final T data;

        public Success(T data) {
            this.data = data;
        }

        public T getData() {
            return data;
        }
    }

    // Error状态
    public static class Error<T> extends ITestDataState<T> {
        private final Throwable error;

        public Error(Throwable error) {
            this.error = error;
        }

        public Throwable getError() {
            return error;
        }
    }
}
