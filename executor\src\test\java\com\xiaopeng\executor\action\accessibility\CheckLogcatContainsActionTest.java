package com.xiaopeng.executor.action.accessibility;

import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CheckLogcatContainsActionTest {
    @Test
    public void testregex() {
        // 测试用例
        String[] testPatternParts = {
                "\"ABC\"",                                  // 单个简单模式
                "\"A BC\" \"DEF\"",                         // 多个带空格的模式
                "\"Abc\" \"error\\\\s*occurred\"",          // 包含转义和正则特殊字符
                "\"Pattern with \\\"escaped quotes\\\"\"", // 包含转义引号
                "\"SinglePattern\"",                        // 单个模式，无空格
                "",                                         // 空字符串
                "  \"Leading Space\" \"Trailing Space\"  ", // 前后有空格
                "NoQuotesHere",                             // 没有引号
                "\"Mismatched quote"                       // 引号不匹配 (预期不解析)
        };

        for (String patternPart : testPatternParts) {
            System.out.println("\nInput patternPart: [" + patternPart + "]");
            List<String> patterns = new ArrayList<>();

            if (patternPart != null && !patternPart.trim().isEmpty()) {
                Pattern p = Pattern.compile("\"((?:\\\\\"|[^\"])*)\"");
                Matcher m = p.matcher(patternPart.trim()); // 使用 trim() 移除首尾空格
                while (m.find()) {
                    String extractedPattern = m.group(1);
                    patterns.add(extractedPattern);
                }
            }

            System.out.println("Output patterns list: " + patterns);
        }
    }

}
