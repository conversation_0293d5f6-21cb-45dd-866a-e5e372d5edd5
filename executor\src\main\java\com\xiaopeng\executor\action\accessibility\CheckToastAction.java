package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class CheckToastAction extends BaseAction {
    private static final String TAG = "CheckToastAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String expectText = (String) context.getStringParam();
        if (expectText == null) {
            throw new ActionException("expectText is null!", FailureCode.SI001);
        }
        String currentToast = this.service.getCurrentToast();
        FileLogger.i(TAG, "currentToast: " + currentToast + ", expectValue: " + expectText);
        if (!expectText.equals(currentToast)) {
            return TestResult.failure("Toast text does not match expect value");
        }
        return TestResult.success("Toast text matches expect value");
    }
}
