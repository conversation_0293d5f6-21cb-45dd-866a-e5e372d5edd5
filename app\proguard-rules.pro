# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# 保留行号
-keepattributes SourceFile,LineNumberTable

# 保留继承的
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.support.annotation.**
-keep public class * extends android.app.Activity
-keepnames class * extends androidx.fragment.app.Fragment
# -keep class android.os.**

# lib_xpui
-keep class com.xiaopeng.xui.drawable.** { *; }
-keep class com.xiaopeng.libtheme.** { *; }
-dontwarn com.xiaopeng.libtheme.**
-keep class com.xiaopeng.xui.** { *; }
-keep class xiaopeng.widget.ClipDrawable { *; }

# lib_feature
-keep class com.xiaopeng.lib.**{*;}

# CarService
-keep class android.car.** { *; }

# ApiRouter
-keep class com.xiaopeng.lib.apirouter.** { *; }
-dontwarn com.xiaopeng.lib.apirouter.**

# Vui
-keep class com.xiaopeng.speech.vui.** { *; }
-keep class com.xiaopeng.vui.** {*; }

#打乱文件目录结构
-repackageclasses

-keep class com.xiaopeng.lib.utils.** { *; }

# 保留 Retrofit 的 CallAdapter 和 Converter 相关类
-keep class retrofit2.** { *; }
-keep class com.google.gson.** { *; }  # 如果使用 Gson
-keep class okhttp3.** { *; }
-keep class com.fasterxml.jackson.databind.** { *; }  # 如果使用 Jackson


# 保留所有模型类及其成员（替换实际包名）
-keep class com.xiaopeng.xpautotest.bean.** { *; }
-keep class com.xiaopeng.xpautotest.client.api.ApiResponse { *; }
-keep class com.xiaopeng.xpautotest.client.api.ApiException { *; }
-keep class com.xiaopeng.xpautotest.community.test.FailureReasonDetail { *; }
-keep class com.xiaopeng.xpautotest.community.test.FailureCode { *; }

# 保留无参构造函数（关键！）
-keepclassmembers class com.xiaopeng.xpautotest.bean.** {
    public <init>();
}

# 保留 Gson 注解（如 @SerializedName）
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# 保留泛型信息（关键！）
-keepattributes Signature

# 保留 TypeToken 相关逻辑（避免匿名内部类被混淆）
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken

# opencv
# -keep class org.opencv.** { *; }

#打乱文件目录结构
-repackageclasses

# 去掉告警
-dontwarn android.app.ActivityThread
-dontwarn android.app.CompatAppRecord
-dontwarn android.app.INotificationManager
-dontwarn android.app.IUiModeManager
-dontwarn android.app.PropertyInvalidatedCache
-dontwarn android.app.RemoteInputHistoryItem
-dontwarn android.content.HookIntent
-dontwarn android.content.pm.IPackageMoveObserver
-dontwarn android.content.pm.UserInfo
-dontwarn android.content.res.CompatibilityInfo
-dontwarn android.hardware.ICameraService
-dontwarn android.hardware.camera2.CameraInjectionSession$InjectionStatusCallback
-dontwarn android.hardware.camera2.impl.CameraMetadataNative
-dontwarn android.hardware.display.BrightnessConfiguration
-dontwarn android.hardware.display.DisplayManagerGlobal
-dontwarn android.hardware.display.WifiDisplayStatus
-dontwarn android.hardware.input.IInputSensorEventListener
-dontwarn android.hardware.input.InputDeviceBatteryState
-dontwarn android.hardware.input.InputDeviceIdentifier
-dontwarn android.hardware.input.InputDeviceSensorManager
-dontwarn android.hardware.input.InputSensorInfo
-dontwarn android.hardware.input.KeyboardLayout
-dontwarn android.hardware.input.TouchCalibration
-dontwarn android.media.AudioDeviceAttributes
-dontwarn android.media.AudioDevicePort
-dontwarn android.media.AudioFocusInfo
-dontwarn android.media.AudioGainConfig
-dontwarn android.media.AudioPatch
-dontwarn android.media.AudioPort
-dontwarn android.media.AudioPortConfig
-dontwarn android.media.AudioPortEventHandler
-dontwarn android.media.IAudioFocusDispatcher
-dontwarn android.media.IAudioServerStateDispatcher
-dontwarn android.media.IAudioService
-dontwarn android.media.IPlaybackConfigDispatcher
-dontwarn android.media.IRecordingConfigDispatcher
-dontwarn android.media.IRingtonePlayer
-dontwarn android.media.IVolumeController
-dontwarn android.media.SoundEffectParms
-dontwarn android.media.SoundField
-dontwarn android.media.VolumePolicy
-dontwarn android.media.audiopolicy.AudioPolicy
-dontwarn android.media.audiopolicy.AudioProductStrategy
-dontwarn android.media.audiopolicy.AudioVolumeGroupChangeHandler
-dontwarn android.os.BatterySaverPolicyConfig
-dontwarn android.os.IPowerManager
-dontwarn android.os.IServiceManager
-dontwarn android.os.IThermalService
-dontwarn android.os.IUserManager
-dontwarn android.os.IUserRestrictionsListener
-dontwarn android.os.IVibratorStateListener
-dontwarn android.os.IVoldTaskListener
-dontwarn android.os.PowerSaveState
-dontwarn android.os.PowerWhitelistManager
-dontwarn android.os.ServiceDebugInfo
-dontwarn android.os.ServiceSpecificException
-dontwarn android.os.ShellCommand
-dontwarn android.os.storage.IStorageManager
-dontwarn android.service.notification.ZenModeConfig
-dontwarn android.text.method.AllCapsTransformationMethod
-dontwarn android.text.method.TransformationMethod2
-dontwarn android.util.LongArray
-dontwarn android.util.LongSparseLongArray
-dontwarn android.util.MathUtils
-dontwarn android.util.Pools$SynchronizedPool
-dontwarn android.util.Singleton
-dontwarn android.util.Slog
-dontwarn android.view.AccessibilityIterators$TextSegmentIterator
-dontwarn android.view.FrameMetricsObserver
-dontwarn android.view.GhostView
-dontwarn android.view.HandlerActionQueue
-dontwarn android.view.IWindow
-dontwarn android.view.IWindowSession
-dontwarn android.view.InputEventConsistencyVerifier
-dontwarn android.view.InputMonitor
-dontwarn android.view.InsetsFlags
-dontwarn android.view.RoundScrollbarRenderer
-dontwarn android.view.ThreadedRenderer
-dontwarn android.view.ViewDebug$CanvasProvider
-dontwarn android.view.ViewHierarchyEncoder
-dontwarn android.view.ViewRootImpl
-dontwarn com.android.internal.R$styleable
-dontwarn com.android.internal.os.FuseAppLoop
-dontwarn com.android.internal.util.ContrastColorUtil
-dontwarn com.android.internal.util.StatLogger
-dontwarn com.android.internal.widget.ILockSettings
-dontwarn com.xiaopeng.IXPMotionListener
-dontwarn com.xiaopeng.input.IInterceptEventListener
-dontwarn com.xiaopeng.input.ILimitMotionEventListener
-dontwarn com.xiaopeng.input.InputDevicePolicyList
-dontwarn org.w3c.dom.bootstrap.DOMImplementationRegistry
-dontwarn io.netty.internal.tcnative.Buffer
-dontwarn io.netty.internal.tcnative.CertificateCallback
-dontwarn io.netty.internal.tcnative.Library
-dontwarn io.netty.internal.tcnative.SSL
-dontwarn io.netty.internal.tcnative.SSLContext
-dontwarn javax.naming.InvalidNameException
-dontwarn javax.naming.NamingException
-dontwarn javax.naming.directory.Attribute
-dontwarn javax.naming.directory.Attributes
-dontwarn javax.naming.ldap.LdapName
-dontwarn javax.naming.ldap.Rdn
-dontwarn javax.xml.stream.XMLEventReader
-dontwarn javax.xml.stream.XMLInputFactory
-dontwarn javax.xml.stream.XMLStreamException
-dontwarn javax.xml.stream.events.Attribute
-dontwarn javax.xml.stream.events.Characters
-dontwarn javax.xml.stream.events.StartElement
-dontwarn javax.xml.stream.events.XMLEvent
-dontwarn org.apache.log4j.Level
-dontwarn org.apache.log4j.Logger
-dontwarn org.apache.log4j.Priority
-dontwarn org.apache.logging.log4j.Level
-dontwarn org.apache.logging.log4j.LogManager
-dontwarn org.apache.logging.log4j.Logger
-dontwarn org.apache.logging.log4j.message.MessageFactory
-dontwarn org.apache.logging.log4j.spi.ExtendedLogger
-dontwarn org.apache.logging.log4j.spi.ExtendedLoggerWrapper
-dontwarn org.ietf.jgss.GSSContext
-dontwarn org.ietf.jgss.GSSCredential
-dontwarn org.ietf.jgss.GSSException
-dontwarn org.ietf.jgss.GSSManager
-dontwarn org.ietf.jgss.GSSName
-dontwarn org.ietf.jgss.Oid
-dontwarn org.jetbrains.annotations.Async$Execute
-dontwarn org.jetbrains.annotations.Async$Schedule
-dontwarn org.slf4j.impl.StaticLoggerBinder
-dontwarn reactor.blockhound.integration.BlockHoundIntegration
-dontwarn software.amazon.awssdk.crt.auth.credentials.Credentials
-dontwarn software.amazon.awssdk.crt.auth.signing.AwsSigner
-dontwarn software.amazon.awssdk.crt.auth.signing.AwsSigningConfig$AwsSignatureType
-dontwarn software.amazon.awssdk.crt.auth.signing.AwsSigningConfig$AwsSignedBodyHeaderType
-dontwarn software.amazon.awssdk.crt.auth.signing.AwsSigningConfig$AwsSigningAlgorithm
-dontwarn software.amazon.awssdk.crt.auth.signing.AwsSigningConfig
-dontwarn software.amazon.awssdk.crt.auth.signing.AwsSigningResult
-dontwarn software.amazon.awssdk.crt.checksums.CRC32
-dontwarn software.amazon.awssdk.crt.checksums.CRC32C
-dontwarn software.amazon.awssdk.crt.http.HttpHeader
-dontwarn software.amazon.awssdk.crt.http.HttpRequest
-dontwarn software.amazon.awssdk.crt.http.HttpRequestBodyStream
