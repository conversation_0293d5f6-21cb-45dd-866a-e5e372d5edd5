package com.xiaopeng.xpautotest.client;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.xiaopeng.xpautotest.client.api.ApiException;
import com.xiaopeng.xpautotest.client.api.ApiResponse;

import java.io.IOException;

public class ApiResponseAdapterFactory implements TypeAdapterFactory {
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
        if (type.getRawType() != ApiResponse.class) {
            return null;
        }

        TypeAdapter<ApiResponse<?>> delegate = (TypeAdapter<ApiResponse<?>>) gson.getDelegateAdapter(this, type);
        return (TypeAdapter<T>) new ApiResponseAdapter(delegate);
    }

    private static class ApiResponseAdapter extends TypeAdapter<ApiResponse<?>> {
        private final TypeAdapter<ApiResponse<?>> delegate;

        ApiResponseAdapter(TypeAdapter<ApiResponse<?>> delegate) {
            this.delegate = delegate;
        }

        @Override
        public void write(JsonWriter out, ApiResponse<?> value) throws IOException {
            delegate.write(out, value);
        }

        @Override
        public ApiResponse<?> read(JsonReader in) throws IOException {
            ApiResponse<?> response = delegate.read(in);
            if (!response.isSuccess()) {
                throw new ApiException(response.getCode(), response.getMessage());
            }
            return response;
        }
    }
}
