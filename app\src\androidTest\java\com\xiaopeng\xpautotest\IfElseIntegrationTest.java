package com.xiaopeng.xpautotest;

import android.content.Context;
import android.content.Intent;
import android.os.Parcelable;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.xiaopeng.executor.TestExecutor;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * if-else条件判断功能集成测试
 *
 * 这是主要的if-else集成测试文件，测试实际执行环境中的完整功能
 *
 * 测试目标：
 * 1. 测试if-else逻辑在真实Android环境中的执行
 * 2. 验证各种if-else结构的实际运行效果
 * 3. 测试不同场景下的条件判断和分支执行
 * 4. 验证错误处理和边界情况
 *
 * 注意：executor模块的IfElseIntegrationTest专注于语法解析测试
 */
@RunWith(AndroidJUnit4.class)
public class IfElseIntegrationTest {
    
    private Context context;
    private TestExecutor testExecutor;
    
    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        testExecutor = new TestExecutor(context, "com.xiaopeng.xpautotest");
    }
    
    /**
     * 测试基础if-else逻辑
     */
    @Test
    public void test_if_else_smart_control() throws InterruptedException {
        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            startPackage \"com.xiaopeng.smartcontrol\"\n" +
                "            Delay 3\n" +
                "\n" +
                "Procedure:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 使用if-else判断门窗按钮是否存在\n" +
                "            If WaitStr \"门窗\" 5\n" +
                "                ClickByText \"门窗\"\n" +
                "                Delay 2\n" +
                "            Else\n" +
                "                ScrollTo \"门窗\"\n" +
                "                Delay 1\n" +
                "                ClickByText \"门窗\"\n" +
                "            EndIf\n" +
                "\n" +
                "PostCondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            BackToHome\n" +
                "            Delay 2\n";

        // 使用TestExecutorAdapter直接执行
        int result = testExecutor.runTestScript(1L, steps);
        assertEquals("if-else智能车控测试应成功", 0, result); // CaseExecuteState.SUCCESS = 0
    }
    
    /**
     * 测试if-endif语法（仅if结构）- 权限检查场景
     */
    @Test
    public void test_if_endif_only() throws InterruptedException {
        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            startPackage \"com.xiaopeng.smartcontrol\"\n" +
                "            Delay 2\n" +
                "\n" +
                "Procedure:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 测试仅if结构，无else分支\n" +
                "            If WaitStr \"允许\" 3\n" +
                "                ClickByText \"允许\"\n" +
                "                Delay 1\n" +
                "            EndIf\n" +
                "            \n" +
                "            # 另一个if-endif测试\n" +
                "            If WaitStr \"门窗\" 5\n" +
                "                ClickByText \"门窗\"\n" +
                "            EndIf\n" +
                "\n" +
                "PostCondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            BackToHome\n" +
                "            Delay 2\n";

        int result = testExecutor.runTestScript(2L, steps);
        assertEquals("if-endif测试应该成功", 0, result);
    }

    /**
     * 测试if-elseif-else完整结构 - 空调控制场景
     */
    @Test
    public void test_if_elseif_else_hvac() throws InterruptedException {
        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            startPackage \"com.xiaopeng.carhvac\"\n" +
                "            Delay 3\n" +
                "\n" +
                "Procedure:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 使用if-elseif-else判断空调控制选项\n" +
                "            If WaitStr \"com.xiaopeng.carhvac:id/hvac_power_btn\" 3\n" +
                "                ClickById \"com.xiaopeng.carhvac:id/hvac_power_btn\"\n" +
                "            ElseIf WaitStr \"空调\" 3\n" +
                "                ClickByText \"空调\"\n" +
                "            ElseIf WaitStr \"气候\" 3\n" +
                "                ClickByText \"气候\"\n" +
                "            Else\n" +
                "                startPackage \"com.xiaopeng.smartcontrol\"\n" +
                "                Delay 3\n" +
                "            EndIf\n" +
                "\n" +
                "PostCondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            BackToHome\n" +
                "            Delay 2\n";

        int result = testExecutor.runTestScript(3L, steps);
        assertEquals("if-elseif-else空调控制测试应该成功", 0, result);
    }
    
    /**
     * 测试if-elseif结构（无else分支）- 多媒体控制场景
     */
    @Test
    public void test_if_elseif_only_media() throws InterruptedException {
        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            startPackage \"com.xiaopeng.media\"\n" +
                "            Delay 3\n" +
                "\n" +
                "Procedure:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 多个elseif条件，测试优先级选择\n" +
                "            If WaitStr \"音乐\" 2\n" +
                "                ClickByText \"音乐\"\n" +
                "                shell \"echo 'Music mode selected'\"\n" +
                "            ElseIf WaitStr \"收音机\" 2\n" +
                "                ClickByText \"收音机\"\n" +
                "                shell \"echo 'Radio mode selected'\"\n" +
                "            ElseIf WaitStr \"蓝牙\" 2\n" +
                "                ClickByText \"蓝牙\"\n" +
                "                shell \"echo 'Bluetooth mode selected'\"\n" +
                "            ElseIf WaitStr \"USB\" 2\n" +
                "                ClickByText \"USB\"\n" +
                "                shell \"echo 'USB mode selected'\"\n" +
                "            EndIf\n" +
                "\n" +
                "PostCondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            BackToHome\n" +
                "            Delay 2\n";

        int result = testExecutor.runTestScript(4L, steps);
        assertEquals("if-elseif多媒体控制测试应该成功", 0, result);
    }

    /**
     * 测试不同参数数量的Action在if条件中的使用
     */
    @Test
    public void test_if_else_different_action_parameters() throws InterruptedException {
        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            startPackage \"com.xiaopeng.smartcontrol\"\n" +
                "            Delay 2\n" +
                "\n" +
                "Procedure:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 测试无参数Action\n" +
                "            If BackToHome\n" +
                "                shell \"echo 'BackToHome executed successfully'\"\n" +
                "            Else\n" +
                "                shell \"echo 'BackToHome execution failed'\"\n" +
                "            EndIf\n" +
                "            \n" +
                "            Delay 1\n" +
                "            startPackage \"com.xiaopeng.smartcontrol\"\n" +
                "            Delay 2\n" +
                "            \n" +
                "            # 测试1个参数Action\n" +
                "            If WaitStr \"门窗\" 5\n" +
                "                ClickByText \"门窗\"\n" +
                "                shell \"echo 'Single parameter action succeeded'\"\n" +
                "            Else\n" +
                "                shell \"echo 'Single parameter action failed'\"\n" +
                "            EndIf\n" +
                "            \n" +
                "            # 测试2个参数Action\n" +
                "            If WaitId \"com.xiaopeng.smartcontrol:id/menu_item\" 3\n" +
                "                ClickById \"com.xiaopeng.smartcontrol:id/menu_item\"\n" +
                "                shell \"echo 'Two parameter action succeeded'\"\n" +
                "            ElseIf WaitStr \"设置\" 3\n" +
                "                ClickByText \"设置\"\n" +
                "                shell \"echo 'Backup plan succeeded'\"\n" +
                "            Else\n" +
                "                shell \"echo 'All plans failed'\"\n" +
                "            EndIf\n" +
                "            \n" +
                "            # 测试3个参数Action\n" +
                "            If CheckLogcatContains \"com.xiaopeng.smartcontrol:id/title\" \"智能车控\" 10\n" +
                "                shell \"echo 'Three parameter action verification succeeded'\"\n" +
                "            Else\n" +
                "                shell \"echo 'Three parameter action verification failed'\"\n" +
                "            EndIf\n" +
                "\n" +
                "PostCondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            BackToHome\n" +
                "            Delay 1\n";

        int result = testExecutor.runTestScript(5L, steps);
        assertEquals("不同参数Action的if-else测试应该成功", 0, result);
    }


    /**
     * 测试if-else在不同阶段的使用 - 完整生命周期测试
     */
    @Test
    public void test_if_else_in_different_phases() throws InterruptedException {
        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 前置条件中的if-else\n" +
                "            If WaitStr \"允许\" 3\n" +
                "                ClickByText \"允许\"\n" +
                "                shell \"echo 'Permission granted successfully'\"\n" +
                "            Else\n" +
                "                shell \"echo 'No permission grant needed'\"\n" +
                "            EndIf\n" +
                "            \n" +
                "            startPackage \"com.xiaopeng.smartcontrol\"\n" +
                "            Delay 3\n" +
                "\n" +
                "Procedure:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 主流程中的if-else\n" +
                "            If WaitStr \"门窗\" 5\n" +
                "                ClickByText \"门窗\"\n" +
                "                shell \"echo 'Main process: Door window function test'\"\n" +
                "                Delay 2\n" +
                "            Else\n" +
                "                shell \"echo 'Main process: Door window function unavailable'\"\n" +
                "            EndIf\n" +
                "\n" +
                "PostCondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 后置条件中的if-else\n" +
                "            If WaitStr \"退出\" 2\n" +
                "                ClickByText \"退出\"\n" +
                "                shell \"echo 'Application exited normally'\"\n" +
                "            Else\n" +
                "                BackToHome\n" +
                "                shell \"echo 'Forced return to home'\"\n" +
                "            EndIf\n" +
                "            \n" +
                "            Delay 2\n";

        int result = testExecutor.runTestScript(8L, steps);
        assertEquals("不同阶段if-else测试应该成功", 0, result);
    }

}
