package com.xiaopeng.xpautotest.community.utils;

import android.util.Log;

import androidx.annotation.NonNull;

import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;

import java.io.BufferedReader;
import java.io.IOException; // 引入 IOException
import java.io.InputStreamReader;
// 引入并发相关的类
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.List; 
import java.util.stream.Collectors; 


/**
 * 执行命令
 */
public class CMDUtils {

    private static final String TAG = "CMDUtils";
 
    // 用于执行阻塞 IO 操作的线程池
    private static final ExecutorService logcatExecutor = Executors.newCachedThreadPool(new ThreadFactory() {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        @Override
        public Thread newThread(@NonNull Runnable r) {
            Thread t = new Thread(r, "logcat-reader-" + threadNumber.getAndIncrement());
            t.setDaemon(true); // 设置为守护线程，以便应用退出时它们不会阻止 JVM 关闭
            return t;
        }
    });

    /**
     * 命令执行结果
     */
    public static class CMD_Result {
        public int resultCode;
        public String error;
        public String success;

        public CMD_Result(int resultCode, String error, String success) {
            this.resultCode = resultCode;
            this.error = error;
            this.success = success;
        }
    }


    /**
     * 执行命令
     *
     * @param command         命令
     * @param isShowCommand   是否显示执行的命令
     * @param isNeedResultMsg 是否反馈执行的结果
     * @retrun CMD_Result
     */
    public static CMD_Result runCMD(String command, boolean isShowCommand,
                                    boolean isNeedResultMsg){
        if (isShowCommand)
            Log.i(TAG, "runCMD:" + command);
        CMD_Result cmdRsult = null;
        int result;
        Process process = null;
        BufferedReader successReader = null;
        BufferedReader errorReader = null;
        try {
//            command = command.replaceAll(";", "&");
            process = Runtime.getRuntime().exec(command);
            result = process.waitFor();
            if (isNeedResultMsg) {
                StringBuilder successMsg = new StringBuilder();
                StringBuilder errorMsg = new StringBuilder();
                successReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                String line;
                while ((line = successReader.readLine()) != null) {
                    successMsg.append(line).append("\n");
                }
                while ((line = errorReader.readLine()) != null) {
                    errorMsg.append(line).append("\n");
                }
                cmdRsult = new CMD_Result(result, errorMsg.toString(),
                        successMsg.toString());
            }
        } catch (Exception e) {
            Log.e(TAG, "run CMD:" + command + " failed");
            e.printStackTrace();
            // Todo: 这里还需要上报具体的异常情况，现在先简单做处理
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
        } finally {
            if (successReader != null) {
                try {
                    successReader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (errorReader != null) {
                try {
                    errorReader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (process != null) {
                process.destroy();
            }
        }
        return cmdRsult;
    }

//    public static boolean grepLogcat(String tag, String expect, int timeout) {
//        String command = "logcat -s " + tag;
//        try {
//            Process process = Runtime.getRuntime().exec(command);
//        } catch (Exception e) {
//            Log.e(TAG, "run logcat -s " + tag + " command failed");
//            e.printStackTrace();
//        }
//        return false;
//    }



    public static CMD_Result callAppProcess(String[] command) {
        CMD_Result cmdRsult = null;
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true); // 合并标准错误流和标准输出流

        Process process = null;
        BufferedReader reader = null;
        try {
            process = processBuilder.start();

            // 读取命令输出
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();
            Log.w(TAG, "Exit code: " + exitCode);
            cmdRsult = new CMD_Result(exitCode, output.toString(),
                    output.toString());
        } catch (Exception e) {
            Log.e(TAG, "Error calling app_process", e);
        } finally {
            // 释放资源
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    Log.e(TAG, "Error closing reader", e);
                }
            }
            if (process != null) {
                process.destroy();
            }
        }
        return cmdRsult;
    }




/**
 * 执行 logcat 命令并通过链式 grep -E 进行过滤 (AND 逻辑)。
 * 只有当某一行同时匹配所有提供的模式时，该行才会被输出。
 * 此方法会阻塞，直到找到第一行匹配的输出、超时或 logcat 进程结束。
 *
 * @param logcatAndPatterns 一个包含多个用于 `grep -E` 的扩展正则表达式模式的列表。
 * @param timeoutMs 等待第一行匹配输出的超时时间（毫秒）。
 * @return 命令的标准输出的第一行内容（如果找到匹配），如果在超时时间内无匹配则返回 null。
 */
public static String logcatGrep(List<String> logcatAndPatterns, long timeoutMs) {
    if (logcatAndPatterns == null || logcatAndPatterns.isEmpty()) {
        FileLogger.e(TAG, "Pattern list cannot be null or empty for AND grep.");
        return null;
    }
    // 构建链式 grep 命令
    StringBuilder commandBuilder = new StringBuilder("logcat"); 
    for (String pattern : logcatAndPatterns) {
        commandBuilder.append(" | grep -E --line-buffered \"").append(pattern).append("\"");
    }
    String command = commandBuilder.toString();
    FileLogger.i(TAG, "Running logcat command with AND: '" + command + "'. Waiting for first matching line up to " + timeoutMs + "ms.", false);

    Process logcatProcess = null;
    BufferedReader reader = null;
    Future<String> future = null;

    try {
        logcatProcess = Runtime.getRuntime().exec(new String[]{"sh", "-c", command});
        reader = new BufferedReader(new InputStreamReader(logcatProcess.getInputStream()));
        
        final BufferedReader finalReader = reader;
        Callable<String> readTask = () -> {
            try {
                // 只读取一行，这是一个阻塞操作
                return finalReader.readLine();
            } catch (IOException e) {
                return null;
            }
        };

        future = logcatExecutor.submit(readTask);

        // 等待结果，带有超时
        String firstLine = future.get(timeoutMs, TimeUnit.MILLISECONDS);

        if (firstLine != null) {
            return firstLine + "\n";
        } else {
            FileLogger.i(TAG, "Logcat grep returned null, no line matched within the timeout.");
            return null;
        }
    } catch (Exception e) {
        if (future != null) {
            future.cancel(true);
        }
        return null;
    } finally {
        // 首先销毁进程，以防止在关闭流时发生挂起。
        if (logcatProcess != null) {
            logcatProcess.destroy();
        }
        // 现在关闭 reader 不应再阻塞。
        if (reader != null) {
            try {
                reader.close();
            } catch (IOException e) {
                FileLogger.d(TAG, "Error closing logcat reader : " + e.getMessage());
            }
        }
    }
}

}
