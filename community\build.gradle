apply plugin: 'com.android.library'

android {
    namespace 'com.xiaopeng.xpautotest.community'
    compileSdkVersion rootProject.ext.versions.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.versions.minSdkVersion
        targetSdkVersion rootProject.ext.versions.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.2.0'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
//    implementation 'org.apache.commons:commons-compress:1.18'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.5.3'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.5.3'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.5.3'
    implementation('com.xiaopeng.lib:lib_utils:1.7.5.9')
}
