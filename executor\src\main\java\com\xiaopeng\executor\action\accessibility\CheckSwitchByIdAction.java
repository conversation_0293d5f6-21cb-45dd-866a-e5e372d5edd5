package com.xiaopeng.executor.action.accessibility;

import android.graphics.Rect;
import android.view.accessibility.AccessibilityNodeInfo;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class CheckSwitchByIdAction extends BaseAction {
    private static final String TAG = "CheckSwitchByIdAction";
    private static final String XSWITCH_CLASS_NAME = "com.xiaopeng.xui.widget.XSwitch";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String resourceId = context.getStringParam();
        String expectedState = context.getStringParam();
        
        if (resourceId == null) {
            throw new ActionException("resourceId is null!", FailureCode.SI001);
        }
        
        if (expectedState == null) {
            throw new ActionException("expectedState is null!", FailureCode.SI001);
        }
        
        // 验证expectedState参数，只接受"on"或"off"
        expectedState = expectedState.toLowerCase().trim();
        if (!"on".equals(expectedState) && !"off".equals(expectedState)) {
            throw new ActionException("expectedState must be 'on' or 'off', but got: " + expectedState, FailureCode.SI001);
        }
        
        boolean expectedChecked = "on".equals(expectedState);

        // 根据resourceId查找节点
        AccessibilityNodeInfo targetNode = this.service.findNodeById(resourceId);
        if (targetNode == null) {
            FileLogger.e(TAG, "Target node not found for resourceId: " + resourceId);
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
            return TestResult.failure("Target node not found for resourceId: " + resourceId);
        }

        try {
            // 查找子节点中第一个class为com.xiaopeng.xui.widget.XSwitch的控件
            AccessibilityNodeInfo switchNode = findFirstXSwitchChild(targetNode);
            if (switchNode == null) {
                FileLogger.e(TAG, "XSwitch child node not found in target node: " + resourceId);
                FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
                return TestResult.failure("XSwitch child node not found in target node: " + resourceId);
            }

            try {
                // 检查XSwitch控件的checked属性
                boolean isChecked = switchNode.isChecked();
                FileLogger.i(TAG, "XSwitch checked status: " + isChecked + ", expected: " + expectedState);
                
                if (isChecked == expectedChecked) {
                    return TestResult.success("XSwitch state matches expected '" + expectedState + "' for resourceId: " + resourceId);
                } else {
                    FailureContextHolder.setFailureIfNotSet(FailureCode.BF003);
                    return TestResult.failure("XSwitch state is '" + (isChecked ? "on" : "off") + "' but expected '" + expectedState + "' for resourceId: " + resourceId);
                }
            } finally {
                switchNode.recycle();
            }
        } finally {
            targetNode.recycle();
        }
    }

    /**
     * 递归查找第一个class为com.xiaopeng.xui.widget.XSwitch的子节点
     * @param node 父节点
     * @return 第一个XSwitch子节点，如果没有找到返回null
     */
    private AccessibilityNodeInfo findFirstXSwitchChild(AccessibilityNodeInfo node) {
        if (node == null) {
            return null;
        }
        // 前序遍历
        int childCount = node.getChildCount();
        for (int i = 0; i < childCount; i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child == null) {
                continue;
            }

            // 检查当前子节点的class是否为XSwitch
            CharSequence className = child.getClassName();
            if (className != null && XSWITCH_CLASS_NAME.equals(className.toString())) {
                Rect bounds = new Rect();
                child.getBoundsInScreen(bounds);
                FileLogger.i(TAG, "Found XSwitch child node at bounds: [" + bounds.left + "," + bounds.top + "][" + bounds.right + "," + bounds.bottom + "]");
                return child; // 找到第一个XSwitch节点，直接返回
            }

            // 递归查找子节点的子节点
            AccessibilityNodeInfo result = findFirstXSwitchChild(child);
            if (result != null) {
                child.recycle(); // 回收当前子节点
                return result;
            }
            
            child.recycle(); // 回收当前子节点
        }
        
        return null; // 没有找到XSwitch节点
    }
}