package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class HeartbeatEntity implements Parcelable, ReportableEntity {
    private long taskId;
    private long taskExecutionId;
    private String mode;

    public HeartbeatEntity(long taskId, long taskExecutionId, String mode) {
        this.taskId = taskId;
        this.taskExecutionId = taskExecutionId;
        this.mode = mode;
    }

    protected HeartbeatEntity(Parcel in) {
        taskId = in.readLong();
        taskExecutionId = in.readLong();
        mode = in.readString();
    }

    public static final Creator<HeartbeatEntity> CREATOR = new Creator<HeartbeatEntity>() {
        @Override
        public HeartbeatEntity createFromParcel(Parcel in) {
            return new HeartbeatEntity(in);
        }

        @Override
        public HeartbeatEntity[] newArray(int size) {
            return new HeartbeatEntity[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(taskId);
        dest.writeLong(taskExecutionId);
        dest.writeString(mode);
    }

    @Override
    public long getTaskExecutionId() {
        return taskExecutionId;
    }
}
