package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class CheckLogcatContainsAction extends BaseAction{
    private static final String TAG = "CheckLogcatContainsAction";

    private static final long DEFAULT_TIMEOUT_MS = 10000; // 默认超时时间 10 秒

    @Override
    public TestResult execute(ActionContext context) throws ActionException {

        String rawParams = context.getAllParams();
        if (rawParams == null || rawParams.trim().isEmpty()) {
            String errorMsg = "No parameters provided.";
            FileLogger.e(TAG, errorMsg);
            throw new ActionException(errorMsg, FailureCode.SI001);
        }
        String patternPart = "";
        String timeoutPart = null;
        long timeoutMs = DEFAULT_TIMEOUT_MS;
        List<String> patterns = new ArrayList<>(); // 初始化列表

        // 1. 尝试分离模式部分和超时部分 (按最后一个空格分割)
        int lastSpaceIndex = rawParams.lastIndexOf(' ');
        if (lastSpaceIndex != -1 && lastSpaceIndex < rawParams.length() - 1) {
            String potentialTimeout = rawParams.substring(lastSpaceIndex + 1).trim();
            try {
                // 尝试解析最后一个部分为超时时间 (秒)
                long parsedTimeoutSeconds = Long.parseLong(potentialTimeout);
                timeoutMs = parsedTimeoutSeconds * 1000; // 转换为毫秒
                if (timeoutMs <= 0) {
                    FileLogger.i(TAG, "Invalid timeout value (<= 0): " + potentialTimeout + "s. Using default: " + DEFAULT_TIMEOUT_MS + "ms", false);
                    timeoutMs = DEFAULT_TIMEOUT_MS;
                }
                timeoutPart = potentialTimeout; // 记录解析出的超时部分
                patternPart = rawParams.substring(0, lastSpaceIndex).trim(); // 空格之前的部分是模式
                //FileLogger.i(TAG, "Detected timeout: " + timeoutPart + "s (" + timeoutMs + "ms)", false);
            } catch (NumberFormatException e) {
                // 如果最后一个部分不是数字，则认为整个字符串都是模式部分
                patternPart = rawParams.trim();
                //FileLogger.i(TAG, "Last part is not a valid timeout number. Treating entire string as patterns. Using default timeout: " + DEFAULT_TIMEOUT_MS + "ms", false);
                timeoutMs = DEFAULT_TIMEOUT_MS; // 使用默认超时
            }
        } else {
            // 没有找到空格，或者空格在最后，整个字符串都是模式部分
            patternPart = rawParams.trim();
            //FileLogger.i(TAG, "No space found or space at the end. Treating entire string as patterns. Using default timeout: " + DEFAULT_TIMEOUT_MS + "ms", false);
            timeoutMs = DEFAULT_TIMEOUT_MS; // 使用默认超时
        }

        // 2. 解析模式部分: 使用正则表达式查找所有双引号包裹的字符串
        // "ABC"
        // "A BC" "DEF"
        // "Abc" "error\\s*occurred"
        if (!patternPart.isEmpty()) {
            Pattern p = Pattern.compile("\"((?:\\\\\"|[^\"])*)\"");
            Matcher m = p.matcher(patternPart);
            while (m.find()) {
                String extractedPattern = m.group(1);
                patterns.add(extractedPattern);
            }
        }

        // 3. 检查是否成功解析到任何模式
        if (patterns.isEmpty()) {
            String errorMsg = "No patterns enclosed in double quotes found in: " + patternPart;
            throw new ActionException(errorMsg, FailureCode.SI001);
        }

        // FileLogger.i(TAG, "Executing logcat with grep patterns " + patterns + " and timeout: " + timeoutMs + "ms. Checking for output matching ALL patterns.",false);

        // 4. 调用 CMDUtils 的logcatGrep
        String logcatOutput = CMDUtils.logcatGrep(patterns, timeoutMs); // 调用方法，传入列表

        // 4.1. 执行 logcat -c 命令清空日志缓冲区（无论 logcatGrep 结果如何都执行）
        try {
            CMDUtils.CMD_Result clearResult = CMDUtils.runCMD("logcat -c", false, false);
            FileLogger.i(TAG, "Executed logcat -c command to clear log buffer", false);
        } catch (Exception e) {
            // 清空日志缓冲区失败不影响后续流程，只记录日志
            FileLogger.e(TAG, "Failed to clear logcat buffer with 'logcat -c', but continuing: " + e.getMessage(),e);
        }

        // 5. 检查返回值
        if (logcatOutput != null && !logcatOutput.isEmpty()) {
            // 记录部分输出
            String snippet = logcatOutput.length() > 200 ? logcatOutput.substring(0, 200) + "..." : logcatOutput;
            String successMsg = "Logcat output snippet: " + patterns;
            FileLogger.i(TAG, successMsg + " Snippet:\n" + snippet,false);
            return TestResult.success(successMsg);
        } else {
            // 超时、错误或没有行同时匹配所有模式
            String failureMsg = "Did not receive any output matching ALL patterns for: " + patterns + " within " + timeoutMs + "ms";
            FileLogger.i(TAG, failureMsg,false);
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF002);
            return TestResult.failure(failureMsg);
        }
    }

}