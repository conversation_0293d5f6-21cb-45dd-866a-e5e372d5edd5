<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    package="com.xiaopeng.xpautotest"
    android:sharedUserMaxSdkVersion="32">
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.SUPER_APPLICATION_RUNNING" />
    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    <uses-permission android:name="android.permission.MEDIA_PROJECTION" />
    <uses-permission android:name="com.xiaopeng.permission.OTA_SERVICE" />
    <uses-permission android:name="com.xiaopeng.permission.CAR_SERVICE" />
    <uses-permission android:name="com.xiaopeng.permission.ACTIVITY" />
    <uses-permission android:name="com.xiaopeng.permission.SERVICE" />
    <uses-permission android:name="com.xiaopeng.permission.BROADCAST" />
    <application
        android:name="com.xiaopeng.xpautotest.App"
        android:configChanges="keyboard|keyboardHidden|navigation"
        android:extractNativeLibs="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/autotest_AppTheme"
        tools:replace="android:label">
        <meta-data
            android:name="com.xiaopeng.lib.lib_feature_modules"
            android:value="com.xiaopeng.carcontrol,com.xiaopeng.caraccount,com.xiaopeng.carspeechservice" />
        <activity
            android:name="com.xiaopeng.xpautotest.ui.MainActivity"
            android:configChanges="navigation|colorMode|mnc|mcc|fontScale|keyboard|keyboardHidden|screenSize|smallestScreenSize|density|locale|uiMode|orientation|layoutDirection|screenLayout|touchscreen"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/autotest_AppTheme">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name="com.xiaopeng.xpautotest.service.TestExecutionService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice"/>

        <service
            android:name="com.xiaopeng.xpautotest.service.DebuggingModeService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice"/>

        <service
            android:name=".accessibility.AutoTestAccessibilityService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/service_config" />
        </service>

        <receiver android:name="com.xiaopeng.xpautotest.receiver.StartBroadcastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.xiaoppeng.xpautotest.OPEN_APP_ACTION" />
            </intent-filter>
        </receiver>
        <service
            android:name="com.xiaopeng.xpautotest.service.OSSUploadService"
            android:exported="true">  
            <intent-filter>
                <action android:name="com.xiaopeng.xpautotest.action.START_OSS_UPLOAD_SERVICE" />
            </intent-filter>
        </service> 
    </application>

</manifest>