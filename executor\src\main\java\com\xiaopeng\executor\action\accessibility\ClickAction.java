package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.Scanner;

public class ClickAction extends BaseAction {
    private static final String TAG = "ClickAction";
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String coordinateStr = (String) context.getStringParam();
        if (coordinateStr == null) {
            throw new ActionException("coordinate is null!", FailureCode.SI001);
        }

        boolean result;
        FileLogger.i(TAG, "params: " + coordinateStr);
        try (Scanner scanner = new Scanner(coordinateStr).useDelimiter("[^0-9]+")) {
            int x = scanner.nextInt();
            int y = scanner.nextInt();
            scanner.close();
            result = this.service.click(x, y);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ActionException(e.getMessage(), FailureCode.SI002,e);
        }

        if (!result) {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB003);
            return TestResult.failure("Failed to click");
        }
        return TestResult.success("Click successfully.");
    }
}
