package com.xiaopeng.executor.action.accessibility;

import android.os.SystemClock;
import android.view.accessibility.AccessibilityNodeInfo;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.Locale;

public class ScrollToAction extends BaseAction {
    private static final String TAG = "ScrollTo";
    private static final long POST_SCROLL_WAIT_MS = 100;
    private static final int SCROLL_TO_CENTER_DURATION_MS = 600;

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String targetTypeParam;
        String targetValueParam = context.getStringParam();
        String scrollDirectionParam = context.getStringParam();

        if (targetValueParam == null || targetValueParam.isEmpty()) {
            String errorMessage = "Target value parameter is null or empty.";
            FileLogger.e(TAG, errorMessage);
            throw new ActionException(errorMessage, FailureCode.SI001);
        }
        if (targetValueParam.contains(":id/")) {
            targetTypeParam = "id";
        } else {
            targetTypeParam = "text";
        }

        String targetType = targetTypeParam.toLowerCase(Locale.ROOT);
        String scrollDirection = (scrollDirectionParam == null || scrollDirectionParam.isEmpty()) ?
                                 "vertical" : scrollDirectionParam.toLowerCase(Locale.ROOT);

        AccessibilityNodeInfo targetNode = null;
        String failureReason;

        try {
            // 查找并使它显示到屏幕上ACTION_SHOW_ON_SCREEN
            targetNode = this.service.findNodeAndEnsureVisible(targetType, targetValueParam);

            if (targetNode == null) {
                failureReason = String.format(Locale.ROOT, "Element '%s' (type: %s) not found after initial find and ensure visible attempts.", targetValueParam, targetType);
                FileLogger.w(TAG, failureReason);
                FailureContextHolder.setFailureIfNotSet(FailureCode.BF004);
                return TestResult.failure(failureReason);
            }

            // 尝试将node滑动到中心
            boolean scrollAttempted = this.service.scrollNodeToCenter(targetNode, scrollDirection, SCROLL_TO_CENTER_DURATION_MS);
            if (scrollAttempted) {
                SystemClock.sleep(POST_SCROLL_WAIT_MS);
                AccessibilityNodeInfo oldNode = targetNode;
                targetNode = this.service.findNode(targetType, targetValueParam);
                this.service.safeRecycle(oldNode);
                if (targetNode == null) {
                    failureReason = String.format(Locale.ROOT, "Element '%s' (type: %s) disappeared after scroll to center attempt and re-find.", targetValueParam, targetType);
                    FileLogger.w(TAG, failureReason);
                    FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
                    return TestResult.failure(failureReason);
                }
            }
            // 滑动以后再检查一次可见性
            if (targetNode.isVisibleToUser()) {
                return TestResult.success(String.format(Locale.ROOT, "Element '%s' (type: %s) found and made visible.", targetValueParam, targetType));
            } else {
                failureReason = String.format(Locale.ROOT, "ScrollToElementAction FAILED. Element '%s' (type: %s) became null or was not found or was invisible after scroll.", targetValueParam, targetType);
                FileLogger.w(TAG, failureReason);
                FailureContextHolder.setFailureIfNotSet(FailureCode.AB001);
                return TestResult.failure(failureReason);
            }
        } finally {
            if (targetNode != null) {
                this.service.safeRecycle(targetNode); 
            }
        }
    }
} 