<?xml version="1.0" encoding="utf-8"?>
<com.xiaopeng.xui.widget.XConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="100dp">

    <!-- title -->
    <include layout="@layout/at_layout_factory_title" />

    <!-- footer -->
<!--    <include layout="@layout/layout_factory_footer" />-->

    <!-- tab & content -->
    <com.xiaopeng.xui.widget.XLinearLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/title_height"
        android:layout_marginBottom="@dimen/factory_footer_height"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- tab -->
        <com.xiaopeng.xui.widget.XFrameLayout
            android:id="@+id/fragment_container_left"
            android:layout_width="@dimen/factory_fragment_left_width"
            android:layout_height="match_parent" />

        <!-- separator -->
        <com.xiaopeng.xui.view.XView
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/separator_line_bg" />

        <!-- content -->
        <com.xiaopeng.xui.widget.XFrameLayout
            android:id="@+id/fragment_container_right"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/fragment_right_padding" />

    </com.xiaopeng.xui.widget.XLinearLayout>

    <com.xiaopeng.xui.widget.XConstraintLayout
        android:id="@+id/web_error_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/x_color_bg_1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/fragment_container"
        app:vuiMode="DISABLED">

        <com.xiaopeng.xui.widget.XTextView
            android:id="@+id/tv_error_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/error_unknown"
            android:textColor="@color/x_theme_text_01"
            android:textSize="40sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </com.xiaopeng.xui.widget.XConstraintLayout>

    <com.xiaopeng.xui.widget.XConstraintLayout
        android:id="@+id/web_loading_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/x_color_bg_1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/fragment_container"
        app:vuiMode="DISABLED">

        <com.xiaopeng.xui.widget.XLoading
            android:id="@+id/web_loading"
            style="@style/XLoading.XLarge"
            app:layout_constraintBottom_toTopOf="@id/web_tv_loading"
            app:layout_constraintEnd_toEndOf="@id/web_tv_loading"
            app:layout_constraintStart_toStartOf="@id/web_tv_loading" />

        <com.xiaopeng.xui.widget.XTextView
            android:id="@+id/web_tv_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/loading"
            android:textColor="@color/x_theme_text_01"
            android:textSize="40sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </com.xiaopeng.xui.widget.XConstraintLayout>

</com.xiaopeng.xui.widget.XConstraintLayout>