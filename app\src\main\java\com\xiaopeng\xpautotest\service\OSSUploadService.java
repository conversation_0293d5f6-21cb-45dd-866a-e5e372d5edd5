package com.xiaopeng.xpautotest.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.xiaopeng.xpautotest.client.oss.OSSUploadQueueManager;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.event.FileUploadEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class OSSUploadService extends Service {
    private static final String TAG = "OSSUploadService";
    private OSSUploadQueueManager mOSSUploadQueueManager;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mOSSUploadQueueManager = OSSUploadQueueManager.getInstance();
        mOSSUploadQueueManager.start();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (mOSSUploadQueueManager != null) {
            mOSSUploadQueueManager.stop();
            mOSSUploadQueueManager = null;
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onFileUploadEvent(FileUploadEvent event) {
        //FileLogger.d(TAG, "Received FileUploadEvent for: " + event.getFilePath());
        if (mOSSUploadQueueManager != null) {
            mOSSUploadQueueManager.addUploadTask(event.getFilePath());
        }
    }
}