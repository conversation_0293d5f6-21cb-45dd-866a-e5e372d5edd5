package com.xiaopeng.executor.action.accessibility;

import android.graphics.Rect;
import android.os.Build;
import android.view.accessibility.AccessibilityNodeInfo;
import androidx.annotation.RequiresApi;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

public class ClickByTextNewAction extends BaseAction {
    private static final String TAG = "ClickByTextNewAction";

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String text = (String) context.getStringParam();
        if (text == null) {
            throw new ActionException("text is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "text: " + text);

        // 先进行元素查找，这部分不设置超时
        if (this.service == null) {
            throw new ActionException("AccessibilityService is null", FailureCode.AB002);
        }

        // 查找元素（这部分使用现有的查找逻辑，可能包含重试等机制）
        AccessibilityNodeInfo node = this.service.findNodeByText(text, null, true);
        if (node == null) {
            return TestResult.failure("Failed to find element with text: '" + text + "'");
        }

        try {
            Rect bounds = new Rect();
            node.getBoundsInScreen(bounds);

            if (bounds.width() <= 0 || bounds.height() <= 0) {
                node.recycle();
                return TestResult.failure("Element has invalid bounds for text: '" + text + "': " + bounds.toShortString());
            }

            // 计算元素中心坐标
            int centerX = (bounds.left + bounds.right) / 2;
            int centerY = (bounds.top + bounds.bottom) / 2;

            FileLogger.i(TAG, "Found element for text='" + text + "', bounds=" + bounds.toShortString() + ", center=(" + centerX + "," + centerY + ")");

            // 现在开始异步点击，只对点击操作设置超时
            final CountDownLatch latch = new CountDownLatch(1);
            final AtomicReference<TestResult> result = new AtomicReference<>();

            GestureCallback callback = new GestureCallback() {
                @Override
                public void onSuccess() {
                    String message = "Clicked by text with coordinates: '" + text + "' successfully.";
                    FileLogger.d(TAG, message);
                    result.set(TestResult.success(message));
                    latch.countDown();
                }

                @Override
                public void onFailure(String error) {
                    String message = "Failed to click by text with coordinates: '" + text + "': " + error;
                    FileLogger.w(TAG, message);
                    result.set(TestResult.failure(message));
                    latch.countDown();
                }

                @Override
                public void onCancelled() {
                    String message = "Click by text with coordinates was cancelled: '" + text + "'";
                    FileLogger.w(TAG, message);
                    result.set(TestResult.failure(message));
                    latch.countDown();
                }
            };

            // 执行异步点击 - 只对点击操作进行异步处理
            this.service.performClickAsync(centerX, centerY, callback);

            // 只对点击手势执行设置超时时间
            long clickTimeoutMs = 2000; // 2秒超时，只针对点击操作
            try {
                boolean completed = latch.await(clickTimeoutMs, TimeUnit.MILLISECONDS);
                if (!completed) {
                    return TestResult.failure("Click gesture timed out after " + clickTimeoutMs + "ms for text: '" + text + "'");
                }
                if (result.get() == null) {
                    return TestResult.failure("Click gesture did not complete for text: '" + text + "'");
                } else {
                    return result.get();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ActionException("Click gesture was interrupted for text: '" + text + "'", FailureCode.AB001, e);
            }

        } catch (Exception e) {
            FileLogger.e(TAG, "Exception occurred during click operation for text: " + text, e);
            throw new ActionException("Exception occurred: " + e.getMessage(), FailureCode.AB001, e);
        } finally {
            node.recycle();
        }
    }
}
