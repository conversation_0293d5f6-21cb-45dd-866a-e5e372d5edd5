package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.system.ImageRecognition;

public class ClickByIconAction extends BaseAction {
    private static final String TAG = "WaitIconAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String templateIconName = (String) context.getStringParam();
        if (templateIconName == null) {
            throw new ActionException("template icon is null!", FailureCode.SI001);
        }
        String region = context.getStringParam();
        double scale = context.getDoubleParam();
        int minMatches = context.getIntParam();
        if (!templateIconName.endsWith(".png")) {
            templateIconName += ".png";
        }

        FileLogger.i(TAG, "templateIconFile: " + templateIconName + ", dropRegion: " + region + ", scaleFactor: " + scale + ", minGoodMatches: " + minMatches);
        String timeStampString = context.getTimeStampString();
        String templateImagePath = Constant.AUTOTEST_TEMPLATE_ICON_PATH + templateIconName;
        // 屏幕截图
        String screenImageName = this.service.screenShotSyn(timeStampString);
        if (screenImageName == null || screenImageName.isEmpty()) {
            return TestResult.failure("Screen captured failed!");
        }
        TestResult.ActionArtifacts actionArtifacts = new TestResult.ActionArtifacts(screenImageName,"");
        String screenImagePath = Constant.AUTOTEST_IMAGE_PATH + screenImageName;

        ImageRecognition imageRecognition = ImageRecognition.getInstance();
        ImageRecognition.MatchResult matchResult = imageRecognition.fastMatch(screenImagePath, templateImagePath, region, scale, minMatches);
        if (!matchResult.isMatched()) {
            return TestResult.failure("match icon failed! " + matchResult.getMessage(), actionArtifacts);
        }
        // 点击匹配到的图标
        int x = matchResult.getMatchRectCenterX();
        int y = matchResult.getMatchRectCenterY();
        boolean clickResult = this.service.clickAbs(x, y);
        FileLogger.i(TAG, "click icon at (" + x + ", " + y + "), clickResult: " + clickResult);
        if (!clickResult) {
            return TestResult.failure("click icon failed at (" + x + ", " + y + ")", actionArtifacts);
        }
        return TestResult.success("click icon success at (" + x + ", " + y + ")", actionArtifacts);
    }
}
