package com.xiaopeng.xpautotest.client.interceptor;

import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.constant.EnvironmentConfig;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class AuthInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request original = chain.request();
        String token = EnvironmentConfig.getInstance().getApiKey();

        Request request = original.newBuilder()
                .header("Authorization", "Token " + token)
                .header("Content-Type", "application/json")
                .build();

        return chain.proceed(request);
    }
}
