package com.xiaopeng.executor.action.accessibility;

import android.content.Context;

import com.xiaopeng.xpautotest.accessibility.AccessibilityHelper;
import com.xiaopeng.xpautotest.accessibility.AutoTestAccessibilityService;
import com.xiaopeng.executor.bean.ActionConfig;

public class AccessibilityConfig extends ActionConfig {
    private static AccessibilityConfig instance;
    private AccessibilityHelper helper;

    public static synchronized AccessibilityConfig getInstance() {
        if (instance == null) {
            instance = new AccessibilityConfig();
        }
        return instance;
    }

    public void initService(Context context, String packageName) {
//        helper = AccessibilityHelper.getInstance(getApplicationContext());
        helper = AccessibilityHelper.getInstance(context);
        String serviceName = packageName + "/" + AutoTestAccessibilityService.class.getName().replace(packageName, "");

        if (!helper.isServiceEnabled(serviceName)) {
            helper.enableAccessibilityService(serviceName);
            try {
                Thread.sleep(1000); // 等待1秒让服务启动
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    public AccessibilityHelper getService() {
        return helper;
    }
}
