package com.xiaopeng.xpautotest.dialog;

import android.content.Context;
import android.content.DialogInterface;

import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xui.app.XDialog;
import com.xiaopeng.xui.app.XDialogSystemType;

import androidx.annotation.NonNull;

public class SystemDialog extends XDialog {

    private static final String TAG = SystemDialog.class.getSimpleName();

    public SystemDialog(@NonNull Context context) {
        super(context);

        setSystemDialog(XDialogSystemType.TYPE_SYSTEM_DIALOG);

        this.setOnShowListener();
        this.setDismissListener();
    }

    public SystemDialog(@NonNull Context context, int style) {
        super(context, style);

        setSystemDialog(XDialogSystemType.TYPE_SYSTEM_DIALOG);

        this.setOnShowListener();
        this.setDismissListener();
    }

    private void setOnShowListener() {
        this.setOnShowListener(new DialogInterface.OnShowListener() {
            @Override
            public void onShow(DialogInterface dialog) {
                Log.i(TAG, "Dialog onShow " + dialog);
            }
        });
    }

    private void setDismissListener() {
        this.setOnDismissListener(new DialogInterface.OnDismissListener() {

            @Override
            public void onDismiss(DialogInterface dialog) {
                Log.i(TAG, "Dialog onDismiss " + dialog);
            }
        });
    }
}
