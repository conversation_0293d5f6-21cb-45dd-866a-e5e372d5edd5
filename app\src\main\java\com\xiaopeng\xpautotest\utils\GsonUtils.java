package com.xiaopeng.xpautotest.utils;

import com.google.gson.Gson;
import com.xiaopeng.xpautotest.community.utils.Log;

import java.lang.reflect.Type;

public class GsonUtils {
    public static String toJson(Object obj, String from) {
        if (obj == null) {
            return null;
        }
        try {
            return new Gson().toJson(obj);
        } catch (Exception e) {
            Log.e("GsonUtils", "from:" + from, e);
        }
        return null;
    }

    public static <T> T fromJson(String json, Type type) {
        Gson gson = new Gson();
        return gson.fromJson(json, type);
    }

}
