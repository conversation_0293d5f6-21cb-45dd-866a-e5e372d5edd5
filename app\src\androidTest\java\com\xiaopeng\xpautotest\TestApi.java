package com.xiaopeng.xpautotest;

import android.app.Instrumentation;
import android.content.Context;
import android.content.Intent;
import android.os.Parcelable;

import com.xiaopeng.executor.action.SmartActionExecutor;
import com.xiaopeng.executor.action.carapi.CarApiConfig;
import com.xiaopeng.executor.action.carapi.CarApiFactory;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.TechType;
import com.xiaopeng.executor.TestExecutor;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.bean.TestScriptEntity;
import com.xiaopeng.xpautotest.service.TestExecutionService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import static org.junit.Assert.assertTrue;


@RunWith(AndroidJUnit4.class)
public class TestApi {
    private static final String TAG = "TestOne";
    private Context context;

    @Before
    public void setup() throws Exception {
        Instrumentation instrumentation = InstrumentationRegistry.getInstrumentation();
        context = instrumentation.getTargetContext();
//        AccessibilityHelper helper = AccessibilityHelper.getInstance(context);
//        String packageName = "com.xiaopeng.xpautotest";
//        String serviceName = packageName + "/" + AutoTestAccessibilityService.class.getName().replace(packageName, "");
//        if (!helper.isServiceEnabled(serviceName)) {
//            helper.enableAccessibilityService(serviceName);
//        }
    }

    @Test
    public void test_script() throws IOException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, InstantiationException {
        String scriptName = "/sdcard/AutoTest/script/test_01_01.xw";
        TestExecutor testExecutor = new TestExecutor(context, "com.xiaopeng.xpautotest");
//        boolean result = testExecutor.runTestCase(scriptName);
//        assertTrue(result);
    }

    @Test
    public void test_accessibility() throws IOException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, InstantiationException {
        TestExecutor testExecutor = new TestExecutor(context, "com.xiaopeng.xpautotest");

//        testExecutor.execute("ClickByText");
    }

    @Test
    public void test_carapi() throws Exception {
        CarApiConfig carApiConfig = CarApiConfig.getInstance();
        carApiConfig.initService(context);

        SmartActionExecutor executor = SmartActionExecutor.getInstance();
        executor.registerFactory(TechType.CAR_API, new CarApiFactory(carApiConfig));

        String[] params = new String[]{"HVAC_SEAT_VENTILATION", "1"};
        ActionContext context = new ActionContext(1L, 1, params);
        // 执行动作
        // boolean result = executor.execute("CheckBcmProp".toLowerCase(), context);
        // assertTrue(result);
    }

    @Test
    public void test_action() throws InterruptedException {
        TestExecutor testExecutor = new TestExecutor(context, "com.xiaopeng.xpautotest");

        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
//                "            CheckBCMProp \"HVAC_SEAT_VENTILATION\" 3\n" +
                "            BackToHome\n" +
                "            Delay 1\n" +
//                "            ScreenCap 111\n" +
                "            ClickByText 设置家\n" +
                "            Delay 1\n" +
                "            WaitStr 行政区划 2\n" +
                "            InputText com.xiaopeng.montecarlo:id/et_search 天河城\n" +
//                "            ClickByTextContains 公司\n" +
                "            Delay 1\n" +
                "            WaitStrContains 行政区划\n" +
//                "            Slide {67,52} {81,36} 50\n" +
//                "            Delay 1\n" +
//                "            Click {45,10}\n" +
//                "            Delay 1\n" +
//                "            AdbClick {45,10}\n" +
//                "            getprop BCM_SEAT_HEAT_LEVEL 1\n" +
//                "            Delay 1\n" +
//                "            vdt getprop BCM_SEAT_HEAT_LEVEL 1\n" +
                "            Delay 3\n";
//        boolean result = testExecutor.runTestScript(0, steps);
//        Thread.sleep(5000);
//        assertTrue(result);

//        List<TestScriptEntity> testScripts = new ArrayList<>();
//        testScripts.add(new TestScriptEntity(1L, steps));
//        Intent intent = new Intent(context, TestExecutionService.class);
//        intent.putParcelableArrayListExtra(Constant.EXTRA_TEST_SCRIPTS, (ArrayList<? extends Parcelable>) testScripts);
//        intent.putExtra(Constant.EXTRA_EXECUTION_ID, 0L);
//        context.startService(intent);
//        Thread.sleep(10000);
//        context.stopService(intent);
    }

    @Test
    public void test_event_to_can_service() throws InterruptedException {
        List<TestScriptEntity> testScripts = new ArrayList<>();
        String steps = "Precondition:\n" +
                "    Scene:\n" +
                "        Android1:\n" +
                "            # 进入车控界面\n" +
                "            startPackage com.xiaopeng.smartcontrol\n" +
                "            Delay 2\n" +
                "            ClickByText \"门窗\"\n" +
                "Procedure:\n" +
                "    Scene 1:\n" +
                "        Android1:\n" +
                "            Delay 2\n" +
                "            # 进入车窗调节界面\n" +
                "            ClickByText \"车窗调节\"\n" +
                "            Delay 2\n" +
                "            Trace can,RDCU,0,0x0218,22,2,1,0,2;can,RDCU,0,0x035D,408,12,0.02,0,2;someip,CDCU,0x2D,0x0062,0x0001,25,4,28,100\n" +
                "            # 打开全部车窗\n" +
                "            ClickByText \"全开\"\n" +
                "            Delay 10\n" +
                "            Trace can,RDCU,0,0x0218,22,2,1,0,1;can,RDCU,0,0x035D,408,12,0.02,0,2;someip,CDCU,0x2D,0x0062,0x0001,25,4,28,0\n" +
                "            # 关闭全部车窗\n" +
                "            ClickByText \"全关\"\n" +
                "            Delay 10";
        testScripts.add(new TestScriptEntity(1L, steps));
        Intent intent = new Intent(context, TestExecutionService.class);
        intent.putParcelableArrayListExtra(Constant.EXTRA_TEST_SCRIPTS, (ArrayList<? extends Parcelable>) testScripts);
        intent.putExtra(Constant.EXTRA_EXECUTION_ID, 0L);
        context.startService(intent);
        Thread.sleep(30000);
        context.stopService(intent);
    }
}
