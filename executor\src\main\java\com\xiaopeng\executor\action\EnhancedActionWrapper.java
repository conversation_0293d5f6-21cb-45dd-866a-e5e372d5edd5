package com.xiaopeng.executor.action;

import com.xiaopeng.executor.bean.ActionConfig;
import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.executor.bean.IAction;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureReasonDetail;
import com.xiaopeng.xpautotest.community.test.FailureContext;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.FailureReasonInferrer;
import com.xiaopeng.xpautotest.community.test.SimpleFailureReasonInferrer;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.accessibility.AccessibilityHelper;
import com.xiaopeng.executor.action.accessibility.UIDumpAction;
import com.xiaopeng.executor.action.accessibility.ScreenCapAction;

/**
 * 增强Action包装器
 * 为任何IAction实现统一的增强功能，包括：
 *
 * 核心功能：
 * 1. 统一的失败原因收集（ThreadLocal优先，智能推断兜底）
 * 2. 自动异常增强（为ActionException补充失败原因）
 * 3. Action执行失败自动截图支持
 * 4. 透明包装，对原Action无侵入
 *
 * 设计模式：装饰器模式
 * 职责：为Action添加失败处理、截图、异常增强等横切关注点
 */
public class EnhancedActionWrapper<T extends ActionConfig> implements IAction<T> {

    private final String TAG = "EnhancedActionWrapper";

    private final IAction<T> wrappedAction;
    private final FailureReasonInferrer failureReasonInferrer = new SimpleFailureReasonInferrer();

    public EnhancedActionWrapper(IAction<T> action) {
        this.wrappedAction = action;
    }

    @Override
    public void init(T config) {
        wrappedAction.init(config);
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        // 清除之前的失败上下文
        FailureContextHolder.clearFailureContext();
        try {
            // 调用被装饰的Action执行
            TestResult result = wrappedAction.execute(context);

            // 如果失败，进行失败处理
            if (!result.isSuccess()) {
                result = handleFailure(result, context, null);
            }
            return result;

        } catch (ActionException e) {
            // 如果ActionException还没有失败原因，补充失败原因
            if (!e.hasFailureReason()) {
                FailureReasonDetail failureReason = getFailureReason(context, e.getMessage(), e);
                // 创建一个新的ActionException，包含失败原因
                ActionException enhancedException = new ActionException(e.getMessage(), failureReason);
                enhancedException.setStackTrace(e.getStackTrace());
                throw enhancedException;
            }
            // 如果已经有失败原因，直接重新抛出
            throw e;

        } finally {
            // 清理ThreadLocal
            FailureContextHolder.clearFailureContext();
        }
    }

    /**
     * 处理失败情况，自动添加失败原因和截图
     */
    private TestResult handleFailure(TestResult result, ActionContext context, Throwable exception) {
        // 1. 只有在result还没有失败原因时才添加失败原因
        FailureReasonDetail failureReason = result.getFailureReason();
        if (failureReason == null) {
            failureReason = getFailureReason(context, result.getMessage(), exception);
            result.setFailureReason(failureReason);
        }

        // 2. 已经有截图或UIDump结果就不用再添加截图和UIDump了
        if (result.getData() != null && result.getData() instanceof TestResult.ActionArtifacts) {
            TestResult.ActionArtifacts existingArtifacts = (TestResult.ActionArtifacts) result.getData();
            boolean hasScreenshot = existingArtifacts.imgFileName != null && !existingArtifacts.imgFileName.isEmpty();
            boolean hasUIDump = existingArtifacts.uiDumpFileName != null && !existingArtifacts.uiDumpFileName.isEmpty();

            if (hasScreenshot || hasUIDump) {
                FileLogger.i(TAG, "Skip capture - already has artifacts, scriptId: "+ context.getScriptId() + "stepId: " + context.getStepId());
                return result;
            }
        }

        // 3. 如果需要截图，自动添加截图和UI转储
        if (shouldCaptureArtifacts()) {
            TestResult.ActionArtifacts artifacts = captureArtifacts(context);
            if (artifacts != null) {
                return TestResult.failure(result.getMessage(), artifacts, failureReason);
            }
        }
        // 4. 如果没有截图和UI转储结果，返回一个空的ActionArtifacts
        result.setData(new TestResult.ActionArtifacts("", ""));
        return result;
    }

    /**
     * 判断当前Action是否需要自动截图和UI转储
     *
     * Action失败时都需要截图，统一使用AccessibilityHelper服务
     * 规则：
     * 除了UIDump和ScreenCap以外的所有Action都需要自动截图
     */
    private boolean shouldCaptureArtifacts() {
        boolean isExcluded = isExcludedAction();
        if (isExcluded) {
            FileLogger.i(TAG, "Skip capture - excluded.");
        }
        return !isExcluded;
    }

    /**
     * 检查是否为不需要截图的Action
     */
    private boolean isExcludedAction() {
        try {
            return wrappedAction instanceof UIDumpAction || wrappedAction instanceof ScreenCapAction;
        } catch (Exception e) {
            FileLogger.e(TAG, "Error checking excluded action: " + e.getMessage(),e);
            return false;
        }
    }

    /**
     * 执行截图和UI转储
     * 统一使用全局AccessibilityHelper服务为所有Action提供截图功能
     */
    private TestResult.ActionArtifacts captureArtifacts(ActionContext context) {
        try {
            AccessibilityHelper globalHelper = AccessibilityHelper.getInstance();
            if (globalHelper != null) {
                TestResult.ActionArtifacts artifacts = globalHelper.screenShotandDumpUI(context.getTimeStampString());
                FileLogger.i(TAG, "Captured artifacts for scriptId:" +context.getScriptId() + " ,stepId:" + context.getStepId());
                return artifacts;
            } else {
                FileLogger.w(TAG, "AccessibilityHelper is null - cannot capture artifacts");
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to capture artifacts: " + e.getMessage(), e);
        }
        return new TestResult.ActionArtifacts("", "");
    }

    /**
     * 获取失败原因，优先使用ThreadLocal，兜底使用智能推断
     */
    private FailureReasonDetail getFailureReason(ActionContext context, String message, Throwable exception) {
        // 1. 优先使用ThreadLocal中的失败上下文
        FailureContext failureContext = FailureContextHolder.getFailureContext();
        if (failureContext != null) {
            return new FailureReasonDetail(
                    failureContext.getErrorCode(),
                    message,
                    exception
            );
        }

        // 2. 兜底使用智能推断
        String actionName = wrappedAction.getClass().getSimpleName().replace("Action", "");
        return failureReasonInferrer.inferFailureReason(actionName, message, exception);
    }

    /**
     * 获取被装饰的原始Action
     */
    public IAction<T> getWrappedAction() {
        return wrappedAction;
    }
}
