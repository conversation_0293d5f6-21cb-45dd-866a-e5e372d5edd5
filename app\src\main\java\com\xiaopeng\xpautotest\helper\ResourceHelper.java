package com.xiaopeng.xpautotest.helper;

import android.content.Context;

import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;

public class ResourceHelper {

    public static String getUnknown(Context context) {
        return context.getString(R.string.unknown);
    }

    public static String getTestTaskFailMessage(Context context, Integer enterState) {
//        if (enterState == null) {
//            return context.getString(R.string.factory_loading_error_content);
//        }
//
        String message = "";
//        switch (enterState.intValue()) {
//            case EnterState.SUCCESS:
//                message = context.getString(R.string.factory_mode_enter_ok);
//                break;
//
//            case EnterState.UNSUPPORTED:
//                message = context.getString(R.string.factory_mode_enter_fail_not_support);
//                break;
//
//            case EnterState.REPAIR_MODE:
//                message = context.getString(R.string.factory_mode_enter_fail_repair);
//                break;
//
//            case EnterState.SOC_MODE_NOT_IN_FACTORY:
//                message = context.getString(R.string.factory_mode_enter_fail_mcu);
//                break;
//
//            case EnterState.WIFI_SID_NOT_ALLOW:
//                message = context.getString(R.string.factory_mode_enter_fail_wifi);
//                break;
//
//            case EnterState.BUILT_IN_DATA_ERROR:
//                message = context.getString(R.string.factory_loading_error_content);
//                break;
//
//            default:
//                message = context.getString(R.string.factory_loading_error_content);
//                break;
//        }

        return message;
    }

    public static String getCaseExecuteStateDescription(Context context, int status) {
        switch (status) {
            case CaseExecuteState.EXECUTING:
                return context.getString(R.string.function_status_executing);

            case CaseExecuteState.SUCCESS:
                return context.getString(R.string.function_status_execute_success);

            case CaseExecuteState.FAILURE:
                return context.getString(R.string.function_status_execute_failure);

            default:
                return context.getString(R.string.function_status_pending);
        }
    }

    public static int getCaseExecuteStateIcon(int status) {
        int imageResource;
        switch (status) {
            case CaseExecuteState.EXECUTING:
                imageResource = R.mipmap.icon_status_execute;
                break;

            case CaseExecuteState.FAILURE:
            case CaseExecuteState.INTERRUPTED:
                imageResource = R.mipmap.icon_status_failure;
                break;

            case CaseExecuteState.SUCCESS:
                imageResource = R.mipmap.icon_status_success;
                break;
            case CaseExecuteState.SKIPPED:
                imageResource = R.mipmap.icon_status_skipped;
                break;
            default:
                imageResource = R.mipmap.icon_status_pending;
                break;
        }

        return imageResource;
    }

    public static int getCaseExecuteStateBackgroundColor(int status) {
        switch (status) {
            case CaseExecuteState.EXECUTING:
                return R.color.bg_status_executing;

            case CaseExecuteState.SUCCESS:
                return R.color.bg_status_execute_success;

            case CaseExecuteState.FAILURE:
            case CaseExecuteState.INTERRUPTED:
                return R.color.bg_status_execute_failure;

            default:
                return R.color.bg_status_pending;
        }
    }

}
