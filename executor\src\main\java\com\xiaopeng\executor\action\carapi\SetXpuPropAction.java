package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IXpuController;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SetXpuPropAction extends BaseAction {
    private static final String TAG = "SetXpuPropAction";
    IXpuController mIXpuController = null;

    @Override
    public void init(CarApiConfig config) {
        super.init(config);
        initControllers();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String key = (String) context.getStringParam();
        String value = (String) context.getStringParam();
        if (key == null || value == null) {
            throw new ActionException("key or value is null!", FailureCode.SI001);
        }
        boolean res = setXpuProp(key, value);
        FileLogger.i(TAG, "key: " + key + ", value: " + value + ", res: " + res);
        if (res) {
            return TestResult.success("XPU set property success: " + key);
        } else {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB005);
            return TestResult.failure("XPU set property failed: " + key);
        }
    }

    public boolean setXpuProp(String key, String value) {
        return mIXpuController.setValue(key, value);
    }

    private void initControllers() {
        mIXpuController = (IXpuController) carClientWrapper.getController(CarClientWrapper.XP_XPU_SERVICE);
    }
}
