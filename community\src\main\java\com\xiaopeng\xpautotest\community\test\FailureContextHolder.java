package com.xiaopeng.xpautotest.community.test;

public class FailureContextHolder {
    private static final ThreadLocal<FailureContext> CONTEXT = new ThreadLocal<>();
    
    public static void setFailureContext(FailureContext context) {
        CONTEXT.set(context);
    }
    
    public static FailureContext getFailureContext() {
        return CONTEXT.get();
    }
    
    public static void clearFailureContext() {
        CONTEXT.remove();
    }
    
    /**
     * 设置失败原因，使用FailureCode的标准描述
     * 这是唯一的对外接口，确保描述的一致性
     */
    protected static void setFailure(FailureCode code) {
        setFailureContext(FailureContext.create(code, code.getDescription()));
    }

    /**
     * 只有在没有失败原因时才设置失败原因
     * 避免覆盖已有的失败原因，保留第一次设置的失败原因
     */
    public static void setFailureIfNotSet(FailureCode code) {
        if (getFailureContext() == null) {
            setFailure(code);
        }
    }
}
