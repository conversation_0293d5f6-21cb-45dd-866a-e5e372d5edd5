package com.xiaopeng.xpautotest.carmanager;

import android.car.Car;
import android.car.hardware.CarEcuManager;
import android.car.hardware.CarPropertyValue;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import androidx.annotation.NonNull;

/**
 * @param <C> CarManager
 * <AUTHOR>
 */

public abstract class BaseCarController<C extends CarEcuManager, T extends IBaseCallback>
        implements IBaseCarController<T> {

    protected C mCarManager;

    protected final List<Integer> mPropertyIds;
    protected final Object mCallbackLock = new Object();
    protected final CopyOnWriteArrayList<T> mCallbacks = new CopyOnWriteArrayList<>();

    protected final ConcurrentHashMap<Integer, CarPropertyValue<?>> mCarPropertyMap = new ConcurrentHashMap<>();

    protected abstract void initCarManager(Car carClient);

    protected abstract List<Integer> getRegisterPropertyIds();

    protected abstract void disconnect();

    protected abstract void handleEventsUpdate(CarPropertyValue<?> value);

    protected BaseCarController() {
        mPropertyIds = getRegisterPropertyIds();
    }

    /**
     * Register {@link IBaseCallback} callback to car controller
     *
     * @param callback callback object
     */
    @Override
    public final void registerCallback(T callback) {
        if (callback != null) {
            mCallbacks.add(callback);
        }
    }

    /**
     * Unregister {@link IBaseCallback} callback to car controller
     *
     * @param callback callback object
     */
    @Override
    public final void unregisterCallback(T callback) {
        mCallbacks.remove(callback);
    }

    protected void handleCarEventsUpdate(@NonNull final CarPropertyValue<?> value) {
        mCarPropertyMap.put(value.getPropertyId(), value);
        handleEventsUpdate(value);
    }

    protected final int getIntProperty(int propertyId) throws Exception {
        return getValue(getCarProperty(propertyId));
    }

    protected final int[] getIntArrayProperty(int propertyId) throws Exception {
        return getIntArrayProperty(getCarProperty(propertyId));
    }

    protected final int[] getIntArrayProperty(CarPropertyValue<?> value) {
        Object[] values = getValue(value);
        int[] result = null;
        if (values != null) {
            result = new int[values.length];
            for (int i = 0; i < values.length; i++) {
                Object objValue = values[i];
                if (objValue instanceof Integer) {
                    result[i] = (Integer) objValue;
                }
            }
        }

        return result;
    }

    protected final float getFloatProperty(int propertyId) throws Exception {
        return getValue(getCarProperty(propertyId));
    }

    protected final float[] getFloatArrayProperty(int propertyId) throws Exception {
        return getFloatArrayProperty(getCarProperty(propertyId));
    }

    protected final float[] getFloatArrayProperty(CarPropertyValue<?> value) {
        Object[] values = getValue(value);
        float[] result = null;
        if (values != null) {
            result = new float[values.length];
            for (int i = 0; i < values.length; i++) {
                Object objValue = values[i];
                if (objValue instanceof Float) {
                    result[i] = (Float) objValue;
                }
            }
        }
        return result;
    }

    @NonNull
    private CarPropertyValue<?> getCarProperty(int propertyId) throws Exception {
        CarPropertyValue<?> property = mCarPropertyMap.get(propertyId);
        if (property != null) {
            return property;
        } else {
            throw new Exception("Car property not found");
        }
    }

    @SuppressWarnings("unchecked")
    protected final <E> E getValue(CarPropertyValue<?> value) {
        return (E) value.getValue();
    }

}