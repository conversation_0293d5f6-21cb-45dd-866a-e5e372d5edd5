package com.xiaopeng.xpautotest.constant;

public final class Constant {

    public static class HttpConfig {
        public static final int CONNECT_TIMEOUT = 10 * 1000;
        public static final int READ_TIMEOUT = CONNECT_TIMEOUT;
        public static final int WRITE_TIMEOUT = CONNECT_TIMEOUT;
        public static final int DNS_TIMEOUT = CONNECT_TIMEOUT;

        /**
         * logan生产环境地址
         */
        public static final String RELEASE_URL = "http://xp-autotest.x-peng.com/autotest";

        /**
         * logan预发布环境地址
         */
        public static final String DEPLOY_URL = "http://xp-autotest-uat.x-peng.com/autotest";

        /**
         * logan测试地址
         */
        public static final String DEBUG_URL = "http://xp-autotest-test.x-peng.com/autotest";

        /**
         * 内部开发地址
         */
        public static final String INTER_DEBUG_URL = "http://10.192.39.157:8888";

        /**
         * 内部正式地址
         */
        public static final String INTER_RELEASE_URL = "http://10.192.39.84:8899";
    }
}
