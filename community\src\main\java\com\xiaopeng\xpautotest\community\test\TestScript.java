package com.xiaopeng.xpautotest.community.test;

import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.io.IOException;
import java.util.ArrayList;
public class TestScript {
    private String steps;
    private long caseId;
    private long scriptId;
    private int loopTime = 1;

    private ArrayList<TestScene> setUpSceneList = new ArrayList<>();
    private ArrayList<TestScene> testSceneList = new ArrayList<>();
    private ArrayList<TestScene> tearDownSceneList = new ArrayList<>();

    public TestScript(long scriptId, String steps) throws IOException {
        this.scriptId = scriptId;
        this.steps = steps;
        String[] arrayList = steps.split("\n");
        TestScene testScene = null;
        String phaseType = "";
        for (int i = 0; i < arrayList.length; i++) {
            String line = arrayList[i].trim();
//            System.out.println(line);
            if (line.length() == 0
                    || line.startsWith("Android1")
                    || line.startsWith("CaseID")
                    || line.startsWith("CaseName")) {
                continue;
            }else if (line.startsWith("Precondition")
                    || line.startsWith("Procedure")
                    || line.startsWith("PostCondition")){
                phaseType = line.trim().substring(0, line.length() - 1);

            } else if (line.startsWith("LoopTime")) {
                String s = line.replaceFirst(".*?(\\d+)", "$1");
                loopTime = Integer.parseInt(s);
            } else if (line.startsWith("Scene")) {
                if (testScene != null) {
                    testSceneList.add(testScene);
                }
                testScene = new TestScene(scriptId, line);
                testScene.setPhaseType(phaseType);

            } else {
                if (testScene != null) {
                    testScene.addStep(i+1, line);
                }
            }
        }
        if (testScene != null) {
            testSceneList.add(testScene);
        }
        //System.out.println("TestSceneList: " + testSceneList);
        FileLogger.i("TestSceneList",testSceneList.toString(),false);

    }


    // Getters and setters
    public long getCaseId() {
        return caseId;
    }
    public long getScriptId() {
        return scriptId;
    }
    public String getSteps() {
        return steps;
    }

    public void setSteps(String steps) {
        this.steps = steps;
    }

    public int getLoopTime() {
        return loopTime;
    }

    public ArrayList<TestScene> getTestSceneList() {
        return testSceneList;
    }

    public boolean empty() {
        return testSceneList.isEmpty();
    }
}
