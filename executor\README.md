# Executor 模块

## 简介

本模块 (`executor`) 是自动化测试框架的核心执行引擎。它负责解析和执行测试用例，支持通过多种技术手段（如无障碍服务、车载API）与待测试应用或系统进行交互，并反馈测试结果。

## 核心功能

*   执行测试用例（Test Case）、测试脚本（Test Script）、测试场景（Test Scene）和测试步骤（Test Step）。
*   支持通过不同的技术类型（`TechType`）执行具体的测试动作（Action）。
*   目前支持的技术类型包括：
    *   无障碍服务 (`ACCESSIBILITY_SERVICE`)
    *   车载 API (`CAR_API`)
*   提供测试执行过程中的暂停、恢复和停止功能。
*   通过回调机制反馈测试步骤的成功或失败状态。
*   支持场景级别的重试机制。

## 主要类及其职责

1.  **`TestExecutor`**:
    *   **初始化**: 初始化底层技术服务配置（如无障碍服务、车载API），并注册实现了 `IActionFactory` 接口的动作工厂到 `SmartActionExecutor`。
    *   **执行控制**: 提供 `pauseExecution()`, `resumeExecution()`, `stopExecution()` 方法控制测试流程。
    *   **脚本执行**: 负责加载和解析测试脚本，并按顺序执行测试场景和测试步骤。内部通过调用 `SmartActionExecutor` 来执行每个 `TestStep` 对应的 `IAction`。
        *   `runTestCase(String scriptName)`: 从文件加载测试用例并执行。
        *   `runTestScript(TestScript testScript)`: 执行 `TestScript` 对象。
    *   **结果回调**: 通过 `IExecuteHandler` 接口，在测试步骤成功或失败时进行回调。

2.  **`SmartActionExecutor`**:
    *   **单例模式**: 确保全局只有一个执行器实例。
    *   **工厂注册**: 通过 `registerFactory(TechType type, IActionFactory factory)` 方法，管理不同 `TechType` 对应的 `IActionFactory` 实现。
    *   **动作执行**:
        *   `execute(String actionName, TechType techType, ActionContext context)`: 根据指定的技术类型，从对应的 `IActionFactory` 获取 `IAction` 实例并执行。
        *   `execute(String actionName, ActionContext context)`: 自动选择技术实现。它会按照预设的优先级（当前为 `CAR_API` > `ACCESSIBILITY_SERVICE`），遍历已注册的 `IActionFactory`，使用第一个声明支持该 `actionName` (`containsAction` 方法) 的工厂来获取 `IAction` 实例并执行。

3.  **`IAction<T extends ActionConfig>` (接口)**:
    *   定义了单个可执行动作的契约。是所有具体测试动作（如点击、滑动、设置文本等）的父接口。
    *   `void init(T config)`: 用于初始化动作，接收一个特定类型的 `ActionConfig` 子类对象，允许动作在执行前进行配置。
    *   `TestResult execute(ActionContext context) throws ActionException`: 核心执行方法，接收 `ActionContext`（包含执行参数和上下文），返回 `TestResult`，并可能抛出 `ActionException`。

4.  **`IActionFactory` (接口)**:
    *   定义了创建和管理 `IAction` 实例的工厂的契约。每种 `TechType` (如无障碍服务、车载API) 通常会有一个对应的实现类。
    *   `IAction<?> getAction(String actionName)`: 根据动作名称获取一个 `IAction` 实例。这是 `SmartActionExecutor` 获取具体动作的主要方式。
    *   `boolean containsAction(String actionName)`: 检查该工厂是否能够创建或提供指定名称的动作。
    *   `boolean supports(TechType techType)`: 检查该工厂是否支持特定的技术类型。
    *   `IAction<?> createAction(String actionType)`: (备用或旧方法) 根据类型创建动作。
    *   **具体实现示例**:
        *   `AccessibilityFactory`: 位于 `com.xiaopeng.executor.action.accessibility` 包，实现了针对无障碍服务的动作工厂。它在内部通过 `Map<String, Supplier<IAction<AccessibilityConfig>>>` 注册并管理所有无障碍动作（如 `ClickAction`, `InputTextAction` 等）。它持有一个 `AccessibilityConfig` 实例，并在创建动作时将其注入，同时缓存已创建的动作实例。
        *   `CarApiFactory`: 位于 `com.xiaopeng.executor.action.carapi` 包，实现了针对车载API的动作工厂。其结构和逻辑与 `AccessibilityFactory` 高度相似，但管理的是车载API相关的动作（如 `CheckBcmPropAction`）和配置 (`CarApiConfig`)。

5.  **`BaseAction` (基类模板)**:
    *   此类并非单一存在，而是在各个技术类型包（如 `accessibility` 和 `carapi`）下有其特定版本。
    *   **职责**: 作为具体 `IAction` 实现的通用基类，简化开发。
    *   `void init(SpecificConfig config)`: 接收特定技术的配置对象（如 `AccessibilityConfig` 或 `CarApiConfig`），并从中提取和保存核心服务/客户端的引用（如 `AccessibilityHelper` 或 `CarClientWrapper`），供子类动作使用。
    *   `TestResult execute(ActionContext context)`: 通常提供一个默认的失败实现，强制子类重写此方法以实现具体动作逻辑。
    *   **示例**:
        *   `com.xiaopeng.executor.action.accessibility.BaseAction`: 保存 `AccessibilityHelper`。
        *   `com.xiaopeng.executor.action.carapi.BaseAction`: 保存 `CarClientWrapper`。

## 关键设计模式

*   **工厂模式 (Factory Pattern)**: `SmartActionExecutor` 依赖 `IActionFactory` 的具体实现来创建和获取 `IAction` 实例。这使得系统能够轻松扩展，支持新的动作和技术类型，而无需修改 `SmartActionExecutor` 的核心逻辑。每个 `IActionFactory` 负责其对应 `TechType` 下的 `IAction` 对象的创建。
*   **单例模式 (Singleton Pattern)**: `SmartActionExecutor` 采用单例模式，确保全局只有一个动作执行的协调中心。
*   **策略模式 (Strategy Pattern - 隐含)**: `TechType` 和不同的 `IActionFactory` 实现共同构成了策略选择。`SmartActionExecutor` 根据 `TechType` 或动作名称选择合适的工厂（策略）来执行动作。
*   **命令模式 (Command Pattern)**: `IAction` 接口及其实现类是命令模式的体现。每个 `IAction` 实现封装了一个具体的操作请求（如"点击按钮X"），将请求的发送者 (`SmartActionExecutor`) 与接收者（执行具体UI操作或API调用的逻辑）解耦。`ActionContext` 则携带了命令执行所需的参数。

## 模块依赖

*   `com.xiaopeng.xpautotest.community.test` (包含 `TestResult`, `TestScript`, `TestScene`, `TestStep` 等核心测试模型)
*   `com.xiaopeng.xpautotest.community.utils` (包含 `FileLogger`, `FileUtils` 等工具类)
*   `executor/src/main/java/com/xiaopeng/executor/bean` (核心接口与数据模型所在地):
    *   `IAction.java`: 定义了测试动作的执行契约。
    *   `IActionFactory.java`: 定义了动作工厂的创建契约。
    *   `ActionContext.java`: **动作执行上下文**。
        *   **职责**: 封装单个动作执行时所需的运行时参数和元数据。
        *   **内容**: 包含 `scriptId` (脚本ID), `stepId` (步骤ID), `timeStampString` (时间戳，用于日志或截图文件名), 以及动作参数。
        *   **参数传递**: 支持两种参数形式：
            1.  `String[] params` (通过构造函数传入): 参数存储在内部的 `Queue<String>` 中，动作实现通过 `getStringParam()` / `getIntParam()` (消耗队列) 或 `getAllParams()` (不消耗队列，返回拼接字符串) 按顺序获取。这是 `TestExecutor` 目前主要使用的方式。
            2.  `Map<String, Object> params` (通过构造函数传入): 允许传递命名的、类型更丰富的参数，通过 `getParam(String key)` 或类型转换的 `getStringParam(String key)` / `getIntParam(String key)` 获取。此方式在当前流程中较少使用，但提供了扩展性。
    *   `TechType.java`: **技术类型枚举**。
        *   **职责**: 定义了执行动作可以依赖的底层技术栈。
        *   **值**: `ACCESSIBILITY_SERVICE` (无障碍服务), `CAR_API` (车载API), `UI_AUTOMATOR` (UI Automator，目前尚无对应工厂和动作实现，但表明了扩展方向)。
        *   **用途**: 被 `SmartActionExecutor` 用于注册和选择 `IActionFactory`，也被工厂自身用于声明其支持的技术类型。
    *   `ActionException.java`: **自定义异常类**。
        *   **职责**: 继承自 `Exception`，用于表示在动作执行过程中发生的特定错误。
        *   **用途**: 由 `IAction.execute()` 抛出，并在具体动作实现中因参数错误或底层操作失败时实例化和抛出。`TestExecutor` 会捕获此异常。
    *   `ActionConfig.java`: **动作配置基类 (抽象类)**。
        *   **职责**: 作为特定技术类型配置类 (`AccessibilityConfig`, `CarApiConfig`) 的父类。
        *   **内容**: 目前仅包含一个未被有效使用的私有 `timeoutMs` 字段。其主要作用是通过泛型 `IAction<T extends ActionConfig>` 约束具体动作的 `init` 方法的配置参数类型。
*   `executor/src/main/java/com/xiaopeng/executor/action` (具体动作和工厂的实现):
    *   **`accessibility` 子包**:
        *   `AccessibilityFactory.java`: `IActionFactory` 的无障碍服务实现。
        *   `AccessibilityConfig.java` (extends `ActionConfig`): **无障碍服务配置类 (单例)**。
            *   **职责**: 初始化并提供对 `AccessibilityHelper` 的访问。
            *   **核心逻辑**: `initService(Context context, String packageName)` 方法获取 `AccessibilityHelper` 实例，并检查/尝试启用 `AutoTestAccessibilityService`。`getService()` 返回 `AccessibilityHelper`。
            *   **注入路径**: `TestExecutor` -> `AccessibilityFactory` 构造 -> `BaseAction.init()` -> 具体无障碍动作使用。
        *   `BaseAction.java`: 无障碍服务动作的基类，从 `AccessibilityConfig` 获取并持有 `AccessibilityHelper` 实例。
        *   众多具体动作类 (如 `ClickAction.java` - 点击坐标, `ClickByIdAction.java` - 按ID点击, `InputTextAction.java` - 输入文本, `SwipeAction.java` - 滑动, `WaitIdAction.java` - 等待元素出现, `StartPackageAction.java` - 启动应用等)，均继承自 `BaseAction` 并实现特定的UI交互。这些动作展示了通过无障碍服务进行点击、文本输入、滑动、等待特定UI元素、启动应用等多样化的操作能力。
    *   **`carapi` 子包**:
        *   `CarApiFactory.java`: `IActionFactory` 的车载API实现。
        *   `CarApiConfig.java` (extends `ActionConfig`): **车载API配置类 (单例)**。
            *   **职责**: 初始化并提供对 `CarClientWrapper` 的访问，并预初始化部分车载控制器。
            *   **核心逻辑**: `initService(Context context)` 方法获取 `CarClientWrapper` 实例，调用 `connectToCar()` 连接车载服务，并调用 `initControllers()` (当前主要初始化 `IBcmController`)。`getService()` 返回 `CarClientWrapper`。
            *   **注入路径**: `TestExecutor` -> `CarApiFactory` 构造 -> `BaseAction.init()` -> 具体车载API动作使用。
        *   `BaseAction.java`: 车载API动作的基类，从 `CarApiConfig` 获取并持有 `CarClientWrapper` 实例。
        *   具体动作类 (如 `CheckBcmPropAction.java`)，继承自 `BaseAction` 并与车载硬件或服务交互。

## 执行流程概述

1.  外部调用方通过 `TestExecutor` 的 `runTestCase` 或 `runTestScript` 方法发起测试。
2.  `TestExecutor` 解析脚本为 `TestScene` 和 `TestStep`。
3.  对于每个 `TestStep`，`TestExecutor` 构建 `ActionContext`（包含参数等）。
4.  `TestExecutor` 调用 `SmartActionExecutor.execute(actionName, context)`。
5.  `SmartActionExecutor` 根据 `actionName`（和可选的 `TechType`）：
    *   如果是自动选择模式：遍历已注册的 `IActionFactory`（如 `AccessibilityFactory`, `CarApiFactory`），调用其 `containsAction(actionName)` 判断是否支持该动作。
    *   找到合适的 `IActionFactory` 后，调用其 `getAction(actionName)` 方法。此方法内部可能会先调用 `createAction(actionName)`（如果动作未被缓存），在 `createAction` 中通过 `Supplier.get()` 创建具体动作实例（如 `new ClickAction()`），并调用该动作的 `init(config)` 方法注入特定技术的配置（如 `AccessibilityConfig`）。
6.  获取到的 `IAction` 实例（如 `ClickAction` 对象，它继承自对应技术类型的 `BaseAction` 并实现了 `IAction` 接口）的 `execute(context)` 方法被调用。
7.  `IAction` 实现类（如 `ClickAction`）执行具体的测试操作。它会使用在 `BaseAction` 的 `init` 方法中初始化的服务客户端（如 `AccessibilityHelper` 或 `CarClientWrapper`），并结合从 `ActionContext` 获取的参数，来完成任务（例如通过无障碍服务模拟点击坐标 (x,y)，或调用车载API检查BCM属性）。
8.  执行结果 (`TestResult`) 返回给 `SmartActionExecutor`，再返回给 `TestExecutor`。
9.  `TestExecutor` 根据 `TestResult` 进行日志记录、通过 `IExecuteHandler` 回调，并决定后续执行逻辑。

## 配置与上下文传递流程

1.  **应用启动/测试初始化阶段 (`TestExecutor` 构造)**:
    *   `AccessibilityConfig.getInstance().initService(context, packageName)` 被调用：
        *   获取 `AccessibilityHelper` 单例。
        *   检查 `AutoTestAccessibilityService` (在 `com.xiaopeng.xpautotest.accessibility` 包内) 是否已启用，如果未启用，则尝试通过 `AccessibilityHelper` 修改系统设置来启用它。
    *   `CarApiConfig.getInstance().initService(context)` 被调用：
        *   获取 `CarClientWrapper` 单例。
        *   调用 `CarClientWrapper.connectToCar(context)` 异步连接到 Android Car 服务。
        *   `CarApiConfig` 内部还会尝试预初始化一个 `IBcmController`。
2.  **工厂创建与注册 (`TestExecutor` 构造)**:
    *   创建 `AccessibilityFactory` 实例时，将 `AccessibilityConfig.getInstance()` 传入其构造函数。工厂保存此配置实例。
    *   创建 `CarApiFactory` 实例时，将 `CarApiConfig.getInstance()` 传入其构造函数。工厂保存此配置实例。
    *   这些工厂随后被注册到 `SmartActionExecutor` 中，与对应的 `TechType`关联。
3.  **动作执行阶段 (`TestExecutor.runTestStep`)**:
    *   `TestExecutor` 根据测试脚本中的步骤信息，创建一个 `ActionContext` 实例。此实例包含 `scriptId`, `stepId`, `timeStampString` 以及从脚本解析出的动作参数（一个 `String[]` 数组，被放入 `ActionContext` 的内部参数队列）。
    *   `SmartActionExecutor.execute(actionName, context)` 被调用。
4.  **动作获取与初始化 (`IActionFactory` 实现)**:
    *   `SmartActionExecutor` 根据 `actionName` 和 `TechType` (或自动选择) 选定一个工厂 (如 `AccessibilityFactory`)。
    *   调用工厂的 `getAction(actionName)` 方法。
    *   工厂内部（通常在 `createAction` 方法中）实例化具体的 `IAction` 实现类 (如 `ClickAction`)。
    *   关键步骤：工厂调用该新创建动作的 `init(config)` 方法，并将自身持有的配置对象 (如 `AccessibilityConfig` 实例) 传递给动作。
    *   在动作的 `init` 方法中 (通常是在其 `BaseAction` 父类中实现)，动作会从传入的配置对象中获取核心的服务/帮助类引用 (如调用 `AccessibilityConfig.getService()` 得到 `AccessibilityHelper`) 并保存为成员变量。
5.  **动作执行 (`IAction` 实现)**:
    *   具体动作的 `execute(ActionContext context)` 方法被调用。
    *   动作实现类从传入的 `ActionContext` 对象中获取其操作所需的具体参数 (如点击坐标、输入文本等)，通常是通过 `context.getStringParam()` 或 `context.getIntParam()` 等方法从参数队列中按顺序提取。
    *   利用在 `init` 阶段获得的服务/帮助类引用 (如 `this.service` 指向 `AccessibilityHelper`) 和从 `ActionContext` 获得的参数，执行具体的操作。
    *   返回一个 `TestResult` 对象。如果发生特定于动作的错误，则抛出 `ActionException`。

这个流程确保了：
*   底层服务 (`AccessibilityHelper`, `CarClientWrapper`) 在测试开始时被正确初始化和配置。
*   配置信息 (主要是服务/帮助类的引用) 通过依赖注入的方式，从 `Config` 单例 -> `Factory` -> `Action` 的 `init` 方法，最终到达具体动作实现。
*   每个动作执行时，通过 `ActionContext` 对象接收其独特的运行时参数和元数据。

## 底层能力提供者 (Helpers/Wrappers)

为了执行具体的操作，`Action` 实现依赖于一些底层的帮助类或包装类，这些类封装了与系统服务（如无障碍服务、车载硬件服务）的直接交互。它们虽然位于 `executor` 模块的源码树下（`executor/src/main/java/com/xiaopeng/xpautotest/`），但其包名暗示了它们可能是为了更广泛的 `xpautotest` 项目而设计的通用组件。

1.  **`com.xiaopeng.xpautotest.accessibility.AccessibilityHelper`**:
    *   **角色**: 作为无障碍服务交互的中心API。它是一个单例，需要 `Context` 初始化。
    *   **核心机制**: 通过 `bindService(BaseAccessibilityService service)` 方法与一个实际运行的 `AccessibilityService`（如 `AutoTestAccessibilityService`，也位于此包下）实例绑定。实际的无障碍操作（如查找节点、执行点击/滑动等手势）最终由这个绑定的服务执行。
    *   **主要功能**:
        *   **服务管理**: 检查无障碍服务是否启用 (`isServiceEnabled`)，通过修改系统设置来启用服务 (`enableAccessibilityService`)。
        *   **节点查找**: 提供多种方式查找界面元素 (`AccessibilityNodeInfo`)，如按ID (`findNodeById`)、文本 (`findNodeByText`, `findNodeByTextContains`)、类名 (`findNodeByClassName`)。
        *   **UI操作**: 执行点击 (`click` - 按坐标, `clickNode` - 按节点, `clickNodeById`, `clickNodeByText`), 文本输入 (`inputText`)，滑动/轻触 (`swipe`, `tap`)，列表滚动 (`scrollListView`)。坐标通常是基于屏幕百分比。
        *   **同步等待**: 等待特定节点出现，支持超时 (`waitForNodeById`, `waitForNodeByText`)。
        *   **应用/Activity控制**: 获取当前Activity (`getCurrentActivity`)，启动包 (`startPackage`)，启动Activity (`startActivity`)，返回桌面 (`goToHome`)。
        *   **诊断与调试**: 截屏和导出UI层级 (`screenShotandDumpUI`)，供失败时记录。
        *   **`NodeOperator` 内部类**: 提供链式调用的流畅API来操作 `AccessibilityNodeInfo`。
    *   **依赖**: 依赖于一个正在运行的 `BaseAccessibilityService` 实现来执行底层操作。

    *   **`AutoTestAccessibilityService` (extends `BaseAccessibilityService`)**: 
        *   这是在 `AndroidManifest.xml` 中声明的实际无障碍服务组件。
        *   **关键职责**: 在 `onServiceConnected()` 回调中，它获取 `AccessibilityHelper` 的单例，并调用 `AccessibilityHelper.getInstance(...).bindService(this)`，将自身（活动的无障碍服务实例）与 `AccessibilityHelper` 关联起来。这一步是连接 `Helper` API 与实际服务能力的关键。
        *   它继承了 `BaseAccessibilityService` 的所有底层操作能力。

    *   **`BaseAccessibilityService` (extends `android.accessibilityservice.AccessibilityService`)**:
        *   **角色**: 封装了与 Android 无障碍框架直接交互的底层逻辑，作为 `AccessibilityHelper` 实际操作的执行者。
        *   **主要功能**:
            *   **手势执行**: 实现 `performClick` (坐标点击), `performSwipe` (滑动) 等手势。针对不同 Android 版本和多显示器场景，它会智能选择使用 `dispatchGesture()` API (Android N+)，或回退到执行 `input swipe` 等shell命令。
            *   **节点查找**: 提供 `findViewByID`, `findViewByText`, `findViewByTextContains` 等方法，这些方法遍历窗口中的节点树来定位元素。
            *   **全局动作**: 执行 `GLOBAL_ACTION_HOME`, `GLOBAL_ACTION_BACK` 等。
            *   **截图与UI结构导出**: 包含 `screenshot` (使用 `takeScreenshot` API on R+) 和 `performUiDump` (使用 `UIHierarchyDumper` 类将节点树序列化为XML) 的逻辑，用于测试诊断。
            *   **事件处理**: `onAccessibilityEvent` 方法（主要由 `AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED` 和 `TYPE_WINDOW_CONTENT_CHANGED`驱动）用于更新当前活动 Activity 的名称 (`currentActivityName`)。
            *   管理一个内部的 `Context` 实例 (`mContext`)，由 `AccessibilityHelper` 在绑定时注入。

2.  **`com.xiaopeng.xpautotest.carmanager.CarClientWrapper`**:
    *   **角色**: 作为与Android车载硬件服务交互的中心API和管理器。它是一个单例。
    *   **核心机制**: 使用标准的 `android.car.Car` API来连接到车辆的系统服务。它管理到 `Car` 服务的连接 (`connectToCar`, `disconnect`)，并在连接成功后初始化各种车载模块的控制器。
    *   **主要功能**:
        *   **服务连接管理**: 异步连接到车载服务，并处理连接/断开事件。
        *   **控制器工厂与缓存**:
            *   定义了大量代表不同车载子系统（如BCM, VCU, MCU）的服务名称常量 (e.g., `XP_BCM_SERVICE`)。
            *   通过 `getController(String serviceName)` 方法，按服务名称获取对应的控制器实例（如 `IBcmController`）。这些控制器通常继承自 `BaseCarController`。
            *   `createCarController` 方法根据服务名实例化具体的控制器 (e.g., `new VcuController()`, `BcmControllerFactory.createCarController()`)。
            *   控制器实例会被缓存，以便复用。
        *   **同步**: 等待车载服务连接成功 (`isCarServiceConnected` 使用 `wait/notifyAll` 机制)。
    *   **依赖**: 具体的功能实现（如读写车辆属性）委托给由它创建和管理的各个 `BaseCarController` 子类（如 `BcmController`, `VcuController`）。这些控制器内部再使用 `android.car.hardware.*` 下的特定管理器（如 `CarBcmManager`, `CarVcuManager`）与硬件通信。


## 待分析和完善

*   深入分析 `com.xiaopeng.xpautotest.carmanager` 包下的 `BaseCarController` 及其具体实现（如 `BcmController`），以完全理解车载API能力的实现细节。
*   完善错误处理和异常传递机制。
*   增加更多技术类型的支持（这会涉及到创建新的子包、新的工厂、配置类和动作实现）。
*   审视 `ActionConfig` 基类中 `timeoutMs` 字段的用途，如果需要通用超时，应提供访问机制或在子类中使用。
*   考虑 `ActionContext` 中基于 `Map` 的参数传递方式是否可以更广泛地利用，以提高参数传递的灵活性和可读性。 