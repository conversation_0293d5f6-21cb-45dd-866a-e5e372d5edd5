package com.xiaopeng.xpautotest.helper.log;

import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessContext;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessorResult;
import com.xiaopeng.xpautotest.helper.log.processor.CduLogProcessor;
import com.xiaopeng.xpautotest.helper.log.processor.FileLogProcessor;
import com.xiaopeng.xpautotest.helper.log.processor.ILogProcessor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志处理管理器
 * 统一管理不同类型的日志处理器，提供统一的处理入口
 */
public class LogProcessorManager {
    private static final String TAG = "LogProcessorManager";
    
    private final Map<LogProcessorResult.LogType, ILogProcessor> processors;

    public LogProcessorManager() {
        processors = new HashMap<>();
        initializeProcessors();
    }

    /**
     * 创建默认配置的处理器管理器
     * @return 包含所有默认处理器的管理器实例
     */
    public static LogProcessorManager createDefault() {
        return new LogProcessorManager();
    }

    /**
     * 初始化所有日志处理器
     */
    private void initializeProcessors() {
        // 注册FileLogger日志处理器
        registerProcessor(new FileLogProcessor());
        // 注册CDU日志处理器
        registerProcessor(new CduLogProcessor());
    }

    /**
     * 注册日志处理器
     * @param processor 日志处理器
     */
    public void registerProcessor(ILogProcessor processor) {
        if (processor != null) {
            processors.put(processor.getSupportedLogType(), processor);
        }
    }

    /**
     * 处理所有类型的日志
     * @param context 日志处理上下文
     * @return 所有日志处理结果的列表，永不返回null
     */
    public List<LogProcessorResult> processAllLogs(LogProcessContext context) {
        if (context == null) {
            FileLogger.e(TAG, "LogProcessContext is null, returning empty results");
            return new ArrayList<>();
        }

        // 预分配容量，避免ArrayList扩容
        List<LogProcessorResult> results = new ArrayList<>(processors.size());

        for (ILogProcessor processor : processors.values()) {
            LogProcessorResult result = processWithProcessor(processor, context);
            if (result != null) {
                results.add(result);
            }
        }

        return results;
    }

    /**
     * 使用单个处理器处理日志，完全异常隔离
     * @param processor 日志处理器
     * @param context 处理上下文
     * @return 处理结果，异常时返回失败结果，严重异常时返回null
     */
    private LogProcessorResult processWithProcessor(ILogProcessor processor, LogProcessContext context) {
        if (processor == null) {
            FileLogger.e(TAG, "Processor is null, skipping");
            return null;
        }

        LogProcessorResult.LogType logType = null;
        long startTime = System.currentTimeMillis();

        try {
            logType = processor.getSupportedLogType();
            // 检查是否应该处理
            if (!processor.shouldProcess(context)) {
                FileLogger.d(TAG, "Skipping " + logType + " processing (conditions not met)");
                return null;
            }

            FileLogger.d(TAG, "Processing " + logType + " logs");
            LogProcessorResult result = processor.process(context);

            long processingTime = System.currentTimeMillis() - startTime;

            if (result == null) {
                String errorMsg = "Processor returned null result";
                FileLogger.w(TAG, logType + " " + errorMsg + " (took " + processingTime + "ms)");
                return LogProcessorResult.failure(logType, errorMsg).build();
            }

            if (result.isSuccess()) {
                FileLogger.i(TAG, logType + " processed successfully in " + processingTime + "ms: " + result.getOssPath());
            } else {
                FileLogger.w(TAG, logType + " processing failed in " + processingTime + "ms: " + result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;

            // 获取日志类型失败时的兜底处理
            if (logType == null) {
                try {
                    logType = processor.getSupportedLogType();
                } catch (Exception ex) {
                    FileLogger.e(TAG, "Failed to get processor log type after " + processingTime + "ms", ex);
                    return null; // 无法确定类型，跳过此处理器
                }
            }

            String errorMsg = "Unexpected error processing " + logType + " after " + processingTime + "ms: " + e.getMessage();
            FileLogger.e(TAG, errorMsg, e);
            return LogProcessorResult.failure(logType, errorMsg).build();
        }
    }

    /**
     * 处理特定类型的日志
     * @param logType 日志类型
     * @param context 日志处理上下文
     * @return 日志处理结果，如果处理器不存在或不应处理则返回null
     */
    public LogProcessorResult processLog(LogProcessorResult.LogType logType, LogProcessContext context) {
        ILogProcessor processor = processors.get(logType);
        if (processor == null) {
            String errorMsg = "No processor found for log type: " + logType;
            FileLogger.w(TAG, errorMsg);
            return LogProcessorResult.failure(logType, errorMsg).build();
        }

        if (!processor.shouldProcess(context)) {
            FileLogger.d(TAG, "Skipping " + logType + " processing (conditions not met)");
            return null;
        }

        try {
            FileLogger.d(TAG, "Processing " + logType + " logs");
            LogProcessorResult result = processor.process(context);

            if (result.isSuccess()) {
                FileLogger.i(TAG, logType + " processed successfully: " + result.getOssPath());
            } else {
                FileLogger.w(TAG, logType + " processing failed: " + result.getErrorMessage());
            }

            return result;
        } catch (Exception e) {
            String errorMsg = "Unexpected error processing " + logType + ": " + e.getMessage();
            FileLogger.e(TAG, errorMsg, e);
            return LogProcessorResult.failure(logType, errorMsg).build();
        }
    }


}
