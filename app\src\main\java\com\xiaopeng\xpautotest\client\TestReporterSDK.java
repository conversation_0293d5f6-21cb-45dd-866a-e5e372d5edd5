package com.xiaopeng.xpautotest.client;

import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.manager.ConnectionChangeReceiver;
import com.xiaopeng.xpautotest.bean.ReportableEntity;

public class TestReporterSDK {
    private static TestReporterSDK instance;
    private ReportQueueManager queueManager;

    public TestReporterSDK(ReportQueueManager queueManager) {
        this.queueManager = queueManager;
    }

    public static void init() {
        instance = new TestReporterSDK(ReportQueueManager.getInstance());

        // 启动时加载未上报数据
        if (ConnectionChangeReceiver.getInstance().isWifiConnected()) {
            instance.queueManager.loadFromDisk();
        }
    }

    public static void reportStep(ReportableEntity entity) {
        if (entity.getTaskExecutionId() == 0) {
            FileLogger.e("TestReporterSDK", "executionId is 0, will not report!");
            return;
        }
        if (instance == null) {
            instance = new TestReporterSDK(ReportQueueManager.getInstance());
        }
        instance.queueManager.reportEntity(entity);
    }
}
