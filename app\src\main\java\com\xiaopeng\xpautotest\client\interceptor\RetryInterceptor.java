package com.xiaopeng.xpautotest.client.interceptor;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class RetryInterceptor implements Interceptor {
    private final int maxRetries;
    private int retryCount = 0;

    public RetryInterceptor(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = null;
        IOException exception = null;

        // 尝试请求 + 重试
        while (retryCount <= maxRetries) {
            try {
                response = chain.proceed(request);
                if (response.isSuccessful()) {
                    return response;
                } else if (shouldRetry(response.code())) {
                    closeResponse(response);
                    waitForRetry();
                    retryCount++;
                } else {
                    return response; // 不可恢复错误
                }
            } catch (IOException e) {
                exception = e;
                waitForRetry();
                retryCount++;
            }
        }

//        if (exception != null) throw exception;
        if (response == null) {
            // Reset retry count after max retries
            retryCount = 0;
            throw new IOException("Failed to execute request after " + maxRetries + " retries!");
        }
        return response;
    }

    public boolean shouldRetry(int statusCode) {
        // 扩展 HTTP 状态码判断
        return statusCode == 429 || // 限流
                statusCode == 503 || // 服务不可用
                (statusCode >= 500 && statusCode < 600); // 所有服务端错误
    }

    private void waitForRetry() {
        try {
            Thread.sleep((long) Math.pow(2, retryCount) * 1000); // 指数退避
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void closeResponse(Response response) {
        if (response != null && response.body() != null) {
            response.close();
        }
    }
}
