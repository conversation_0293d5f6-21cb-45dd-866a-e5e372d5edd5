apply plugin: 'com.android.application'
apply plugin: "xiaopeng-buildhelper"


// 默认打包发布的版本，如果是本地打包发布，只需要修改此变量即可
final String BUILD_VERSION = "1.0.0-SNAPSHOT"
project.checkBuildConfig()
project.ext {
    // 是否允许发布到maven仓库的 标志，默认值为true，以打包平台传过来的配置为优先
    publishToMaven = project.getPublishFlag(true)
    // 发布到maven的组名称，APK为com.xiaopeng.apk，aar为com.xiaopeng.aar
    groupId = "com.xiaopeng.apk"
    // 发布到maven的项目名称，替换为自己的业务名称
    artifactId = "xpAutoTest"
    publishVersion = project.getBuildVersion(BUILD_VERSION)
}

def getConfigProperty(String property) {
    def configFile = file('../../personal_config.properties')
    if (configFile.canRead()) {
        Properties configProps = new Properties()
        configProps.load(new FileInputStream(configFile))
        def value = configProps[property]
        println value
        return value
    } else {
        throw new GradleException("Could not find personal_config.properties!")
    }
}

android {
    namespace 'com.xiaopeng.xpautotest'
    compileSdkVersion rootProject.ext.versions.compileSdkVersion

    defaultConfig {
        applicationId "com.xiaopeng.xpautotest"
        minSdkVersion rootProject.ext.versions.minSdkVersion
        targetSdkVersion rootProject.ext.versions.targetSdkVersion
        versionCode project.getVersionCode(9999)  // 9999为默认值
        versionName project.getBuildVersion(BUILD_VERSION)    // BUILD_VERSION为默认值
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        ndk {
            // Specifies the ABI configurations of your native
            // libraries Gradle should build and package with your APK.
            abiFilters 'arm64-v8a'
        }
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++17"
                arguments "-DANDROID_STL=c++_shared" // 使用动态运行时
            }
        }
    }
    signingConfigs {
        debug {
            try {
                storeFile file(getConfigProperty('STORE_FILE'))
                storePassword KEYSTORE_PASSWORD
                keyAlias KEY_ALIAS
                keyPassword KEY_PASSWORD
            } catch (ex) {
                throw new InvalidUserDataException("You should define KEYSTORE_PASSWORD and KEY_PASSWORD in gradle.properties.")
            }
        }
        release {
            try {
                storeFile file(getConfigProperty('STORE_FILE'))
                storePassword KEYSTORE_PASSWORD
                keyAlias KEY_ALIAS
                keyPassword KEY_PASSWORD
            } catch (ex) {
                throw new InvalidUserDataException("You should define KEYSTORE_PASSWORD and KEY_PASSWORD in gradle.properties.")
            }
//            keyAlias 'platform_xiaopeng'
//            keyPassword 'xiaopeng0109'
//            storeFile file('D:/android/xiaopeng/xiaopeng_test.keystore')
//            storePassword 'xiaopeng0109'
        }
    }
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            buildConfigField 'String', 'ENV_TYPE', project.hasProperty('ENV_TYPE') ? project.getProperty('ENV_TYPE') : '"prod"'
        }
        debug {
            signingConfig signingConfigs.debug
            buildConfigField 'String', 'ENV_TYPE', project.hasProperty('ENV_TYPE') ? project.getProperty('ENV_TYPE') : 'test'
        }
    }

    buildFeatures {
        buildConfig true
    }

    packagingOptions {
        pickFirst 'META-INF/INDEX.LIST'
        pickFirst 'META-INF/DEPENDENCIES'
        pickFirst 'META-INF/io.netty.versions.properties'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        exclude "lib/arm64-v8a/libc++_shared.so"
    }
}

//dependencies {
//    xuimanagerLib 'com.xiaopeng.jar:xuimanager:2.0.0-SNAPSHOT'
//}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
//    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
//    implementation 'androidx.appcompat:appcompat:1.7.0'
//    implementation 'com.google.android.material:material:1.12.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation 'javax.servlet:servlet-api:2.5'
    implementation project(':community')
    implementation project(':executor')
    implementation project(':trace')
    compileOnly 'com.xiaopeng.jar:xuimanager:2.0.0-SNAPSHOT'
    compileOnly 'com.xiaopeng.jar:carapi:8155_LA_xpdev-SNAPSHOT'
    implementation 'com.xiaopeng.lib:lib_xpui:5.6.3'
    implementation('com.xiaopeng.lib:lib_utils:1.7.5.9'){
        changing = true
        exclude group: 'org.apache.commons', module: 'commons-compress'
    }
    implementation "com.xiaopeng.lib:lib_bughunter:2.3.5"
    implementation "com.xiaopeng.lib:lib_config:1.2.7.3"
    implementation "com.xiaopeng.lib.framework:datalogmodule:2.0.1.5"
    implementation "com.xiaopeng.lib:lib_http:1.5.4"
    implementation "com.xiaopeng.lib.framework:netchannelmodule:1.9.0.8"
    implementation "com.xiaopeng.lib:apirouterclient:2.0.9.15-napa"

    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.18'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava2:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'software.amazon.awssdk:s3:2.22.13'
    implementation 'software.amazon.awssdk:url-connection-client:2.22.13'
    implementation 'com.aliyun.dpa:oss-android-sdk:2.9.4'
    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
}
