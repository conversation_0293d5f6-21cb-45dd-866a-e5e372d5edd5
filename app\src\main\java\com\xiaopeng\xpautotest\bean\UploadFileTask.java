package com.xiaopeng.xpautotest.bean;

public class UploadFileTask {
    String absoluteFilePath;
    long creationTimestamp;
    FileType filetype;
    private int retryCount;

    public enum FileType {
        FILE_IMAGE,
        FILE_LOG,
        FILE_ZIP,
        FILE_XML,
        FILE_CDU_LOG,
        FILE_UNKNOWN
    }

    public UploadFileTask(String absoluteFilePath, long creationTimestamp, FileType type, int initialRetryCount) {
        this.absoluteFilePath = absoluteFilePath;
        this.creationTimestamp = creationTimestamp;
        this.filetype = type;
        this.retryCount = initialRetryCount;
    }

    public UploadFileTask(String absoluteFilePath, long creationTimestamp, FileType type) {
        this(absoluteFilePath, creationTimestamp, type, 0);
    }

    public UploadFileTask(String absoluteFilePath, long creationTimestamp, int initialRetryCount) {
        this.absoluteFilePath = absoluteFilePath;
        this.creationTimestamp = creationTimestamp;
        this.retryCount = initialRetryCount;

        if (absoluteFilePath != null) {
            String lowerCasePath = absoluteFilePath.toLowerCase();
            if (lowerCasePath.endsWith(".zip")) {
                // 检查是否是CDU日志目录下的zip文件
                if (lowerCasePath.contains("/cdu_log/")) {
                    this.filetype = FileType.FILE_CDU_LOG;
                } else {
                    this.filetype = FileType.FILE_ZIP;
                }
            } else if (lowerCasePath.endsWith(".png") || lowerCasePath.endsWith(".jpg") || lowerCasePath.endsWith(".jpeg")) {
                this.filetype = FileType.FILE_IMAGE;
            } else if (lowerCasePath.endsWith(".log") || lowerCasePath.endsWith(".txt")) {
                this.filetype = FileType.FILE_LOG;
            } else if (lowerCasePath.endsWith(".xml")) {
                this.filetype = FileType.FILE_XML;
            } else {
                this.filetype = FileType.FILE_UNKNOWN;
            }
        } else {
            this.filetype = FileType.FILE_UNKNOWN;
        }
    }

    public UploadFileTask(String absoluteFilePath, long creationTimestamp) {
        this(absoluteFilePath, creationTimestamp, 0);
    }

    public String getAbsoluteFilePath() {
        return absoluteFilePath;
    }

    public long getCreationTimestamp() {
        return creationTimestamp;
    }

    public FileType getFiletype() {
        return filetype;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void incrementRetryCount() {
        this.retryCount++;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }
}
