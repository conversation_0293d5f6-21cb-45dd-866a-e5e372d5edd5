package com.xiaopeng.xpautotest.accessibility;

import android.os.Handler;
import android.os.Looper;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import com.xiaopeng.executor.action.accessibility.AccessibilityConfig;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class AutoTestAccessibilityService extends BaseAccessibilityService {
    private static final String TAG = "AutoTestAccessibilityService";


    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        FileLogger.i(TAG, "AccessibilityService connected: " + this);
        AccessibilityHelper.getInstance(this.getApplicationContext()).bindService(this);
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
//        Log.i(TAG, "AccessibilityEvent: " + event.toString());
        super.onAccessibilityEvent(event);
        // 处理事件（如点击、滑动、文本输入）
//        if (event.getEventType() == AccessibilityEvent.TYPE_VIEW_CLICKED) {
//            AccessibilityNodeInfo node = event.getSource();
//            if (node != null) {
////                performClick(node); // 自定义点击逻辑
//                node.recycle(); // 必须释放资源
//            }
//            Log.i(TAG, "TYPE_VIEW_CLICKED");
//        }
//        Log.i(TAG, getRootInActiveWindow().toString());
    }

    @Override
    public void onInterrupt() {
        super.onInterrupt();
        FileLogger.i(TAG, "AccessibilityService onInterrupt");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        FileLogger.i(TAG, "AccessibilityService onDestroy");
        
        // 延迟重启服务
        scheduleServiceRestart();
    }
    
    /**
     * 延迟重启无障碍服务
     */
    private void scheduleServiceRestart() {
        Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    FileLogger.i(TAG, "Attempting to restart AccessibilityService...");
                    AccessibilityConfig.getInstance().initService(getApplicationContext(), getPackageName());
                } catch (Exception e) {
                    FileLogger.e(TAG,"Error restarting AccessibilityService: " + e.getMessage());
                }
            }
        }, 1000); // 1秒后重启
    }
}
