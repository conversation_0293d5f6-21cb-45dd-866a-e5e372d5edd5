package com.xiaopeng.xpautotest.helper.log.model;

import com.xiaopeng.xpautotest.bean.TestStartedEntity;

/**
 * 日志处理上下文
 * 包含日志处理所需的所有信息
 */
public class LogProcessContext {
    private final String executionId;
    private final long startTimestamp;
    private final long endTimestamp;
    private final TestStartedEntity taskExecutionInfo;

    private LogProcessContext(Builder builder) {
        this.executionId = builder.executionId;
        this.startTimestamp = builder.startTimestamp;
        this.endTimestamp = builder.endTimestamp;
        this.taskExecutionInfo = builder.taskExecutionInfo;
    }

    public String getExecutionId() {
        return executionId;
    }

    public long getStartTimestamp() {
        return startTimestamp;
    }

    public long getEndTimestamp() {
        return endTimestamp;
    }

    public TestStartedEntity getTaskExecutionInfo() {
        return taskExecutionInfo;
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建LogProcessContext实例
     * @param startTimestamp 测试开始时间戳
     * @param endTimestamp 测试结束时间戳
     * @param taskExecutionInfo 任务执行信息（包含taskExecutionId）
     * @return LogProcessContext实例
     */
    public static LogProcessContext create(long startTimestamp, long endTimestamp,
                                         TestStartedEntity taskExecutionInfo) {
        String executionId = taskExecutionInfo != null ? String.valueOf(taskExecutionInfo.getTaskExecutionId()) : null;
        return new LogProcessContext(executionId, startTimestamp, endTimestamp, taskExecutionInfo);
    }

    /**
     * 直接构造方法（用于create方法内部调用）
     */
    private LogProcessContext(String executionId, long startTimestamp, long endTimestamp,
                            TestStartedEntity taskExecutionInfo) {
        this.executionId = executionId;
        this.startTimestamp = startTimestamp;
        this.endTimestamp = endTimestamp;
        this.taskExecutionInfo = taskExecutionInfo;
    }

    public static class Builder {
        private String executionId;
        private long startTimestamp;
        private long endTimestamp;
        private TestStartedEntity taskExecutionInfo;

        private Builder() {}

        public Builder executionId(String executionId) {
            this.executionId = executionId;
            return this;
        }

        public Builder startTimestamp(long startTimestamp) {
            this.startTimestamp = startTimestamp;
            return this;
        }

        public Builder endTimestamp(long endTimestamp) {
            this.endTimestamp = endTimestamp;
            return this;
        }

        public Builder taskExecutionInfo(TestStartedEntity taskExecutionInfo) {
            this.taskExecutionInfo = taskExecutionInfo;
            return this;
        }

        public LogProcessContext build() {
            return new LogProcessContext(this);
        }
    }

    @Override
    public String toString() {
        return "LogProcessContext{" +
                "executionId='" + executionId + '\'' +
                ", startTimestamp=" + startTimestamp +
                ", endTimestamp=" + endTimestamp +
                ", taskExecutionInfo=" + taskExecutionInfo +
                '}';
    }
}
