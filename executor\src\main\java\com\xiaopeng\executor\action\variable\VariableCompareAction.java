package com.xiaopeng.executor.action.variable;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 变量比较动作
 *
 * 实现变量比较功能，支持以下语法：
 * - $VAR1 == "value"
 * - $VAR1 != "value"
 * - $VAR1 == $VAR2
 * - $VAR1 != $VAR2
 *
 * 用于If条件判断中的变量比较操作
 */
public class VariableCompareAction extends BaseAction {

    private static final String TAG = "VariableCompareAction";

    // 比较操作符模式
    private static final Pattern COMPARE_PATTERN = Pattern.compile("\\s*(.+?)\\s*(==|!=)\\s*(.+)\\s*");

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        try {
            String expression = context.getAllParams();
            if (expression == null || expression.trim().isEmpty()) {
                throw new ActionException("Compare expression cannot be empty", FailureCode.SI001);
            }

            FileLogger.i(TAG, "Processing compare expression: " + expression);

            // 解析并执行比较
            boolean result = parseAndCompare(expression.trim());

            return result ? TestResult.success("Variable comparison succeeded")
                         : TestResult.failure("Variable comparison failed");

        } catch (ActionException e) {
            FileLogger.e(TAG, "Variable comparison failed: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            String errorMsg = "Variable comparison execution failed: " + e.getMessage();
            FileLogger.e(TAG, errorMsg, e);
            throw new ActionException(errorMsg, FailureCode.AB001, e);
        }
    }
    
    /**
     * 解析表达式并执行比较
     */
    private boolean parseAndCompare(String expression) throws ActionException {
        Matcher matcher = COMPARE_PATTERN.matcher(expression);
        if (!matcher.matches()) {
            throw new ActionException("Invalid compare expression format: " + expression +
                ". Expected format: value1 == value2 or value1 != value2", FailureCode.SI001);
        }

        String leftValue = processStringValue(Objects.requireNonNull(matcher.group(1)).trim());
        String operator = matcher.group(2);
        String rightValue = processStringValue(Objects.requireNonNull(matcher.group(3)).trim());

        boolean result = "==".equals(operator) ? leftValue.equals(rightValue) : !leftValue.equals(rightValue);

        FileLogger.i(TAG, String.format("Comparison: '%s' %s '%s' ,result: %s", leftValue, operator, rightValue, result));

        return result;
    }

}
