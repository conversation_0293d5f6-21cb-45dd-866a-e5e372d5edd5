package com.xiaopeng.xpautotest.client.api;

import com.xiaopeng.xpautotest.bean.HeartbeatEntity;
import com.xiaopeng.xpautotest.bean.TestStopEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.bean.VehicleInfo;
import com.xiaopeng.xpautotest.client.RetrofitClient;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class VehicleApiImpl {
    private final VehicleApiService apiService;

    public VehicleApiImpl() {
        apiService = RetrofitClient.getInstance().create(VehicleApiService.class);
    }

    public void register(VehicleInfo info, Callback<ApiResponse<TestTaskBean>> callback) {
        Call<ApiResponse<TestTaskBean>> call = apiService.register(info);
        call.enqueue(callback);
    }

    public void heartbeat(String vin, Callback<ApiResponse<TestTaskBean>> callback) {
        Call<ApiResponse<TestTaskBean>> call = apiService.heartbeat(vin);
        call.enqueue(callback);
    }

    public Response<ApiResponse<TestTaskBean>> heartbeatSync(String vin, HeartbeatEntity info) throws Exception {
        Call<ApiResponse<TestTaskBean>> call = apiService.heartbeatSync(vin, info);
        return call.execute();
    }
}
