package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TestScriptEntity implements Parcelable {
    private long scriptId;
    private String steps;

    public TestScriptEntity(long scriptId, String steps) {
        this.scriptId = scriptId;
        this.steps = steps;
    }

    protected TestScriptEntity(Parcel in) {
        scriptId = in.readLong();
        steps = in.readString();
    }

    public static final Creator<TestScriptEntity> CREATOR = new Creator<TestScriptEntity>() {
        @Override
        public TestScriptEntity createFromParcel(Parcel in) {
            return new TestScriptEntity(in);
        }

        @Override
        public TestScriptEntity[] newArray(int size) {
            return new TestScriptEntity[size];
        }
    };

    public long getScriptId() {
        return scriptId;
    }

    public String getSteps() {
        return steps;
    }

    public boolean needTrace() {
        return steps.contains("Trace ");
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(scriptId);
        dest.writeString(steps);
    }
}
