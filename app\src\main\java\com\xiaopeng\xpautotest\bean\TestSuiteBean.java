package com.xiaopeng.xpautotest.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class TestSuiteBean {

    public static final int FOLD_STATUS_OPEN = 0;
    public static final int FOLD_STATUS_CLOSE = 1;

    @SerializedName("id")
    private long id;

    @SerializedName("name")
    private String name;

    @SerializedName("status")
    private int status = -1;

    @SerializedName("items")
    private List<TestCaseBean> items;

    public long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public int getStatus() {
        return status;
    }

    public List<TestCaseBean> getItems() {
        return items;
    }
}
