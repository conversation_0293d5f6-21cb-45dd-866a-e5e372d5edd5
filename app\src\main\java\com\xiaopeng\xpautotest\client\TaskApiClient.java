package com.xiaopeng.xpautotest.client;

import com.xiaopeng.xpautotest.bean.TestStartedEntity;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.bean.ReportableEntity;
import com.xiaopeng.xpautotest.bean.StepResultEntity;
import com.xiaopeng.xpautotest.bean.TestFinishEntity;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TraceResultEntity;
import com.xiaopeng.xpautotest.client.api.ApiResponse;
import com.xiaopeng.xpautotest.client.api.TaskApiImpl;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class TaskApiClient {
    private static final String TAG = "TaskApiClient";
    private final TaskApiImpl taskApi;

    public TaskApiClient() {
        taskApi = new TaskApiImpl();
    }

    public void reportTestStarted(TestStartedEntity result, ReportCallback callback) {
        taskApi.reportTestStarted(result.getTaskExecutionId(), result, new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "reportResult onResponse: " + response.body());
                    callback.onSuccess();
                } else {
                    Log.e(TAG, "reportResult onResponse Server error: " + response.code());
                    callback.onFailure(result);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "reportResult onFailure: " + t.getMessage());
                callback.onFailure(result);
            }
        });
    }

    public void reportTestResult(TestResultEntity result, ReportCallback callback) {
        taskApi.reportResult(result.getTaskExecutionId(), result, new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "reportResult onResponse: " + response.body());
                    callback.onSuccess();
                } else {
                    Log.e(TAG, "reportResult onResponse Server error: " + response.code());
                    callback.onFailure(result);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "reportResult onFailure: " + t.getMessage());
                callback.onFailure(result);
            }
        });
    }

    public void reportStepResult(StepResultEntity stepResult, ReportCallback callback) {
        taskApi.reportStepResult(stepResult.getTaskExecutionId(), stepResult, new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "reportStepResult onResponse: " + response.body());
                    callback.onSuccess();
                } else {
                    Log.e(TAG, "reportStepResult onResponse Server error: " + response.code());
                    callback.onFailure(stepResult);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "reportTraceResult onFailure: " + t.getMessage());
                callback.onFailure(stepResult);
            }
        });
    }

    public void reportTraceResult(TraceResultEntity traceResult, ReportCallback callback) {
        taskApi.reportTraceResult(traceResult.getTaskExecutionId(), traceResult, new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "reportTraceResult onResponse: " + response.body());
                    callback.onSuccess();
                } else {
                    Log.e(TAG, "reportTraceResult onResponse Server error: " + response.code());
                    callback.onFailure(traceResult);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "reportTraceResult onFailure: " + t.getMessage());
                callback.onFailure(traceResult);
            }
        });
    }

    public void reportTestFinish(TestFinishEntity entity, ReportCallback callback) {
        taskApi.finishTest(entity.getTaskExecutionId(), entity, new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "reportTestFinish onResponse: " + response.body());
                    callback.onSuccess();
                } else {
                    Log.e(TAG, "reportTestFinish onResponse Server error: " + response.code());
                    callback.onFailure(entity);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                Log.e(TAG, "reportTestFinish onFailure: " + t.getMessage());
                callback.onFailure(entity);
            }
        });

    }

    public interface ReportCallback {
        void onSuccess();
        void onFailure(ReportableEntity entity);
    }
}
