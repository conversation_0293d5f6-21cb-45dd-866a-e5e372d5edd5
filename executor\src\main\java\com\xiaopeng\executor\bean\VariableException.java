package com.xiaopeng.executor.bean;

/**
 * 变量操作异常
 * 
 * 用于表示变量相关操作中发生的异常情况：
 * 1. 变量未定义异常
 * 2. 变量名无效异常
 * 3. 变量值类型转换异常
 * 4. 变量循环引用异常
 */
public class VariableException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 异常类型枚举
     */
    public enum Type {
        UNDEFINED_VARIABLE("变量未定义"),
        INVALID_VARIABLE_NAME("变量名无效"),
        TYPE_CONVERSION_ERROR("类型转换错误"),
        CIRCULAR_REFERENCE("循环引用"),
        INVALID_REFERENCE_FORMAT("变量引用格式无效"),
        VARIABLE_OPERATION_ERROR("变量操作错误");
        
        private final String description;
        
        Type(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private final Type type;
    private final String variableName;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public VariableException(String message) {
        super(message);
        this.type = Type.VARIABLE_OPERATION_ERROR;
        this.variableName = null;
    }

    /**
     * 获取详细的异常信息
     * 
     * @return 详细异常信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append(type.getDescription()).append(": ").append(getMessage());
        
        if (variableName != null) {
            sb.append(" (变量: ").append(variableName).append(")");
        }
        
        if (getCause() != null) {
            sb.append(" [原因: ").append(getCause().getMessage()).append("]");
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "VariableException{" +
                "type=" + type +
                ", variableName='" + variableName + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
