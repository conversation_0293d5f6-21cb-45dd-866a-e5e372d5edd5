package com.xiaopeng.executor.bean;

import com.xiaopeng.xpautotest.community.test.FailureReasonDetail;
import com.xiaopeng.xpautotest.community.test.FailureCode;

/**
 * 自定义异常，支持携带失败原因
 */
public class ActionException extends Exception {

    private FailureReasonDetail failureReason;

    /**
     * 带失败原因的构造函数
     */
    public ActionException(String message, FailureReasonDetail failureReason) {
        super(message);
        this.failureReason = failureReason;
    }

    /**
     * 便捷构造函数，直接传入失败代码
     * 使用FailureCode自带的描述，确保描述一致性
     */
    public ActionException(String message, FailureCode failureCode) {
        super(message);
        this.failureReason = new FailureReasonDetail(failureCode, message, null);
    }

    /**
     * 带原始异常的构造函数，保留异常链和堆栈信息
     * 使用FailureCode自带的描述，确保描述一致性
     */
    public ActionException(String message, FailureCode failureCode, Throwable cause) {
        super(message, cause);
        this.failureReason = new FailureReasonDetail(failureCode, message, cause);
    }

    /**
     * 获取失败原因
     */
    public FailureReasonDetail getFailureReason() {
        return failureReason;
    }

    /**
     * 是否包含失败原因
     */
    public boolean hasFailureReason() {
        return failureReason != null;
    }
}
