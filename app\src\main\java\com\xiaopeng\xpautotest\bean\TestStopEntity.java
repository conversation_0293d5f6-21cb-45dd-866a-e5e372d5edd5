package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TestStopEntity implements Parcelable, ReportableEntity {
    private long taskExecutionId;

    public TestStopEntity(long taskExecutionId) {
        this.taskExecutionId = taskExecutionId;
    }

    protected TestStopEntity(Parcel in) {
        taskExecutionId = in.readLong();
    }

    public static final Creator<TestStopEntity> CREATOR = new Creator<TestStopEntity>() {
        @Override
        public TestStopEntity createFromParcel(Parcel in) {
            return new TestStopEntity(in);
        }

        @Override
        public TestStopEntity[] newArray(int size) {
            return new TestStopEntity[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(taskExecutionId);
    }

    @Override
    public long getTaskExecutionId() {
        return taskExecutionId;
    }
}
