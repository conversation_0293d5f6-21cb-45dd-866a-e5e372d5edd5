package com.xiaopeng.executor;

import android.content.Context;

import com.xiaopeng.executor.action.SmartActionExecutor;
import com.xiaopeng.executor.action.accessibility.AccessibilityConfig;
import com.xiaopeng.executor.action.accessibility.AccessibilityFactory;
import com.xiaopeng.executor.action.carapi.CarApiConfig;
import com.xiaopeng.executor.action.carapi.CarApiFactory;
import com.xiaopeng.executor.action.variable.VariableConfig;
import com.xiaopeng.executor.action.variable.VariableFactory;
import com.xiaopeng.executor.bean.TechType;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;
import com.xiaopeng.xpautotest.community.test.TestScript;
import com.xiaopeng.xpautotest.community.test.TestStep;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.FileUtils;
import com.xiaopeng.executor.bean.VariableContext;
import com.xiaopeng.executor.bean.ExecutorContext;
import java.util.ArrayList;

/**
 * 测试执行器基础实现类
 *
 * 专注于基础设施管理：
 * - 技术栈初始化和工厂注册
 * - 全局变量上下文管理
 * - 回调处理器管理
 * - SmartActionExecutor管理
 */
public abstract class BaseTestExecutor implements ITestExecutor {
    private static final String TAG = "BaseTestExecutor";

    // 基础配置
    protected final Context context;
    protected final String packageName;

    // 核心组件
    private final SmartActionExecutor smartActionExecutor;
    private ExecutionHandler executeHandler;

    // 全局变量上下文
    protected VariableContext globalVariableContext;

    // 执行器上下文
    private final ExecutorContext executorContext;

    /**
     * 构造函数 - 初始化所有基础设施
     * 
     * @param context Android上下文
     * @param packageName 包名
     */
    public BaseTestExecutor(Context context, String packageName) {
        this.context = context;
        this.packageName = packageName;

        // 初始化无障碍服务模块的配置
        AccessibilityConfig accessibilityConfig = AccessibilityConfig.getInstance();
        accessibilityConfig.initService(context, packageName);

        // 初始化车载API配置
        CarApiConfig carApiConfig = CarApiConfig.getInstance();
        carApiConfig.initService(context);

        // 初始化变量支持
        globalVariableContext = new VariableContext();
        VariableConfig variableConfig = VariableConfig.getInstance();
        variableConfig.initService(globalVariableContext);

        // 注册工厂到执行器
        smartActionExecutor = SmartActionExecutor.getInstance();
        smartActionExecutor.registerFactory(TechType.ACCESSIBILITY_SERVICE, new AccessibilityFactory(accessibilityConfig));
        smartActionExecutor.registerFactory(TechType.CAR_API, new CarApiFactory(carApiConfig));
        smartActionExecutor.registerFactory(TechType.VARIABLE, new VariableFactory(variableConfig));

        // 初始化执行器上下文
        this.executorContext = new ExecutorContext(this);
        FileLogger.i(TAG, "initialization completed");


    }
    
    /**
     * 准备执行上下文
     *
     * 为脚本执行准备ExecutorContext，重置所有状态
     * 子类执行器调用此方法来获得状态管理能力
     *
     * @return 准备好的执行器上下文
     */
    protected ExecutorContext prepareExecutionContext() {
        executorContext.resetControlState();
        executorContext.resetExecutionState();
        return executorContext;
    }
    
    @Override
    public void pauseExecution() {
        executorContext.pauseExecution();
    }

    @Override
    public void resumeExecution() {
        executorContext.resumeExecution();
    }

    @Override
    public void stopExecution() {
        executorContext.stopExecution();
    }

    @Override
    public boolean isPaused() {
        return executorContext.isPaused();
    }

    @Override
    public boolean isStopped() {
        return executorContext.isStopped();
    }

    
    @Override
    public void addExecuteHandler(ExecutionHandler handler) {
        this.executeHandler = handler;
    }

    @Override
    public void removeExecuteHandler() {
        this.executeHandler = null;
    }

    /**
     * 获取当前的执行处理器 - 内部方法
     *
     * 供子类和ExecutableUnit访问执行处理器，用于回调通知
     *
     * @return 执行处理器，可能为null
     */
    public ExecutionHandler getExecuteHandler() {
        return executeHandler;
    }
    
    /**
     * 获取智能动作执行器 - 内部方法
     *
     * 供子类和ExecutableUnit访问SmartActionExecutor，不暴露给外部调用方
     *
     * @return 智能动作执行器
     */
    public SmartActionExecutor getSmartActionExecutor() {
        return smartActionExecutor;
    }


    /**
     * 运行测试脚本 - 对外入口
     * 包含一些初始化配置
     *
     * @param scriptId 脚本ID
     * @param steps 测试步骤内容
     * @return 执行结果：0表示成功，非0表示失败
     */
    @Override
    public int runTestScript(long scriptId, String steps) {
        try {
            if (globalVariableContext != null) {
                globalVariableContext.clear();
            }
            // 设置当前线程的变量上下文
            SmartActionExecutor.setCurrentVariableContext(globalVariableContext);
            // 创建测试脚本对象
            TestScript testScript = new TestScript(scriptId, steps);
            return runTestScript(testScript);

        } catch (Exception e) {
            FileLogger.e(TAG, "runTestScript failed: " + e.getMessage(), e);
            return CaseExecuteState.FAILURE;
        } finally {
            FileLogger.i(TAG, "Clear current variable context.");
            SmartActionExecutor.clearCurrentVariableContext();
        }
    }

    /**
     * 运行测试脚本 - 子类入口
     *
     * 这是内部方法，由runTestScript(long, String)调用
     * 子类实现具体的执行逻辑
     *
     * @param testScript 测试脚本对象
     * @return 执行结果状态码
     */
    protected abstract int runTestScript(TestScript testScript);

    /**
     * 运行测试用例（从文件加载）
     * @param scriptName 脚本文件名
     * @return 执行结果状态码
     */
    public int runTestCase(String scriptName) {
        ArrayList<String> arrayList = FileUtils.readFile(scriptName);
        if (arrayList.isEmpty()) {
            FileLogger.e(TAG, "load TestCase was empty!");
            return CaseExecuteState.FAILURE;
        }
        return runTestScript(1, String.valueOf(arrayList));
    }


    /**
     * 测试执行处理器接口
     *
     * 定义测试执行过程中的回调方法，用于监听测试步骤的执行状态
     * 作为BaseTestExecutor的内部接口，与执行器紧密关联
     */
    public interface ExecutionHandler {

        /**
         * 步骤执行成功回调
         *
         * @param step 执行成功的步骤
         * @param result 执行结果
         */
        void onStepSuccess(TestStep step, TestResult result);

        /**
         * 步骤执行失败回调
         *
         * @param step 执行失败的步骤
         * @param result 执行结果（包含失败信息）
         */
        void onStepFailure(TestStep step, TestResult result);

        /**
         * 脚本执行成功回调
         * @param testScript
         */
        void onScriptSuccess(TestScript testScript);
    }
}
