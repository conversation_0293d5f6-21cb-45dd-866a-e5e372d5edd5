package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;

import java.util.ArrayList;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TestStartedEntity implements Parcelable, ReportableEntity {
    private long taskExecutionId;
    private int taskType;
    private long triggerTimestamp;
    private long suiteId;
    private ArrayList pendingScriptIdList;
    private int testCount;
    private int okCount;
    private int ngCount;
    private int naCount;

    public TestStartedEntity(long taskExecutionId, int taskType, long triggerTimestamp, long suiteId, ArrayList<Long> pendingScriptIdList) {
        this.taskExecutionId = taskExecutionId;
        this.taskType = taskType;
        this.triggerTimestamp = triggerTimestamp;
        this.suiteId = suiteId;
        this.pendingScriptIdList = pendingScriptIdList;
        this.testCount = pendingScriptIdList.size();
    }

    protected TestStartedEntity(Parcel in) {
        taskExecutionId = in.readLong();
        taskType = in.readInt();
        triggerTimestamp = in.readLong();
        suiteId = in.readLong();
        pendingScriptIdList = in.readArrayList(Long.class.getClassLoader());
        testCount = in.readInt();
        okCount = in.readInt();
        ngCount = in.readInt();
        naCount = in.readInt();
    }

    public static final Creator<TestStartedEntity> CREATOR = new Creator<TestStartedEntity>() {
        @Override
        public TestStartedEntity createFromParcel(Parcel in) {
            return new TestStartedEntity(in);
        }

        @Override
        public TestStartedEntity[] newArray(int size) {
            return new TestStartedEntity[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(taskExecutionId);
        dest.writeInt(taskType);
        dest.writeLong(triggerTimestamp);
        dest.writeLong(suiteId);
        dest.writeList(pendingScriptIdList);
        dest.writeInt(testCount);
        dest.writeInt(okCount);
        dest.writeInt(ngCount);
        dest.writeInt(naCount);
    }

    @Override
    public long getTaskExecutionId() {
        return taskExecutionId;
    }

    public void setTestCount(int testCount) {
        this.testCount = testCount;
    }

    public void addExecuteCount(int status) {
        if (status == CaseExecuteState.SUCCESS) {
            this.okCount++;
        } else if (status == CaseExecuteState.FAILURE) {
            this.ngCount++;
        } else {
            this.naCount++;
        }
    }

    public long getTriggerTimestamp() {
        return triggerTimestamp;
    }
    public long getSuiteId() {
        return suiteId;
    }

    public int getTestCount() {
        return testCount;
    }

    public int getOkCount() {
        return okCount;
    }

    public int getNgCount() {
        return ngCount;
    }
    public int getNaCount() {
        return naCount;
    }
}
