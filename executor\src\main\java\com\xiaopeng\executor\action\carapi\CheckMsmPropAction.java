package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IMsmController;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class CheckMsmPropAction extends BaseAction {
    private static final String TAG = "CheckMsmPropAction";
    IMsmController mIMsmController = null;

    @Override
    public void init(CarApiConfig config) {
        super.init(config);
        initControllers();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String key = (String) context.getStringParam();
        String expectValue = (String) context.getStringParam();
        int checkTimeout = (Integer) context.getIntParam();
        if (key == null || expectValue == null) {
            throw new ActionException("key or expectValue is null!", FailureCode.SI001);
        }
        boolean result = checkMsmProp(key, expectValue, checkTimeout);
        FileLogger.i(TAG, "key: " + key + ", expectValue: " + expectValue + ", result: " + result);
        if (result) {
            return TestResult.success("MSM property " + key + " matched expected value.");
        } else {
            FailureContextHolder.setFailureIfNotSet(FailureCode.BF001);
            return TestResult.failure("MSM property " + key + " not matched expected value: " + expectValue);
        }
    }

    public boolean checkMsmProp(String key, String expectValue, int timeout) {
        return mIMsmController.checkValue(key, expectValue, timeout);
    }

    private void initControllers() {
        mIMsmController = (IMsmController) carClientWrapper.getController(CarClientWrapper.XP_MSM_SERVICE);
    }
}
