package com.xiaopeng.xpautotest.utils;

import java.io.Closeable;

public final class IoUtils {

    public static void closeQuietly(final Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                // ignore
            }
        }
    }
}