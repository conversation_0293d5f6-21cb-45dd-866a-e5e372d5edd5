package com.xiaopeng.xpautotest.carmanager;

/**
 * @param <T> Any control callback interface extend from IBaseCallback
 * <AUTHOR>
 */
public interface IBaseCarController<T extends IBaseCallback> {

    /**
     * Register base car control callback
     *
     * @param callback callback object
     */
    void registerCallback(T callback);

    /**
     * Unregister base car control callback
     *
     * @param callback callback object
     */
    void unregisterCallback(T callback);
}