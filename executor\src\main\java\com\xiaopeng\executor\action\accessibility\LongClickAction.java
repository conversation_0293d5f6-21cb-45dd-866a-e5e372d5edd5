package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.Locale;
import java.util.Scanner;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

public class LongClickAction extends BaseAction {
    private static final String TAG = "LongClickAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        if (this.service == null) {
            String errorMessage = "This.service is null. Cannot execute LongClickAction.";
            FileLogger.e(TAG, errorMessage);
            throw new ActionException(errorMessage, FailureCode.AB002);
        }

        String coordinateStr = context.getStringParam();
        String durationSecondsStr = context.getStringParam();

        if (coordinateStr == null || coordinateStr.isEmpty()) {
            String errorMessage = "Coordinate parameter cannot be null or empty.";
            FileLogger.e(TAG, errorMessage);
            throw new ActionException(errorMessage,FailureCode.SI001);
        }
        if (durationSecondsStr == null || durationSecondsStr.isEmpty()) {
            String errorMessage = "Duration parameter cannot be null or empty.";
            FileLogger.e(TAG, errorMessage);
            throw new ActionException(errorMessage,FailureCode.SI001);
        }

        int xPercent, yPercent, durationMillis;

        try (Scanner scanner = new Scanner(coordinateStr).useDelimiter("[^0-9]+")) {
            xPercent = scanner.nextInt();
            yPercent = scanner.nextInt();
            scanner.close();
            durationMillis = Integer.parseInt(durationSecondsStr) * 1000;

            final CountDownLatch latch = new CountDownLatch(1);
            final AtomicReference<TestResult> result = new AtomicReference<>();

            GestureCallback callback = new GestureCallback() {
                @Override
                public void onSuccess() {
                    String message = String.format(Locale.ROOT, "Long click dispatched at (%d%%, %d%%) for %dms.", xPercent, yPercent, durationMillis);
                    FileLogger.d(TAG, message);
                    result.set(TestResult.success(message));
                    latch.countDown();
                }

                @Override
                public void onFailure(String error) {
                    String message = String.format(Locale.ROOT, "Failed to dispatch long click at (%d%%, %d%%) for %dms: %s", xPercent, yPercent, durationMillis, error);
                    FileLogger.w(TAG, message);
                    result.set(TestResult.failure(message));
                    latch.countDown();
                }

                @Override
                public void onCancelled() {
                    String message = String.format(Locale.ROOT, "Long click cancelled at (%d%%, %d%%) for %dms.", xPercent, yPercent, durationMillis);
                    FileLogger.d(TAG, message);
                    result.set(TestResult.failure(message));
                    latch.countDown();
                }
            };
            this.service.longClickAsync(xPercent, yPercent, durationMillis, callback);

            // 等待回调完成，设置超时时间
            long timeoutMs = durationMillis + 500; // 手势持续时间 + 500ms缓冲时间
            try {
                boolean completed = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
                if (!completed) {
                    return TestResult.failure("Operation timed out after " + timeoutMs + "ms");
                }
                if (result.get() == null) {
                    return TestResult.failure("Long click action did not complete.");
                }else {
                    return result.get();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ActionException(e.getMessage(), FailureCode.AB001,e);
            }
        } catch (Exception e) {
            throw new ActionException(e.getMessage(),FailureCode.AB001,e);
        }
    }
}