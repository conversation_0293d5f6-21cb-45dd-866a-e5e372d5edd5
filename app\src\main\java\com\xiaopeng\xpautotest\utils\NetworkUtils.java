package com.xiaopeng.xpautotest.utils;

import android.content.Context;
import android.net.NetworkCapabilities;

public class NetworkUtils {

//    public static boolean isWIFI(Context context) {
//        NetworkCapabilities networkCapabilities = context.connectivityManager.getNetworkCapabilities(network);
//        boolean isWifi = networkCapabilities != null && networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
//        return isWifi;
//    }
}
