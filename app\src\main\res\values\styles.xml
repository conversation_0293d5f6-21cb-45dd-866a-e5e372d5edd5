<resources>

    <!-- Base application theme. -->
<!--    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">-->
<!--        &lt;!&ndash; Customize your theme here. &ndash;&gt;-->
<!--        <item name="colorPrimary">@color/colorPrimary</item>-->
<!--        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>-->
<!--        <item name="colorAccent">@color/colorAccent</item>-->
<!--    </style>-->
    <style name="autotest_AppTheme" parent="@style/XAppTheme">
        <item name="android:fontFamily">@null</item>
    </style>

    <style name="TableHeaderLine">
        <item name="android:layout_width">1dp</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:background">@color/separator_line_bg</item>
    </style>

    <style name="TableHeaderHorizontalLine">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/separator_line_bg</item>
    </style>

</resources>
