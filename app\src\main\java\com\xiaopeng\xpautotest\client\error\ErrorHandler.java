package com.xiaopeng.xpautotest.client.error;

import android.content.Context;

import com.xiaopeng.lib.utils.NetUtils;
import com.xiaopeng.xpautotest.bean.ErrorCode;
import com.xiaopeng.xpautotest.client.api.ApiException;

import java.io.IOException;

public class ErrorHandler {
    private final Context context;
//    private final NetworkUtils networkUtils;

    public ErrorHandler(Context context) {
        this.context = context;
//        this.networkUtils = new NetworkUtils(context);
    }

    public ErrorCode handleError(Throwable throwable) {
        return mapToErrorCode(throwable);
//        showErrorMessage(errorCode);
//        handleSpecialCase(errorCode);
    }

    private ErrorCode mapToErrorCode(Throwable t) {
        if (t instanceof ApiException) {
            ApiException apiEx = (ApiException) t;
            return mapApiError(apiEx.getCode());
        } else if (t instanceof IOException) {
            return mapNetworkError();
//        } else if (t instanceof FileWriteException) {
//            return ErrorCode.ERROR_FILE_WRITE;
        }
        return ErrorCode.ERROR_UNKNOWN;
    }

    private ErrorCode mapNetworkError() {
        if (!NetUtils.isNetworkAvailable(context)) {
            return ErrorCode.ERROR_NETWORK_NO_CONNECTION;
        } else if (!NetUtils.isWifiEnabled(context)) {
            return ErrorCode.ERROR_NETWORK_NO_WIFI;
        }
        return ErrorCode.ERROR_NETWORK_GENERIC;
    }

    private ErrorCode mapApiError(int code) {
        switch (code) {
            case 400: return ErrorCode.ERROR_API_EMPTY_PARAM;
            case 401: return ErrorCode.ERROR_AUTH_TOKEN_EXPIRED;
            case 404: return ErrorCode.ERROR_API_NOT_FOUND;
            case 409: return ErrorCode.ERROR_API_REGISTER_FAIL;
            case 500: return ErrorCode.ERROR_API_SERVER_ERROR;
            case 2011: return ErrorCode.ERROR_API_REGISTER_NO_TASK;
            case 2012: return ErrorCode.ERROR_API_DOWNLOAD_SCRIPT;
            case 2013: return ErrorCode.ERROR_API_DOWNLOAD_SCRIPT_WRITE;
            case 2014: return ErrorCode.ERROR_API_EXECUTION_STOP;
            default: return ErrorCode.ERROR_API_SERVER_UNKNOWN;
        }
    }

//    private void showErrorMessage(ErrorCode errorCode) {
//        String message = context.getString(errorCode.getMessageRes());
//        if (errorCode == ErrorCode.ERROR_API_REGISTER_FAIL) {
//            message = String.format(message, "用户名已存在"); // 动态参数示例
//        }
//
//        new AlertDialog.Builder(context)
//                .setTitle("操作失败")
//                .setMessage(message)
//                .setPositiveButton("确定", null)
//                .setNegativeButton("重试", (d, w) -> retryAction())
//                .show();
//    }
//
//    private void handleSpecialCase(ErrorCode errorCode) {
//        if (errorCode == ErrorCode.ERROR_AUTH_TOKEN_EXPIRED) {
//            context.startActivity(new Intent(context, LoginActivity.class));
//        }
//    }
}
