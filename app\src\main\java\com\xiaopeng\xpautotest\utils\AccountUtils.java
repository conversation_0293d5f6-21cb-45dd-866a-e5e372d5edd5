package com.xiaopeng.xpautotest.utils;

import android.text.TextUtils;

import com.xiaopeng.lib.utils.SystemPropertyUtil;
import com.xiaopeng.lib.utils.info.BuildInfoUtils;
import com.xiaopeng.xpautotest.community.utils.Log;

public class AccountUtils {

    public static boolean sCarDebug = false;

    public static String getVin() {
        if (sCarDebug) {
            return "LMVHFEFZ4KA668100";
        }
        return SystemPropertyUtil.getVIN();
    }

    public static String getCduId() {
        if (sCarDebug) {
            return "0024004D3138510331353739";
        }
        return getHardwareId();
    }

    public static String getHardwareId() {
        String hardwareId = BuildInfoUtils.getHardwareId();
        if (BuildInfoUtils.isEngVersion() && TextUtils.isEmpty(hardwareId)) {
            Log.e("AccountUtils", "Invalid hardware ID.");
            return "";
        } else {
            return hardwareId;
        }
    }

}
