package com.xiaopeng.xpautotest.client.api;

import com.xiaopeng.xpautotest.bean.StepResultEntity;
import com.xiaopeng.xpautotest.bean.TestFinishEntity;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TestStartedEntity;
import com.xiaopeng.xpautotest.bean.TestStopEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.bean.TraceResultEntity;
import com.xiaopeng.xpautotest.client.RetrofitClient;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class TaskApiImpl {
    private static final String TAG = "TaskApiImpl";
    private final TaskApiService apiService;

    public TaskApiImpl() {
        apiService = RetrofitClient.getInstance().create(TaskApiService.class);
    }

    public void fetchTaskInfo(long taskId, Callback<TestTaskBean> callback) {
        Call<TestTaskBean> call = apiService.getTaskInfo(taskId);
        call.enqueue(callback);
    }

    public void downloadScript(String url, Callback<ResponseBody> callback) {
        Call<ResponseBody> call = apiService.downloadScript(url);
        call.enqueue(callback);
    }

    public void startTest(long taskId, TestTaskBean taskInfo, Callback<ApiResponse<Long>> callback) {
        // execute task
        Call<ApiResponse<Long>> call = apiService.startTest(taskId, taskInfo);
        call.enqueue(callback);
    }

    public void stopTest(long taskId, Callback<ApiResponse> callback) {
        Call<ApiResponse> call = apiService.stopTest(taskId);
        call.enqueue(callback);
    }

    public void restartTest(long taskId, Callback<ApiResponse> callback) {
        Call<ApiResponse> call = apiService.restartTest(taskId);
        call.enqueue(callback);
    }

    public void reportTestStarted(long taskId, TestStartedEntity taskExecution, Callback<ApiResponse> callback) {
        Call<ApiResponse> call = apiService.reportTestStarted(taskId, taskExecution);
        call.enqueue(callback);
    }

    public void reportResult(long taskExecutionId, TestResultEntity testResult, Callback<ApiResponse> callback) {
        Call<ApiResponse> call = apiService.reportResult(taskExecutionId, testResult);
        call.enqueue(callback);
    }

    public void reportStepResult(long taskExecutionId, StepResultEntity stepResult, Callback<ApiResponse> callback) {
        Call<ApiResponse> call = apiService.reportStepResult(taskExecutionId, stepResult);
        call.enqueue(callback);
    }

    public void reportTraceResult(long taskExecutionId, TraceResultEntity traceResult, Callback<ApiResponse> callback) {
        Call<ApiResponse> call = apiService.reportTraceResult(taskExecutionId, traceResult);
        call.enqueue(callback);
    }

    public void finishTest(long taskExecutionId, TestFinishEntity testFinishEntity, Callback<ApiResponse> callback) {
        Call<ApiResponse> call = apiService.finishTest(testFinishEntity.getTaskExecutionId(), testFinishEntity);
        call.enqueue(callback);
    }

    // 同步请求接口
    public Response<ApiResponse<Long>> startTestSync(TestTaskBean taskInfo) throws Exception {
        // execute task
        Call<ApiResponse<Long>> call = apiService.startTest(taskInfo.getId(), taskInfo);
        return call.execute();
    }

    public Response<ApiResponse> reportTestStartedSync(TestStartedEntity taskExecution) throws Exception {
        Call<ApiResponse> call = apiService.reportTestStarted(taskExecution.getTaskExecutionId(), taskExecution);
        return call.execute();
    }

    public Response<ApiResponse> reportTestResultSync(TestResultEntity testResult) throws Exception {
        Call<ApiResponse> call = apiService.reportResult(testResult.getTaskExecutionId(), testResult);
        return call.execute();
    }

    public Response<ApiResponse> reportStepResultSync(StepResultEntity stepResult) throws Exception {
        Call<ApiResponse> call = apiService.reportStepResult(stepResult.getTaskExecutionId(), stepResult);
        return call.execute();
    }

    public Response<ApiResponse> reportTraceResultSync(TraceResultEntity traceResult) throws Exception {
        Call<ApiResponse> call = apiService.reportTraceResult(traceResult.getTaskExecutionId(), traceResult);
        return call.execute();
    }

    public Response<ApiResponse> reportTestFinishSync(TestFinishEntity testFinishEntity) throws Exception {
        Call<ApiResponse> call = apiService.finishTest(testFinishEntity.getTaskExecutionId(), testFinishEntity);
        return call.execute();
    }

    public Response<ApiResponse> reportTestStopSync(TestStopEntity testStopEntity) throws Exception {
        Call<ApiResponse> call = apiService.stopTest(testStopEntity.getTaskExecutionId());
        return call.execute();
    }
}
