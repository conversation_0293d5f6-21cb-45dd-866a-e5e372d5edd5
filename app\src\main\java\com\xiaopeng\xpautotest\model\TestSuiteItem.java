package com.xiaopeng.xpautotest.model;

import java.util.List;

public class TestSuiteItem implements ITestSuiteItem {
    private long id;
    private String name;
    private int status = -1;
    private List<TestCaseItem> items;
    private boolean isSelected; // 是否选中

    public TestSuiteItem() {
    }

    public TestSuiteItem(long id, String name, List<TestCaseItem> items) {
        this.id = id;
        this.name = name;
        this.items = items;
    }

    // Getters & Setters
    public long getId() { return id; }

    public void setId(long id) { this.id = id; }
    public int getStatus() { return status; }

    public  void setStatus(int status) { this.status = status; }
    public String getName() { return name; }

    public void setName(String name) { this.name = name; }
    public List<TestCaseItem> getItems() { return items; }

    public void setItems(List<TestCaseItem> items) { this.items = items; }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }
}
