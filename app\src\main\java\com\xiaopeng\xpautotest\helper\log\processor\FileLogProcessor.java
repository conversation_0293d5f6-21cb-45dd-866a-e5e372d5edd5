package com.xiaopeng.xpautotest.helper.log.processor;

import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.community.event.FileUploadEvent;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessContext;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessorResult;
import com.xiaopeng.xpautotest.utils.OSSPathUtils;

import org.greenrobot.eventbus.EventBus;

import java.io.File;

/**
 * FileLogger日志处理器
 * 负责处理FileLogger产生的执行日志
 */
public class FileLogProcessor implements ILogProcessor {
    private static final String TAG = "FileLogProcessor";

    @Override
    public LogProcessorResult process(LogProcessContext context) {
        String executionId = null;
        try {
            executionId = context.getExecutionId();
            if (executionId == null || executionId.trim().isEmpty()) {
                return LogProcessorResult.failure(getSupportedLogType(),
                    "ExecutionId is null or empty").build();
            }

            // 使用FileLogger进行日志压缩
            String zipFilePath = FileLogger.getInstance().zipAndRetrieveExecutionLogPath(executionId);

            if (zipFilePath == null) {
                return LogProcessorResult.failure(getSupportedLogType(),
                    "Failed to zip execution log or log file not found for ID: " + executionId)
                    .build();
            }

            // 安全地触发文件上传事件
            try {
                EventBus.getDefault().post(new FileUploadEvent(zipFilePath));
            } catch (Exception e) {
                FileLogger.e(TAG, "Failed to post FileUploadEvent, continuing", e);
            }

            // 构造OSS路径
            String ossPath = buildOssPath(zipFilePath);
            if (ossPath == null) {
                return LogProcessorResult.failure(getSupportedLogType(),
                    "Failed to build OSS path for: " + zipFilePath).build();
            }

            return LogProcessorResult.success(getSupportedLogType())
                    .localPath(zipFilePath)
                    .ossPath(ossPath)
                    .build();

        } catch (Exception e) {
            String errorMsg = "Error processing FileLogger log for ID: " + executionId + ", error: " + e.getMessage();
            FileLogger.e(TAG, errorMsg, e);
            return LogProcessorResult.failure(getSupportedLogType(), errorMsg).build();
        }
    }

    /**
     * 安全地构造OSS路径
     * @param zipFilePath 本地zip文件路径
     * @return OSS路径，失败返回null
     */
    private String buildOssPath(String zipFilePath) {
        try {
            File zipFile = new File(zipFilePath);
            String zipFileName = zipFile.getName();
            return OSSPathUtils.buildObjectKey(
                UploadFileTask.FileType.FILE_ZIP,
                zipFileName,
                zipFilePath
            );
        } catch (Exception e) {
            FileLogger.e(TAG, "Failed to build OSS path for: " + zipFilePath, e);
            return null;
        }
    }

    @Override
    public LogProcessorResult.LogType getSupportedLogType() {
        return LogProcessorResult.LogType.FILE_LOG;
    }

    @Override
    public boolean shouldProcess(LogProcessContext context) {
        // FileLogger日志总是需要处理（只要有executionId）
        return context.getExecutionId() != null && !context.getExecutionId().trim().isEmpty();
    }
}
