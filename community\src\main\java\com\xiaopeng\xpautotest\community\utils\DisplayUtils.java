package com.xiaopeng.xpautotest.community.utils;

import android.view.Display;

/**
 * Display相关工具类
 */
public class DisplayUtils {
    
    /**
     * 获取Display的物理ID
     * 通过反射获取 Display.mDisplayInfo.address.mPhysicalDisplayId
     *
     * @param display Display对象
     * @return 物理displayId，如果获取失败返回逻辑displayId
     */
    public static long getPhysicalDisplayId(Display display) {
        if (display == null) {
            return -1L;
        }

        // 使用反射工具获取嵌套字段值
        Long physicalId = ReflectionUtils.getNestedField(display, "mDisplayInfo.address.mPhysicalDisplayId", Long.class);

        // 如果获取失败，回退到逻辑displayId
        return physicalId != null ? physicalId : display.getDisplayId();
    }
}
