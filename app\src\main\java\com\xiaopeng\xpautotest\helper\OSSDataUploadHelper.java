package com.xiaopeng.xpautotest.helper;

import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.bean.UploadFileTask;
import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class OSSDataUploadHelper {
    private static final String TAG = "OSSDataUploadHelper";


    public static List<UploadFileTask> scanAndCreateUploadTasks() {
        FileLogger.i(TAG, "Scanning for existing files to upload");
        List<UploadFileTask> existingFiles = new ArrayList<>();

        // 截图目录
        File screenshotDir = new File(Constant.AUTOTEST_IMAGE_PATH);
        if (screenshotDir.exists() && screenshotDir.isDirectory()) {
            File[] screenshots = screenshotDir.listFiles((dir, name) ->
                    name.toLowerCase().endsWith(".png") || name.toLowerCase().endsWith(".jpg"));
            if (screenshots != null) {
                for (File screenshot : screenshots) {
                    if (screenshot.isFile()) {
                        existingFiles.add(new UploadFileTask(screenshot.getAbsolutePath(), screenshot.lastModified(), UploadFileTask.FileType.FILE_IMAGE));
                        FileLogger.d(TAG, "Found existing image: " + screenshot.getAbsolutePath());
                    }
                }
            }
        } else {
            FileLogger.w(TAG, "Screenshot directory not found: " + Constant.AUTOTEST_IMAGE_PATH);
        }

        // UIDUMP目录
        File uiHierarchyDir = new File(Constant.UIDUMP_DATA_PATH);
        if (uiHierarchyDir.exists() && uiHierarchyDir.isDirectory()) {
            File[] uiHierarchies = uiHierarchyDir.listFiles((dir, name) ->
                    name.toLowerCase().endsWith(".xml"));
            if (uiHierarchies != null) {
                for (File uiHierarchy : uiHierarchies) {
                    if (uiHierarchy.isFile()) {
                        existingFiles.add(new UploadFileTask(uiHierarchy.getAbsolutePath(), uiHierarchy.lastModified(), UploadFileTask.FileType.FILE_XML));
                        FileLogger.d(TAG, "Found existing UI hierarchy: " + uiHierarchy.getAbsolutePath());
                    }
                }
            }
        } else {
            FileLogger.w(TAG, "UI hierarchy directory not found: " + Constant.UIDUMP_DATA_PATH);
        }

        // 日志目录-只扫压缩包（finishtest的时候已经将切分的日志打包了）
        File logDir = new File(Constant.AUTOTEST_LOG_PATH);
        if (logDir.exists() && logDir.isDirectory()) {
            File[] logs = logDir.listFiles((dir, name) ->
                    name.toLowerCase().endsWith(".zip"));
            if (logs != null) {
                for (File log : logs) {
                    if (log.isFile()) {
                        existingFiles.add(new UploadFileTask(log.getAbsolutePath(), log.lastModified(), UploadFileTask.FileType.FILE_ZIP));
                        FileLogger.d(TAG, "Found existing log zip: " + log.getAbsolutePath());
                    }
                }
            }
        } else {
            FileLogger.w(TAG, "Log directory not found: " + Constant.AUTOTEST_LOG_PATH);
        }

        // 大屏日志目录-只扫压缩包
        File cduLogDir = new File(Constant.CDU_LOG_PATH);
        if (cduLogDir.exists() && cduLogDir.isDirectory()) {
            File[] cduLogs = cduLogDir.listFiles((dir, name) ->
                    name.toLowerCase().endsWith(".zip"));
            if (cduLogs != null) {
                for (File cduLog : cduLogs) {
                    if (cduLog.isFile()) {
                        existingFiles.add(new UploadFileTask(cduLog.getAbsolutePath(), cduLog.lastModified(), UploadFileTask.FileType.FILE_CDU_LOG));
                        FileLogger.d(TAG, "Found existing CDU log zip: " + cduLog.getAbsolutePath());
                    }
                }
            }
        } else {
            FileLogger.w(TAG, "CDU log directory not found: " + Constant.CDU_LOG_PATH);
        }

        existingFiles.sort(Comparator.comparingLong(UploadFileTask::getCreationTimestamp));
        FileLogger.i(TAG, "Finished scanning. Found " + existingFiles.size() + " existing files.");
        return existingFiles;
    }

    public static boolean deleteLocalFile(UploadFileTask uploadFileTask) {
        File localFile = new File(uploadFileTask.getAbsoluteFilePath());
        if (localFile.exists()) {
            if (localFile.delete()) {
                return true;
            } else {
                return false;
            }
        }
        FileLogger.w(TAG, "Local file not found for deletion: " + uploadFileTask.getAbsoluteFilePath());
        return true;
    }

    public static boolean attemptDeleteWithRetries(UploadFileTask uploadFileTask) {
        boolean deleted = false;
        int retryCount = 0;
        int maxRetries = 1;
        long initialDelayMs  = 2000;
        while (!deleted && retryCount <= maxRetries) {
            deleted = deleteLocalFile(uploadFileTask);
            if (deleted) {
                //FileLogger.i(TAG, "Successfully deleted local file: " + uploadFileTask.getAbsoluteFilePath() + " on attempt " + (retryCount + 1));
                return true;
            }
            retryCount++;
            if (retryCount < maxRetries) {
                FileLogger.w(TAG, "Failed to delete local file: " + uploadFileTask.getAbsoluteFilePath() + ". Retry " + retryCount + "/" + maxRetries);
                try {
                    Thread.sleep(initialDelayMs * retryCount);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    FileLogger.w(TAG, "Delete retry sleep interrupted for: " + uploadFileTask.getAbsoluteFilePath());
                    return false; // 如果线程被中断，停止重试并返回失败
                }
            }
        }
        if (!deleted) {
            FileLogger.e(TAG, "Failed to delete local file after " + maxRetries + " retries: " + uploadFileTask.getAbsoluteFilePath());
        }
        return deleted;
    }
}
