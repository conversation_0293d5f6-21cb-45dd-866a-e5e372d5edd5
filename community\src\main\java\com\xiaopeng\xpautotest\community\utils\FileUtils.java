package com.xiaopeng.xpautotest.community.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class FileUtils {
    private static final String TAG = "FileUtils";

    /**
     * 读取文本数据
     *
     * @return String, 读取到的文本内容，失败返回null
     */
    public static String readFileContent(String filePath) {
        File file = TextUtils.isEmpty(filePath) ? null : new File(filePath);
        return readFile(file);
    }

    /**
     * 读取文本数据
     *
     * @return String, 读取到的文本内容，失败返回null
     */
    public static String readFile(File file) {
        if (file == null || !file.exists()) {
            Log.w(TAG, "file is not exist! file = " + file);
            return null;
        }
        FileInputStream fis = null;
        String content = null;
        try {
            fis = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            while (true) {
                int readLength = fis.read(buffer);
                if (readLength == -1) {
                    break;
                }
                arrayOutputStream.write(buffer, 0, readLength);
            }
            fis.close();
            arrayOutputStream.close();
            content = arrayOutputStream.toString("utf-8");

        } catch (Exception e) {
            Log.e(TAG, "---error = " + e.getMessage());
        } finally {
            safeClose(fis);
        }
        Log.i(TAG, "---read file, file = " + file);
        return content;
    }

    public static void safeClose(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                com.xiaopeng.xpautotest.community.utils.Log.e(TAG, "---error = " + e.getMessage());
            }
        }
    }

    public static ArrayList<String> readFile(Context context, String fileName) {
        ArrayList<String> arrayList = new ArrayList<>();
        FileInputStream inputStream = null;
        BufferedReader bufferedReader = null;
        byte[] buffer = null;
        try {
            inputStream = context.openFileInput(fileName);
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String str = null;
            while ((str = bufferedReader.readLine()) != null) {
                arrayList.add(str);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try{
                if (inputStream != null) inputStream.close();
                if (bufferedReader != null) bufferedReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return arrayList;
    }

    public static ArrayList<String> readFile(String fileName) {
        ArrayList<String> arrayList = new ArrayList<>();
        FileInputStream inputStream = null;
        BufferedReader bufferedReader = null;
        byte[] buffer = null;
        try {
            File file = new File(fileName);
            inputStream = new FileInputStream(file);
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String str = null;
            while ((str = bufferedReader.readLine()) != null) {
                arrayList.add(str);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try{
                if (inputStream != null) inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try{
                if (bufferedReader != null) bufferedReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return arrayList;
    }

    public static void writeFile(Context context, String fileName) {
        FileOutputStream out = null;
        BufferedWriter writer = null;
        try {
            //设置文件名称，以及存储方式
            out = context.openFileOutput(fileName, Context.MODE_PRIVATE);
            //创建一个OutputStreamWriter对象，传入BufferedWriter的构造器中
            writer = new BufferedWriter(new OutputStreamWriter(out));
            //向文件中写入数据
            writer.write("test");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (writer != null) writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void writeFile(String fileName, String line) {
        FileOutputStream out = null;
        BufferedWriter writer = null;
        try {
            File file = new File(fileName);
            //设置文件名称，以及存储方式
            out = new FileOutputStream(file, true);
            //创建一个OutputStreamWriter对象，传入BufferedWriter的构造器中
            writer = new BufferedWriter(new OutputStreamWriter(out));
            if (!file.exists()) {
                writer.write("case name,total loop,current loop,starttime,endtime,result");
            }
            //向文件中写入数据
            writer.write(line);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (writer != null)
                    writer.close();
                if (out != null)
                    out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除单个文件
     * @param   filePath    被删除文件的文件名
     * @return 文件删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.isFile() && file.exists()) {
            return file.delete();
        }
        return false;
    }

    /**
     * 删除文件夹以及目录下的文件
     * @param   filePath 被删除目录的文件路径
     * @return  目录删除成功返回true，否则返回false
     */
    public static boolean deleteDirectory(String filePath) {
        boolean flag = false;
        //如果filePath不以文件分隔符结尾，自动添加文件分隔符
        if (!filePath.endsWith(File.separator)) {
            filePath = filePath + File.separator;
        }
        File dirFile = new File(filePath);
        if (!dirFile.exists() || !dirFile.isDirectory()) {
            return false;
        }
        flag = true;
        File[] files = dirFile.listFiles();
        if (files == null) {
            Log.e(TAG, "deleteDirectory: listFiles is null! file=" + dirFile.getAbsolutePath());
            return false;
        }
        //遍历删除文件夹下的所有文件(包括子目录)
        for (int i = 0; i < files.length; i++) {
            if (files[i].isFile()) {
                //删除子文件
                flag = deleteFile(files[i].getAbsolutePath());
                if (!flag) break;
            } else {
                //删除子目录
                flag = deleteDirectory(files[i].getAbsolutePath());
                if (!flag) break;
            }
        }
        if (!flag) return false;
        //删除当前空目录
        return dirFile.delete();
    }

    /**
     *  根据路径删除指定的目录或文件，无论存在与否
     *@param filePath  要删除的目录或文件
     *@return 删除成功返回 true，否则返回 false。
     */
    public static boolean deleteFolder(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            return false;
        } else {
            if (file.isFile()) {
                // 为文件时调用删除文件方法
                return deleteFile(filePath);
            } else {
                // 为目录时调用删除目录方法
                return deleteDirectory(filePath);
            }
        }
    }

    public static List<String> getFilesAllName(String path, String filter) {
        List<String> filenameList = new ArrayList<>();
        File file = new File(path);
        File[] files = file.listFiles();
        if (files == null) {
            Log.e("FileUtils", path + "was empty");
            return filenameList;
        }

        for (int i = 0; i < files.length; i++) {
            if (filter.equals("") || files[i].getName().startsWith(filter)) {
                filenameList.add(files[i].getName());
            }
        }
        Collections.sort(filenameList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        return filenameList;
    }

    public static String getMaxFileName(String path) {
        List<String> fileNames = FileUtils.getFilesAllName(path, "");
        String maxStr = "";
        for (int i = 0; i < fileNames.size(); i++) {
            String fileName = fileNames.get(i);
            if (maxStr.equals("")) {
                maxStr = fileName;
            } else {
                long diffTime = DateUtils.getDiffTimeOfTwoDate(maxStr, fileName);
                if (diffTime < 0) {
                    maxStr = fileName;
                }
            }
        }
        return maxStr;
    }

    public static boolean isFolderExists(String strFolder) {
        File file = new File(strFolder);
        if (!file.exists()) {
            if (file.mkdir()) {
                return true;
            } else {
                return false;
            }
        }
        return true;
    }

    public static boolean grepFileRealtime(String fileName, String expect, long timeout, Integer lineNum) {
        File logFile = new File(fileName);
        long lastTimeFileSize = logFile.length();
        long startTime = System.currentTimeMillis();
        RandomAccessFile randomAccessFile = null;
        while (true) {
            try {
                randomAccessFile = new RandomAccessFile(logFile, "r");
                long nextend = lastTimeFileSize - lineNum;
                if (nextend < 0)
                    nextend = 1;
                randomAccessFile.seek(nextend);
                String tmp = null;
                while ((tmp = randomAccessFile.readLine()) != null) {
//                    System.out.println(tmp);
                    //RandomAccessFile下用readLine的方式会自动将编码变成ISO-8859-1， 需要转换为utf-8
                    String line = new String(tmp.getBytes("ISO_8859_1"), StandardCharsets.UTF_8);
                    if (line.contains(expect)) {
                        System.out.println(line);
                        return true;
                    }
                }
                lastTimeFileSize = randomAccessFile.length();
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            } finally {
                if (randomAccessFile != null) {
                    try {
                        randomAccessFile.close();
                    } catch (IOException ie) {
                        System.out.println("log file " + fileName + " closed failed");
                    }
                }
            }
            long endTime = System.currentTimeMillis();
            if ((endTime - startTime) > timeout) {
                System.out.println("grep file " + fileName + " timeout.");
                return false;
            }
            // 只有第一次往前查找lineNum个字符
            lineNum = 1;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
                //Thread.currentThread().interrupt();
                return false;
            }
        }
    }

}
