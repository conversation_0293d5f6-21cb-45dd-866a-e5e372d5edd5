package com.xiaopeng.xpautotest.client.oss;

import android.content.Context;
import com.alibaba.sdk.android.oss.ClientConfiguration;
import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSS;
import com.alibaba.sdk.android.oss.OSSClient;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.common.OSSLog;
import com.alibaba.sdk.android.oss.common.auth.OSSCredentialProvider;
import com.alibaba.sdk.android.oss.common.auth.OSSPlainTextAKSKCredentialProvider;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.xiaopeng.xpautotest.App;
import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.constant.EnvironmentConfig;
import com.xiaopeng.xpautotest.utils.OSSPathUtils;
import java.io.File;

public class AliyunOSSUploader implements IUploader {

    private static final String TAG = "AliyunOSSUploader";

    private static volatile AliyunOSSUploader instance;
    private OSS oss;
    private String bucketName;
    private AliyunOSSUploader() {
        try {
            EnvironmentConfig.OSSConfig ossConfig = EnvironmentConfig.getInstance().getOssConfig();
            this.bucketName = ossConfig.getBucketName();

            Context appContext = App.getInstance().getApplicationContext();
            OSSCredentialProvider credentialProvider = new OSSPlainTextAKSKCredentialProvider(ossConfig.getAccessKey(), ossConfig.getSecurityKey());
            ClientConfiguration conf = new ClientConfiguration();
            //OSSLog.enableLog();
            oss = new OSSClient(appContext, ossConfig.getEndpoint(), credentialProvider, conf);

        } catch (Exception ex) {
            FileLogger.e(TAG, "Exception: " + ex.getMessage());
        }
    }

    public static synchronized AliyunOSSUploader getInstance() {
        if (instance == null) {
            instance = new AliyunOSSUploader();
        }
        return instance;
    }

    @Override
    public boolean upload(UploadFileTask uploadFileTask) throws Exception {
        if (oss == null) {
            FileLogger.e(TAG, "OSS client is not initialized. Cannot upload.");
            return false;
        }

        File fileToUpload = new File(uploadFileTask.getAbsoluteFilePath());
        if (!fileToUpload.exists() || !fileToUpload.isFile() || !fileToUpload.canRead()) {
            FileLogger.e(TAG, "Invalid or unreadable file provided: " + uploadFileTask.getAbsoluteFilePath());
            return false;
        }

        String originalFileName = fileToUpload.getName();
        String objectKey = OSSPathUtils.buildObjectKey(uploadFileTask.getFiletype(), originalFileName, uploadFileTask.getAbsoluteFilePath());

        if (objectKey == null || objectKey.isEmpty()) {
            FileLogger.w(TAG, "Generated object key is null or empty for file: " + originalFileName + ". Skipping upload.");
            return false;
        }

        FileLogger.i(TAG, "Attempting Aliyun OSS upload for '" + fileToUpload.getAbsolutePath());

        try {

            PutObjectRequest put = new PutObjectRequest(bucketName, objectKey, fileToUpload.getAbsolutePath());
            long start_time = System.currentTimeMillis();
            PutObjectResult putResult = oss.putObject(put);
            long duration = System.currentTimeMillis() - start_time;

            FileLogger.i(TAG, "Aliyun OSS upload completed for '" + objectKey + "' in " + duration + " ms. ETag: " + putResult.getETag());
            return true;

        } catch (ClientException e) {
            FileLogger.e(TAG, "Aliyun OSS SDK ClientException for '" + objectKey + "': " + e.getMessage());
            throw e;
        } catch (ServiceException e) {
            FileLogger.e(TAG, "Aliyun OSS SDK ServiceException for '" + objectKey + "': " + e.getErrorCode() + " - " + e.getRawMessage());
            FileLogger.e(TAG, "Details: RequestId: " + e.getRequestId() + ", HostId: " + e.getHostId() + ", StatusCode: " + e.getStatusCode());
            throw e;
        } catch (Exception e) {
            FileLogger.e(TAG, "Unexpected generic exception during Aliyun OSS upload for '" + objectKey + "': " + e.getMessage());
            throw e;
        }
    }

    public void close() {
        if (oss != null) {
            FileLogger.d(TAG, "Nullifying Aliyun OSS client instance.");
            oss = null;
        }
        if (instance != null) {
            FileLogger.d(TAG, "Nullifying AliyunOSSUploader singleton instance.");
            instance = null;
        }
        FileLogger.i(TAG, "AliyunOSSUploader components 'closed' (nullified).");
    }
}