package com.xiaopeng.xpautotest.community.test;

public class TestResult {
    private boolean success;
    private String message;
    private Object data; // 用于存储动作执行可能返回的数据
    private Throwable error; // 用于存储执行过程中发生的异常
    private FailureReasonDetail failureReason; // 失败原因详情
    private long startTime; // 步骤开始时间
    private long endTime;   // 步骤结束时间

    public TestResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public TestResult(boolean success, String message, Object data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Throwable getError() {
        return error;
    }

    public void setError(Throwable error) {
        this.error = error;
    }

    public FailureReasonDetail getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(FailureReasonDetail failureReason) {
        this.failureReason = failureReason;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public static TestResult success(String message) {
        return new TestResult(true, message);
    }

    public static TestResult success(String message, Object data) {
        return new TestResult(true, message, data);
    }

    public static TestResult failure(String message) {
        return new TestResult(false, message);
    }

    public static TestResult failure(String message, Object data) {
        return new TestResult(false, message, data);
    }

    public static TestResult failure(String message, FailureReasonDetail failureReason) {
        TestResult result = new TestResult(false, message);
        result.setFailureReason(failureReason);
        result.setData(new TestResult.ActionArtifacts("", ""));
        return result;
    }

    public static TestResult failure(String message, Object data, FailureReasonDetail failureReason) {
        TestResult result = new TestResult(false, message, data);
        result.setFailureReason(failureReason);
        return result;
    }

    // 用于截图和UI转储结果的嵌套类
    public static class ActionArtifacts {
        public final String imgFileName;
        public final String uiDumpFileName;

        public ActionArtifacts(String imgFileName, String uiDumpFileName) {
            this.imgFileName = imgFileName;
            this.uiDumpFileName = uiDumpFileName;
        }
    }
}