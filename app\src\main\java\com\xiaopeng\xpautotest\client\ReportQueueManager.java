package com.xiaopeng.xpautotest.client;

import com.xiaopeng.xpautotest.bean.TestStartedEntity;
import com.xiaopeng.xpautotest.bean.TestStopEntity;
import com.xiaopeng.xpautotest.client.api.ApiResponse;
import com.xiaopeng.xpautotest.client.api.TaskApiImpl;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.manager.ConnectionChangeReceiver;
import com.xiaopeng.xpautotest.bean.ReportableEntity;
import com.xiaopeng.xpautotest.bean.StepResultEntity;
import com.xiaopeng.xpautotest.bean.TestFinishEntity;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TraceResultEntity;
import com.xiaopeng.xpautotest.helper.TestDataStorageHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;

import retrofit2.Response;

public class ReportQueueManager {
    private static final String TAG = "ReportQueueManager";
    private static final int MAX_QUEUE_SIZE = 200;  // 防止内存溢出
    private final ConcurrentLinkedQueue<ReportableEntity> memoryQueue = new ConcurrentLinkedQueue<>();
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    private final TaskApiImpl taskApi;
    private final ConnectionChangeReceiver.INetworkMsgHandler mINetworkMsgHandler = this::onNetWorkConnect;

    private static volatile ReportQueueManager instance;

    public static synchronized ReportQueueManager getInstance() {
        if (instance == null) {
            instance = new ReportQueueManager();
        }
        return instance;
    }

    private ReportQueueManager() {
        this.taskApi = new TaskApiImpl();
        ConnectionChangeReceiver.getInstance().addNetworkMsgHandler(mINetworkMsgHandler);
        // 启动定时上报任务
//        scheduler.scheduleAtFixedRate(this::flushQueue, 5, 5, TimeUnit.SECONDS);
    }

    // todo 批量上报，待完善
    private void flushQueue() {
        if (memoryQueue.isEmpty()) return;

        if (!ConnectionChangeReceiver.getInstance().isWifiConnected()) {
            FileLogger.w(TAG, "flushQueue: not wifi connected");
            return;
        }

        List<ReportableEntity> batch = new ArrayList<>();
        while (!memoryQueue.isEmpty() && batch.size() < 50) { // 最多50条/批次
//            batch.add(memoryQueue.poll());
        }



//        taskApiClient.reportSteps(batch, new ReportCallback() {
//            @Override
//            public void onSuccess() {
//                TestDataStorageHelper.getInstance().removePendingReportSync(batch); // 清理已成功数据
//            }
//
//            @Override
//            public void onFailure(List<TestStep> failedSteps) {
//                memoryQueue.addAll(failedSteps); // 失败数据重新入队
//            }
//        });
    }

    public void loadFromDisk() {
        List<ReportableEntity> pendingReports = TestDataStorageHelper.getInstance().loadPendingReportsSync();
        memoryQueue.addAll(pendingReports);
    }

    public void reportEntity(ReportableEntity entity) {
        scheduler.execute(() -> {
            if (!ConnectionChangeReceiver.getInstance().isWifiConnected()) {
                // 非 WiFi 时直接持久化，不入内存队列
                TestDataStorageHelper.getInstance().savePendingReportsSync((Collections.singletonList(entity)));
                FileLogger.w(TAG, "reportEntity: not wifi connected, save to disk");
                return;
            }

            synchronized (memoryQueue) {
                if (memoryQueue.size() >= MAX_QUEUE_SIZE) {
                    TestDataStorageHelper.getInstance().savePendingReportsSync(new ArrayList<>(memoryQueue)); // 持久化溢出数据
                    memoryQueue.clear();
                }
                memoryQueue.offer(entity);
            }

            processQueue();
        });
    }

    private void processQueue() {
        if (!isProcessing.compareAndSet(false, true)) {
            return; // Another task is already processing the queue
        }

        try {
            while (!memoryQueue.isEmpty()) {
                ReportableEntity entity = memoryQueue.poll();
                if (entity != null) {
                    processEntity(entity);
                }
            }
        } finally {
            isProcessing.set(false);
            if (!memoryQueue.isEmpty()) {
                processQueue(); // Trigger processing again if new items were added
            }
        }
    }

    private void processEntity(ReportableEntity entity) {
        Response<ApiResponse> response = null;
        String entityType = "";
        try {
            if (entity instanceof StepResultEntity) {
                entityType = "StepResultEntity";
                response = taskApi.reportStepResultSync((StepResultEntity) entity);
            } else if (entity instanceof TraceResultEntity) {
                entityType = "TraceResultEntity";
                response = taskApi.reportTraceResultSync((TraceResultEntity) entity);
            } else if (entity instanceof TestResultEntity) {
                entityType = "TestResultEntity";
                response = taskApi.reportTestResultSync((TestResultEntity) entity);
            } else if (entity instanceof TestFinishEntity) {
                entityType = "TestFinishEntity";
                response = taskApi.reportTestFinishSync((TestFinishEntity) entity);
            } else if (entity instanceof TestStopEntity) {
                response = taskApi.reportTestStopSync((TestStopEntity) entity);
            } else if (entity instanceof TestStartedEntity) {
                entityType = "TestStartedEntity";
                response = taskApi.reportTestStartedSync((TestStartedEntity) entity);
            } else {
                FileLogger.e(TAG, "processQueue: Unknown entity type: " + entity.getClass().getSimpleName());
                return;
            }
            if (response != null) {
                if (response.isSuccessful()) {
                    FileLogger.i(TAG, "processQueue: reporting " + entityType + " success" + ", response: " + response.body());
                    TestDataStorageHelper.getInstance().removePendingReportSync(entity); // 清理已成功数据
//                        processQueue(); // 递归处理下一条
                } else {
                    FileLogger.e(TAG, "processQueue: reporting " + entityType + " failed" + ", code: " + response.code());
                    memoryQueue.offer(entity); // 失败数据重新入队
                }
            } else {
                FileLogger.e(TAG, "processQueue: reporting " + entityType + " failed" + ", api response is null");
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "processQueue: reporting " + entityType + " failed", e);
        }
    }

    public synchronized ReportableEntity pollEntity() {
        return memoryQueue.poll();
    }

    public synchronized boolean isEmpty() {
        return memoryQueue.isEmpty();
    }

    public synchronized int size() {
        return memoryQueue.size();
    }

    public void onNetWorkConnect(boolean state) {
        FileLogger.i(TAG, "---onNetWorkConnect = " + state);
        if (state) {
            // wifi连接时，才需要上报
            if (ConnectionChangeReceiver.getInstance().isWifiConnected()) {
                FileLogger.i(TAG, "onNetWorkConnect: wifi connected, process report queue");
                processQueue();
            }
        }
    }
}
