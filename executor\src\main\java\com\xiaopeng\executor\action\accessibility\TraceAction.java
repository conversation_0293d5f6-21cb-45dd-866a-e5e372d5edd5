package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.event.TraceEvent;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import org.greenrobot.eventbus.EventBus;

import java.util.Objects;

public class TraceAction extends BaseAction {
    private static final String TAG = "TraceAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String content = context.getStringParam();
        if (Objects.equals(content, "")) {
            throw new ActionException("trace content is null!", FailureCode.SI001);
        }
        FileLogger.i(TAG, "params: " + content);
        // 按照;号分割content，然后根据分割后的内容进行不同的操作
        try {
            String[] contents = content.split(";");
            for (int i = 0; i < contents.length; i++) {
                String[] params = contents[i].split(",");
                if (params.length < 8) {
                    throw new ActionException("trace content is invalid!",FailureCode.SI001);
                }
                String type = params[0].toLowerCase();
                switch (type) {
                    case "can":
                        EventBus.getDefault().post(new TraceEvent.CanEvent(context.getScriptId(), context.getStepId(), i + 1, params[1], params[2], params[3], Integer.parseInt(params[4]), Integer.parseInt(params[5]), Double.parseDouble(params[6]), Integer.parseInt(params[7]), params[8]));
                        FileLogger.i(TAG, "send can trace event: " + params[1] + ", " + params[2] + ", " + params[3] + ", " + params[4] + ", " + params[5] + ", " + params[6] + ", " + params[7] + ", " + params[8]);
                        break;
                    case "lin":
                        EventBus.getDefault().post(new TraceEvent.LinEvent(context.getScriptId(), context.getStepId(), i + 1, params[1], params[2], params[3], Integer.parseInt(params[4]), Integer.parseInt(params[5]), Double.parseDouble(params[6]), Integer.parseInt(params[7]), params[8]));
                        FileLogger.i(TAG, "send lin trace event: " + params[1] + ", " + params[2] + ", " + params[3] + ", " + params[4] + ", " + params[5] + ", " + params[6] + ", " + params[7] + ", " + params[8]);
                        break;
                    case "someip":
                        EventBus.getDefault().post(new TraceEvent.SomeIPEvent(context.getScriptId(), context.getStepId(), i + 1, params[1], params[2], params[3], params[4], Integer.parseInt(params[5]), Integer.parseInt(params[6]), Integer.parseInt(params[7]), params[8]));
                        FileLogger.i(TAG, "send someip trace event: " + params[1] + ", " + params[2] + ", " + params[3] + ", " + params[4] + ", " + params[5] + ", " + params[6] + ", " + params[7] + ", " + params[8]);
                        break;
                    default:
                        throw new ActionException("trace type is invalid! " + type, FailureCode.SI001);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ActionException("parse trace content error! ",FailureCode.SI001,e);
        }
        return TestResult.success("trace content is parsed successfully!");
    }
}
