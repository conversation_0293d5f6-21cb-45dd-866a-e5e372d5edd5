package com.xiaopeng.xpautotest.client.api;

import com.xiaopeng.xpautotest.bean.StepResultEntity;
import com.xiaopeng.xpautotest.bean.TestFinishEntity;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TestStartedEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.bean.TraceResultEntity;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

public interface TaskApiService {
    @GET("task/{taskId}")
    Call<TestTaskBean> getTaskInfo(@Path("taskId") long taskId);

    // 下载测试脚本
    @Streaming
    @GET
    Call<ResponseBody> downloadScript(@Url String fileUrl);

    // 首次启动测试任务
    @POST("task/{taskId}/execute")
    Call<ApiResponse<Long>> startTest(@Path("taskId") long taskId, @Body TestTaskBean taskInfo);

    @POST("task-execution/{taskExecutionId}/cancel")
    Call<ApiResponse> stopTest(@Path("taskExecutionId") long taskExecutionId);

    // 重新测试当前任务
    @POST("task/{taskId}/restart")
    Call<ApiResponse> restartTest(@Path("taskId") long taskId);

    // 开始执行测试任务
    @POST("task-execution/{taskExecutionId}/started")
    Call<ApiResponse> reportTestStarted(@Path("taskExecutionId") long taskId, @Body TestStartedEntity taskExecution);

    // 上报单个用例测试结果
    @POST("task-execution/{taskExecutionId}/reportResult")
    Call<ApiResponse> reportResult(@Path("taskExecutionId") long taskExecutionId, @Body TestResultEntity testResult);

    // 上报单个步骤执行结果
    @POST("task-execution/{taskExecutionId}/reportStepResult")
//    @Headers({
//            "Content-Encoding: gzip",  // 保持 GZIP 压缩
//            "Accept: application/json" // 修改响应类型
//    })
    Call<ApiResponse> reportStepResult(@Path("taskExecutionId") long taskExecutionId, @Body StepResultEntity stepResult);

    // 上报trace解析结果
    @POST("task-execution/{taskExecutionId}/reportTraceResult")
//    @Headers({
//            "Content-Encoding: gzip",  // 保持 GZIP 压缩
//            "Accept: application/json" // 修改响应类型
//    })
    Call<ApiResponse> reportTraceResult(@Path("taskExecutionId") long taskExecutionId, @Body TraceResultEntity traceResult);

    // 结束当前测试任务
    @POST("task-execution/{taskExecutionId}/finish")
    Call<ApiResponse> finishTest(@Path("taskExecutionId") long taskExecutionId, @Body TestFinishEntity testFinishEntity);
}
