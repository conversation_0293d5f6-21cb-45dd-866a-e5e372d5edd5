package com.xiaopeng.executor.action.carapi;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.carmanager.CarClientWrapper;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IEspController;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SetEspPropAction extends BaseAction {
    private static final String TAG = "SetEspPropAction";
    IEspController mIEspController = null;

    @Override
    public void init(CarApiConfig config) {
        super.init(config);
        initControllers();
    }

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String key = (String) context.getStringParam();
        String value = (String) context.getStringParam();
        if (key == null || value == null) {
            throw new ActionException("key or value is null!", FailureCode.SI001);
        }
        boolean res = setEspProp(key, value);
        FileLogger.i(TAG, "key: " + key + ", value: " + value + ", res: " + res);
        if (res) {
            return TestResult.success("ESP set property success: " + key);
        } else {
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB005);
            return TestResult.failure("ESP set property failed: " + key);
        }
    }

    public boolean setEspProp(String key, String value) {
        return mIEspController.setValue(key, value);
    }

    private void initControllers() {
        mIEspController = (IEspController) carClientWrapper.getController(CarClientWrapper.XP_ESP_SERVICE);
    }
}
