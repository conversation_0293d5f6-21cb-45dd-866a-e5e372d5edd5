package com.xiaopeng.xpautotest.community.utils;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Process;
import android.util.Log;

import com.xiaopeng.xpautotest.community.bean.LogCallback;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class FileLogger {
    private static final String TAG = "FileLogger";
    private static final int MAX_LOG_SIZE = 2 * 1024 * 1024; // 2MB
    private static final int FLUSH_INTERVAL = 2000; // 2秒

    private final Handler handler;
    private final ConcurrentLinkedQueue<String> logQueue = new ConcurrentLinkedQueue<>();
    private final AtomicBoolean isWriting = new AtomicBoolean(false);
    private final SimpleDateFormat dateFormat =
            new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    private final SimpleDateFormat timeFormat =
            new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault());

    private String currentExecutionId = null;
    private String currentExecutionLogFilePath = null; 
    private long currentExecutionTimestamp = 0;
    private static LogCallback logCallback;

    private FileLogger() {
        HandlerThread thread = new HandlerThread("LoggerThread",
                Process.THREAD_PRIORITY_BACKGROUND);
        thread.start();
        this.handler = new Handler(thread.getLooper());
        scheduleFlush();
    }

    public static FileLogger getInstance() {
        return Holder.INSTANCE != null ? Holder.INSTANCE :
                (Holder.INSTANCE = new FileLogger());
    }

    public static void setLogCallback(LogCallback callback) {
        logCallback = callback;
    }

    /**
     * 【新增】直接写入一行原始文本，不带任何格式。
     * 主要用于写入 [SCRIPT_START] 这样的结构化标签。
     * @param text 要写入的原始文本
     */
    public void logRawText(String text) {
        String logEntry = text + "\n";
        logQueue.offer(logEntry);
        tryFlush();
    }

    private static class Holder {
        static FileLogger INSTANCE;
    }

    public void log(int level, String tag, String message) {
        String levelString = getLevelString(level);
        String logEntry = String.format(Locale.US, "%s %s %s %s\n",
                timeFormat.format(new Date()),
                levelString,
                tag,
                message);

        logQueue.offer(logEntry);

        // Notify the callback
        if (logCallback != null) {
            logCallback.onLogGenerated(levelString, tag + " " + message);
        }

        tryFlush(); 
    }

    public static void i(String tag, String message) {
        FileLogger.getInstance().log(Log.INFO, tag, message);
        Log.i(tag, message);
    }

    public static void i(String tag, String message, boolean isShow ) {
        FileLogger.getInstance().log(Log.INFO, tag, message);
        if (isShow) {
            Log.i(tag, message);
        }
    }

    public static void d(String tag, String message) {
        FileLogger.getInstance().log(Log.DEBUG, tag, message);
        Log.d(tag, message);
    }

    public static void w(String tag, String message) {
        FileLogger.getInstance().log(Log.WARN, tag, message);
        Log.w(tag, message);
    }

    public static void e(String tag, String message) {
        FileLogger.getInstance().log(Log.ERROR, tag, message);
        Log.e(tag, message);
    }

    public static void e(String tag, String message, Exception e) {
        FileLogger.getInstance().log(Log.ERROR, tag, message + "\n" + e.toString());
        Log.e(tag, message, e);
    }

    private void tryFlush() {
        if (logQueue.size() > 10 && !isWriting.get()) {
            handler.post(this::flushToFile);
        }
    }

    private synchronized void flushToFile() {
        if (isWriting.getAndSet(true)) return;

        File logFile = null;
        try {
            if (currentExecutionId != null && currentExecutionLogFilePath != null) {
                logFile = new File(currentExecutionLogFilePath);
                File logDir = logFile.getParentFile();
                if (logDir != null && !logDir.exists()) {
                    logDir.mkdirs();
                }
                if (!logFile.exists()) {
                    try {
                        logFile.createNewFile();
                    } catch (IOException e) {
                        android.util.Log.e(TAG, "Cannot create execution log file during flush: " + logFile.getAbsolutePath(), e);
                        isWriting.set(false);
                        return;
                    }
                }
            } else {
                logFile = getDefaultDailyLogFile();
            }

            if (logFile == null) {
                 android.util.Log.e(TAG, "Target log file is null. Cannot write logs.");
                 isWriting.set(false);
                 return;
            }

            try (BufferedWriter writer = new BufferedWriter(
                    new FileWriter(logFile, true))) { 
                String logEntry;
                int count = 0;
                while ((logEntry = logQueue.poll()) != null && count++ < 100) {
                    writer.write(logEntry);
                }
            }
        } catch (IOException e) {
            android.util.Log.e(TAG, "Write log failed for " + (logFile != null ? logFile.getAbsolutePath() : "unknown file"), e);
        } finally {
            isWriting.set(false);
        }
    }

    public synchronized void startExecutionLog(String executionId, long startTimestamp) {

        if (this.currentExecutionId != null && !this.currentExecutionId.equals(executionId)) {
             handler.post(this::flushToFile);
        } else if (this.currentExecutionId != null && this.currentExecutionId.equals(executionId)){
             return;
        }

        this.currentExecutionId = executionId;
        this.currentExecutionTimestamp = startTimestamp;
        File logDir = new File(Constant.AUTOTEST_LOG_PATH);
        if (!logDir.exists()) {
            logDir.mkdirs();
        }
        this.currentExecutionLogFilePath = new File(logDir, executionId + "_" + this.currentExecutionTimestamp + ".log").getAbsolutePath();
        Log.i(TAG, "Started execution log for ID: " + executionId + " with timestamp: " + startTimestamp + ", file: " + this.currentExecutionLogFilePath);
        try {
            File newLogFile = new File(this.currentExecutionLogFilePath);
            if (!newLogFile.exists()) {
                newLogFile.createNewFile();
            }
        } catch (IOException e) {
            Log.e(TAG, "Failed to create new execution log file: " + this.currentExecutionLogFilePath, e);
        }
    }

    public synchronized void finishExecutionLog() {
        if (this.currentExecutionId != null && this.currentExecutionLogFilePath != null) {
            if (!logQueue.isEmpty()) {
                flushToFile(); 
            }
        } else {
            Log.w(TAG, "finishExecutionLog called but no active execution log.");
        }
    }

    public synchronized String zipAndRetrieveExecutionLogPath(String executionId) {
        if (this.currentExecutionId == null || 
            !this.currentExecutionId.equals(executionId) || 
            this.currentExecutionLogFilePath == null) {
            return null;
        }
        finishExecutionLog();
        String logFilePathForZipping = this.currentExecutionLogFilePath;
        File logFile = new File(logFilePathForZipping);
        if (!logFile.exists() || !logFile.isFile()) {
            Log.w(TAG, "Execution log file does not exist: " + logFilePathForZipping);
            return null;
        }
        File logDir = logFile.getParentFile();
        if (logDir == null) { 
            logDir = new File(Constant.AUTOTEST_LOG_PATH);
        }
        if (!logDir.exists()) {
            if (!logDir.mkdirs()) {
                Log.e(TAG, "Could not create directory for zip file: " + logDir.getAbsolutePath());
                return null;
            }
        }
        String zipFileName = executionId + "_" + this.currentExecutionTimestamp + ".zip";
        File zipFile = new File(logDir, zipFileName);
        String resultPath = null;
        try {
            boolean compressionSuccess = CompressUtils.zipSingleFile(logFile, zipFile);
            if (compressionSuccess) {
                resultPath = zipFile.getAbsolutePath();
            } else {
                Log.e(TAG, "Failed to zip execution log");
            }
        } catch (Exception e) {
            Log.e(TAG, "Unexpected exception during zipping process for log: " + logFilePathForZipping, e);
        } finally {
            this.currentExecutionId = null;
            this.currentExecutionLogFilePath = null;
            this.currentExecutionTimestamp = 0;
        }
        return resultPath;
    }

    private File getDefaultDailyLogFile() throws IOException {
        File logDir = new File(Constant.AUTOTEST_LOG_PATH);
        if (!logDir.exists() && !logDir.mkdirs()) {
            throw new IOException("Cannot create log directory: " + logDir.getAbsolutePath());
        }
        String dateStr = dateFormat.format(new Date());
        File logFile = new File(logDir, dateStr + ".log");
        if (logFile.exists() && logFile.length() > MAX_LOG_SIZE) {
            String timestamp = new SimpleDateFormat("HHmmss", Locale.getDefault()).format(new Date());
            File rotatedFile = new File(logDir, dateStr + "_" + timestamp + ".log");
            if (!rotatedFile.exists()){
                 if(!rotatedFile.createNewFile()){
                     throw new IOException("Cannot create rotated log file: " + rotatedFile.getAbsolutePath());
                 }
            }
            return rotatedFile; 
        }
        if (!logFile.exists() && !logFile.createNewFile()) {
            throw new IOException("Cannot create log file: " + logFile.getAbsolutePath());
        }
        return logFile;
    }

    private void scheduleFlush() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                flushToFile();
                scheduleFlush();
            }
        }, FLUSH_INTERVAL);
    }

    private String getLevelString(int level) {
        switch (level) {
            case android.util.Log.VERBOSE: return "V";
            case android.util.Log.DEBUG:   return "D";
            case android.util.Log.INFO:    return "I";
            case android.util.Log.WARN:   return "W";
            case android.util.Log.ERROR:  return "E";
            default: return "?";
        }
    }
}
