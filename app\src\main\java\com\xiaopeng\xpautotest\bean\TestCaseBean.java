package com.xiaopeng.xpautotest.bean;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class TestCaseBean {

    public static final String LOCAL_FIRST = "local";

    @SerializedName("caseId")
    private long caseId;

    @SerializedName("scriptId")
    private long scriptId;

    @SerializedName("name")
    private String name;
    @SerializedName("steps")
    private String steps;

    @SerializedName("lastRunTime")
    private String lastRunTime;

    @SerializedName("status")
    private int status = -1;

    @SerializedName("isPass")
    private boolean isPass;

    @SerializedName("isRunning")
    private boolean isRunning;

    @SerializedName("result")
    private String result;

    public long getCaseId() {
        return caseId;
    }

    public long getScriptId() {
        return scriptId;
    }

    public String getName() {
        if (!TextUtils.isEmpty(name)) {
            name = name.trim();
        }
        return name;
    }

    public String getSteps() {
        return steps;
    }

    public int getStatus() {
        return status;
    }

    public boolean getPass() {
        return isPass;
    }

    public boolean isRunning() {
        return isRunning;
    }

    public String getLastRunTime() {
        return lastRunTime;
    }

    public String getResult() {
        return result;
    }
}
