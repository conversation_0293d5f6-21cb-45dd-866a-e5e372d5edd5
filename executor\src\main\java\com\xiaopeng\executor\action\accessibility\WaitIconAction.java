package com.xiaopeng.executor.action.accessibility;

import android.os.SystemClock;
import android.view.accessibility.AccessibilityNodeInfo;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.system.ImageRecognition;

public class WaitIconAction extends BaseAction {
    private static final String TAG = "WaitIconAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String templateIconName = (String) context.getStringParam();
        if (templateIconName == null) {
            throw new ActionException("template icon is null!", FailureCode.SI001);
        }
        long timeout = context.getIntParam();
        String region = context.getStringParam();
        double scale = context.getDoubleParam();
        int minMatches = context.getIntParam();
        if (!templateIconName.endsWith(".png")) {
            templateIconName += ".png";
        }

        FileLogger.i(TAG, "templateIconName: " + templateIconName + ", dropRegion: " + region + ", scaleFactor: " + scale + ", minGoodMatches: " + minMatches);
        String templateImagePath = Constant.AUTOTEST_TEMPLATE_ICON_PATH + templateIconName;

        TestResult.ActionArtifacts actionArtifacts = null;
        ImageRecognition imageRecognition = ImageRecognition.getInstance();
        long endTime = System.currentTimeMillis() + timeout * 1000;
        while (System.currentTimeMillis() < endTime) {
            // 屏幕截图
            String screenImageName = this.service.screenShotSyn(String.valueOf(System.currentTimeMillis()));
            if (screenImageName == null || screenImageName.isEmpty()) {
                return TestResult.failure("Screen captured failed!", new TestResult.ActionArtifacts("",""));
            }
            actionArtifacts = new TestResult.ActionArtifacts(screenImageName,"");
            String screenImagePath = Constant.AUTOTEST_IMAGE_PATH + screenImageName;
            ImageRecognition.MatchResult result = imageRecognition.fastMatch(screenImagePath, templateImagePath, region, scale, minMatches);
            if (result.isMatched()) {
                return TestResult.success("match icon successfully.", actionArtifacts);
            } else {
                FileLogger.w(TAG, "match icon failed, will retry. " + result.getMessage());
            }
            SystemClock.sleep(100);
        }
        if (actionArtifacts == null) {
            actionArtifacts = this.service.screenShotandDumpUI(String.valueOf(System.currentTimeMillis()));
        }
        return TestResult.failure("match icon timeout!", actionArtifacts);
    }
}
