package com.xiaopeng.xpautotest.community.test;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.io.IOException;
import java.util.ArrayList;

/**
 * TestScene的单元测试
 * 重点测试addStep方法的注释和关键字解析逻辑
 */
public class TestSceneTest {
    
    private TestScene testScene;
    private static final long SCRIPT_ID = 12345L;
    
    @Before
    public void setUp() {
        testScene = new TestScene(SCRIPT_ID, "Scene: 1");
        testScene.setPhaseType("Procedure");
    }
    
    /**
     * 测试基本命令（无注释）
     */
    @Test
    public void testBasicCommand() throws IOException {
        testScene.addStep(1, "ClickById button1");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button1", step.getAction());
        assertEquals("", step.getComments()); // 默认注释为空字符串
        assertEquals("", step.getKeywords());
    }
    
    /**
     * 测试同行注释
     */
    @Test
    public void testInlineComment() throws IOException {
        testScene.addStep(1, "ClickById button1 # 点击登录按钮");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button1", step.getAction());
        assertEquals("点击登录按钮", step.getComments());
        assertEquals("", step.getKeywords());
    }
    
    /**
     * 测试同行关键字
     */
    @Test
    public void testInlineKeyword() throws IOException {
        testScene.addStep(1, "ClickById button1 # AW-001");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button1", step.getAction());
        assertEquals("", step.getComments()); // 关键字不作为注释
        assertEquals("AW-001", step.getKeywords());
    }
    
    /**
     * 测试同行注释+关键字
     */
    @Test
    public void testInlineCommentAndKeyword() throws IOException {
        testScene.addStep(1, "ClickById button1 # 点击登录按钮 AW-001");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button1", step.getAction());
        assertEquals("点击登录按钮 AW-001", step.getComments());
        assertEquals("", step.getKeywords());
    }
    
    /**
     * 测试纯注释行（不创建步骤）
     */
    @Test
    public void testPureCommentLine() throws IOException {
        testScene.addStep(1, "# 这是一个纯注释行");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(0, steps.size()); // 纯注释行不创建步骤
    }
    
    /**
     * 测试上一行注释功能
     */
    @Test
    public void testPreviousLineComment() throws IOException {
        // 第一行：纯注释
        testScene.addStep(1, "# 点击登录按钮");
        // 第二行：命令（应该使用上一行的注释）
        testScene.addStep(2, "ClickById login_button");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size()); // 只有第二行创建了步骤
        
        TestStep step = steps.get(0);
        assertEquals("ClickById login_button", step.getAction());
        assertEquals("点击登录按钮", step.getComments()); // 使用了上一行的注释
        assertEquals("", step.getKeywords());
    }
    
    /**
     * 测试上一行关键字功能
     */
    @Test
    public void testPreviousLineKeyword() throws IOException {
        // 第一行：纯关键字注释
        testScene.addStep(1, "# AW-001");
        // 第二行：命令（应该使用上一行的关键字）
        testScene.addStep(2, "ClickById login_button");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById login_button", step.getAction());
        assertEquals("", step.getComments()); // 关键字不作为注释
        assertEquals("AW-001", step.getKeywords()); // 使用了上一行的关键字
    }
    
    /**
     * 测试当前行注释覆盖上一行注释
     */
    @Test
    public void testCurrentLineOverridesPrevious() throws IOException {
        // 第一行：纯注释
        testScene.addStep(1, "# 上一行注释");
        // 第二行：命令+当前行注释（应该覆盖上一行）
        testScene.addStep(2, "ClickById button # 当前行注释");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button", step.getAction());
        assertEquals("当前行注释", step.getComments()); // 使用当前行注释，不是上一行的
        assertEquals("", step.getKeywords());
    }
    
    /**
     * 测试多个#分隔的复杂情况
     */
    @Test
    public void testMultipleHashSeparators() throws IOException {
        testScene.addStep(1, "ClickById button # 第一个注释 # AW-001 # 其他内容");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button", step.getAction());
        assertEquals("第一个注释", step.getComments()); // 第二部分作为注释
        assertEquals("", step.getKeywords()); // #分割最后的那部分不包含AW-开头，关键字为空字符串
    }
    
    /**
     * 测试状态清空机制
     */
    @Test
    public void testStateClearingMechanism() throws IOException {
        // 第一行：设置注释和关键字
        testScene.addStep(1, "# 第一个注释 AW-001");
        // 第二行：使用状态
        testScene.addStep(2, "ClickById button1");
        // 第三行：应该没有注释和关键字（状态已清空）
        testScene.addStep(3, "ClickById button2");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(2, steps.size());
        
        // 第一个步骤使用了注释和关键字
        TestStep step1 = steps.get(0);
        assertEquals("ClickById button1", step1.getAction());
        assertEquals("第一个注释 AW-001", step1.getComments());
        assertEquals("", step1.getKeywords()); // 匹配不到关键字
        
        // 第二个步骤应该没有注释和关键字（状态已清空）
        TestStep step2 = steps.get(1);
        assertEquals("ClickById button2", step2.getAction());
        assertEquals("", step2.getComments()); // 默认值
        assertEquals("", step2.getKeywords()); // 空字符串
    }
    
    /**
     * 测试连续的纯注释行
     */
    @Test
    public void testConsecutiveCommentLines() throws IOException {
        testScene.addStep(1, "# 第一个注释");
        testScene.addStep(2, "# 第二个注释"); // 应该覆盖第一个
        testScene.addStep(3, "ClickById button");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button", step.getAction());
        assertEquals("第二个注释", step.getComments()); // 使用最后一个注释
        assertEquals("", step.getKeywords());
    }
    
    /**
     * 测试空字符串和空白字符
     */
    @Test
    public void testEmptyAndWhitespaceHandling() throws IOException {
        testScene.addStep(1, "ClickById button #   "); // 空注释
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button", step.getAction());
        assertEquals("", step.getComments()); // 空注释被处理为空字符串
        assertEquals("", step.getKeywords());
    }
    
    /**
     * 测试特殊字符处理
     */
    @Test
    public void testSpecialCharacterHandling() throws IOException {
        testScene.addStep(1, "ClickById button # 包含特殊字符的注释!@#$%^&*()");
        
        ArrayList<TestStep> steps = testScene.getStepList();
        assertEquals(1, steps.size());
        
        TestStep step = steps.get(0);
        assertEquals("ClickById button", step.getAction());
        assertEquals("包含特殊字符的注释!@#$%^&*()", step.getComments());
        assertEquals("", step.getKeywords());
    }
}
