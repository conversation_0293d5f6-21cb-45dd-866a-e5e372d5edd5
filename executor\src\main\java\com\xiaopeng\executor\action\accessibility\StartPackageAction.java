package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class StartPackageAction extends BaseAction {
    private static final String TAG = "StartPackageAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String packageName = context.getStringParam();
        if (packageName == null || packageName.isEmpty()) {
            throw new ActionException("package name is null or empty!", FailureCode.SI001);
        }
        double waitTime = context.getDoubleParam();
        FileLogger.i(TAG, "begin startPackage " + packageName);
        boolean result = this.service.startPackage(packageName, waitTime);
        if (!result) {
            return TestResult.failure("Failed to start package: " + packageName);
        }
        return TestResult.success("Successfully started package: " + packageName);
    }
}
