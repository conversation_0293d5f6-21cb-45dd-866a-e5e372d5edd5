package com.xiaopeng.xpautotest.utils;

import android.text.TextUtils;

import com.xiaopeng.screen.ScreenDevice;
import com.xiaopeng.screen.ScreenTaskManagerFactory;
import com.xiaopeng.xpautotest.App;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.view.WindowManagerFactory;

public class ScreenHelper {
    private static final String TAG = "ScreenHelper";
    private final ScreenTaskManagerFactory mScreenTaskManagerFactory;
    private String mAreaType;
    private Callback mCallback;
    private boolean mIsFullScreen;

    public String getAreaType() {
        return mAreaType;
    }

    public void setCallback(Callback callback) {
        mCallback = callback;
    }

    public interface Callback {
        void onScreenAreaChanged(String oldArea, String newArea);
    }

    private ScreenHelper() {
        mScreenTaskManagerFactory = ScreenTaskManagerFactory.get();
    }

    private static class SingletonHolder {
        private static final ScreenHelper sInstance = new ScreenHelper();
    }

    public static ScreenHelper getInstance() {
        return SingletonHolder.sInstance;
    }

    public void init() {
        if (mScreenTaskManagerFactory != null) {
            mAreaType = mScreenTaskManagerFactory.getScreenAreaType(App.getInstance().getPackageName());
            Log.i(TAG, String.format("init, areaType:%s, hash:%s", mAreaType, App.getInstance().hashCode()));
            mIsFullScreen = isFullScreen(mAreaType);
            Log.i(TAG, String.format("init, mIsFullScreen:%s, hash:%s", mIsFullScreen, App.getInstance().hashCode()));
        }
    }

    // 获取当前应用的屏幕id
    public int getScreenId() {
        int screenId = WindowManagerFactory.ID_SHARED_PRIMARY;
        if (mScreenTaskManagerFactory != null) {
            screenId = mScreenTaskManagerFactory.getScreenId(App.getInstance());
        }
        if (screenId == -1) {
            screenId = WindowManagerFactory.ID_SHARED_PRIMARY;
        }
        return screenId;
    }

    // 判断是否是全屏
    private boolean isFullScreen(String screenAreaType) {
        Log.d(TAG, "isFullScreen pass in type:" + screenAreaType);
        return ScreenTaskManagerFactory.WindowLayoutParams.TYPE_AREA_IMMERSION.equals(screenAreaType) ||
                ScreenTaskManagerFactory.WindowLayoutParams.TYPE_AREA_LOGICAL.equals(screenAreaType);
    }

    // 判断是否是娱乐屏
    public boolean isEntertainmentScreen() {
        return getScreenId() == ScreenDevice.SCREEN_SECONDARY ||
                getScreenId() == ScreenDevice.SCREEN_FOURTH;
    }

    // 判断是否是宽屏
    public boolean isWideScreen() {
        return mIsFullScreen || isEntertainmentScreen();
    }

    // 更新屏幕区域
    public void tryUpdateScreenArea(String tag) {
        if (mCallback == null) {
            return;
        }
        String areaType = mScreenTaskManagerFactory.getScreenAreaType(App.getInstance().getPackageName());
        Log.i(TAG, String.format("update(%s), areaType:%s, mAreaType:%s, hash:%s", tag, areaType, mAreaType, App.getInstance().hashCode()));
        if (TextUtils.isEmpty(areaType)) {
            return;
        }
        // 去重
        if (areaType.equals(mAreaType)) {
            return;
        }
        if (mCallback != null) {
            mIsFullScreen = isFullScreen(areaType);
            mCallback.onScreenAreaChanged(mAreaType, areaType);
        }
        mAreaType = areaType;
    }
}
