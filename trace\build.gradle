plugins {
    id 'com.android.library'
}

android {
    namespace 'com.xiaopeng.xpautotest.trace'
    compileSdk 34

    defaultConfig {
        minSdk 28

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            minifyEnabled false
            buildConfigField 'Boolean', 'MOCK', project.hasProperty('MOCK') ? project.getProperty('MOCK') : 'false'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField 'Boolean', 'MOCK', 'false'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation project(':community')
    implementation('com.xiaopeng.lib:lib_utils:1.7.5.9'){
        changing = true
        exclude group: 'org.apache.commons', module: 'commons-compress'
    }
    implementation 'org.greenrobot:eventbus:3.3.1'
}