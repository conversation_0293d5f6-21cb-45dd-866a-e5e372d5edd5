package com.xiaopeng.executor.core;

import com.xiaopeng.executor.bean.ExecutionState;
import com.xiaopeng.executor.bean.ExecutorContext;
import com.xiaopeng.xpautotest.community.test.TestScene;
import com.xiaopeng.xpautotest.community.test.TestStep;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import java.util.ArrayList;
import java.util.List;


/**
 * 场景执行单元
 * 
 * 包装TestScene，保持原有的场景执行逻辑：
 * 1. 场景级循环控制
 * 2. 重试机制（-try模式）
 * 3. 步骤顺序执行
 * 4. 失败处理策略
 */
public class SceneUnit implements ExecutableUnit {
    private static final String TAG = "SceneUnit";
    private final TestScene scene;
    private final List<ExecutableUnit> executionUnits;

    public SceneUnit(TestScene scene) {
        this.scene = scene;
        // 解析TestStep列表，识别if-else结构并创建相应的ExecutableUnit
        this.executionUnits = parseExecutionUnits(scene.getStepList(), scene.getScriptId(), scene.getPhaseType());
    }

    /**
     * 解析执行单元列表，识别if-else结构
     */
    private List<ExecutableUnit> parseExecutionUnits(List<TestStep> steps, long scriptId, String phaseType) {
        List<ExecutableUnit> units = new ArrayList<>();

        // 将TestStep列表转换为字符串列表，用于ConditionalParser
        List<String> stepLines = new ArrayList<>();
        for (TestStep step : steps) {
            stepLines.add(step.getStep());
        }

        int i = 0;
        while (i < stepLines.size()) {
            String line = stepLines.get(i).trim();

            if (ConditionalParser.isIfKeyword(line)) {
                // 解析if-else块
                try {
                    ConditionalParser.ParseResult result = ConditionalParser.parseConditionalBlock(
                        stepLines, i, scriptId, phaseType);
                    units.add(result.getConditionalUnit());
                    i = result.getEndIndex() + 1; // 跳过整个if-else块
                } catch (Exception e) {
                    FileLogger.e(TAG, "Failed to parse conditional block at step " + i + ": " + line, e);
                    throw e;
//                    // 解析失败时，将其作为普通步骤处理
//                    TestStep step = steps.get(i);
//                    units.add(new StepUnit(step));
//                    i++;
                }
            } else {
                // 普通步骤
                TestStep step = steps.get(i);
                units.add(new StepUnit(step));
                i++;
            }
        }

        return units;
    }
    
    @Override
    public boolean execute(ExecutorContext context) {
        ExecutionState state = context.getExecutionState();
        int loopIndex = state.getScriptLoopIndex();
        int sceneIndex = state.getCurrentSceneIndex();

        FileLogger.i(TAG, "begin to run TestScene, " + scene, false);

        for (int k = 0; k < scene.getLoopTime(); k++) {
            boolean lastUnitResult = false;
            int sceneLoopIndex = k + 1;

            // 更新执行状态：保持当前脚本循环、阶段索引和场景索引，更新场景循环索引
            context.updateExecutionLocation(loopIndex, state.getCurrentPhaseIndex(), sceneIndex, sceneLoopIndex, 0);

            for (int l = 0; l < executionUnits.size(); l++) {
                ExecutableUnit unit = executionUnits.get(l);

                // 更新执行状态：更新步骤索引
                context.updateExecutionLocation(loopIndex, state.getCurrentPhaseIndex(), sceneIndex, sceneLoopIndex, l + 1);

                // 执行单元
                boolean unitResult = unit.execute(context);
                lastUnitResult = unitResult;

                if (!unitResult) {
                    if (scene.isTryMode() && sceneLoopIndex < scene.getLoopTime()) {
                        FileLogger.i(TAG, "Unit failed in try mode, breaking current loop: " + unit.getUnitId());
                        break;
                    } else {
                        FileLogger.e(TAG, "Unit execution failed: " + unit.getUnitId());
                        return false;
                    }
                }
            }

            if (scene.isTryMode() && lastUnitResult) {
                FileLogger.i(TAG, "scene-" + sceneIndex + " loop " + (k + 1) + " times successful");
                break;
            }
        }

        return true;
    }
    
    @Override
    public String getUnitId() {
        return "SceneUnit[" + scene.getLoopTime() + (scene.isTryMode() ? "-try" : "") + "]";
    }
}
