package com.xiaopeng.xpautotest.client;

import com.xiaopeng.xpautotest.client.interceptor.AuthInterceptor;
import com.xiaopeng.xpautotest.client.interceptor.ErrorInterceptor;
import com.xiaopeng.xpautotest.client.interceptor.LoggingInterceptor;
import com.xiaopeng.xpautotest.client.interceptor.RetryInterceptor;
import com.xiaopeng.xpautotest.constant.EnvironmentConfig;

import java.util.concurrent.TimeUnit;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

public class RetrofitClient {
    private static Retrofit retrofit;

    public static Retrofit getInstance() {
        if (retrofit == null) {
            retrofit = new Retrofit.Builder()
                    .baseUrl(EnvironmentConfig.getInstance().getBaseUrl())
//                    .addConverterFactory(GsonConverterFactory.create())
                    .addConverterFactory(GsonConverterFactory.create(GsonFactory.create()))
                    .addCallAdapterFactory(RxJava2CallAdapterFactory.create())      // 使用RxJava异步
                    .client(createOkHttpClient())
                    .build();
        }
        return retrofit;
    }

    private static OkHttpClient createOkHttpClient() {
        return new OkHttpClient.Builder()
                .addInterceptor(new AuthInterceptor())      // 授权拦截器
                .addInterceptor(new LoggingInterceptor())   // 日志拦截器
                .addInterceptor(new ErrorInterceptor())     // 错误处理拦截器
                .addInterceptor(new RetryInterceptor(3))        // 重试拦截器
//                .addInterceptor(new GzipRequestInterceptor())   // Gzip压缩请求拦截器
                .connectTimeout(15, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
                .build();
    }
}
