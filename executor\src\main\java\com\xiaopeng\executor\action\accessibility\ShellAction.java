package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ShellAction extends BaseAction {
    private static final String TAG = "ShellAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String command = context.getAllParams();
        if (command == null) {
            throw new ActionException("shell comman is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "executing command: " + command, false);
        return shell(command, null);
    }

    public TestResult shell(String command, String expectValue) {
        CMDUtils.CMD_Result cmdResult = CMDUtils.runCMD(command, true, true);
        if (cmdResult == null) {
            String errorMsg = "cmd result is null";
            FileLogger.e(TAG, errorMsg);
            return TestResult.failure(errorMsg);
        }
        if (cmdResult.resultCode != 0) {
            String errorMsg = "cmd result is error!" + cmdResult.error;
            FileLogger.e(TAG, errorMsg);
            return TestResult.failure(errorMsg+ cmdResult.error);
        }
        if (expectValue != null && !expectValue.equals(getRealValue(cmdResult.success))) {
            String errorMsg = "cmd result is not equal! " + cmdResult.success;
            FileLogger.e(TAG, errorMsg);
            return TestResult.failure(errorMsg);
            }

        String successMsg = "Shell command success: " + cmdResult.success;
        FileLogger.d(TAG, successMsg);
        return TestResult.success(successMsg);
    }

    public String getRealValue(String input) {
        Pattern pattern = Pattern.compile("=\\s*(\\d+),");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
}
