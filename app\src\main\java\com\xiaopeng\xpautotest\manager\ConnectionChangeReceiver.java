package com.xiaopeng.xpautotest.manager;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;

import com.xiaopeng.lib.utils.NetUtils;
import com.xiaopeng.xpautotest.App;
import com.xiaopeng.xpautotest.community.utils.Log;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 网络状态变化监听
 * */
public class ConnectionChangeReceiver extends BroadcastReceiver {
    private static final String TAG = "ConnectionChangeReceiver";
    private boolean mRegistered;
    public static boolean sConnected;
    private ConnectivityManager mConnMgr;
    private NetworkCallbackImpl mNetworkCallback;
    private final List<INetworkMsgHandler> mNetworkHandlers = new ArrayList<>();
    private final ExecutorService backgroundExecutor = Executors.newSingleThreadExecutor();

    private ConnectionChangeReceiver() {
    }

    private static class SingletonHolder {
        static ConnectionChangeReceiver sInstance = new ConnectionChangeReceiver();
    }

    public static ConnectionChangeReceiver getInstance() {
        return SingletonHolder.sInstance;
    }

    public synchronized void addNetworkMsgHandler(INetworkMsgHandler handler) {
        if (handler != null && !mNetworkHandlers.contains(handler)) {
            mNetworkHandlers.add(handler);
        }
    }

    public synchronized void removeNetworkMsgHandler(INetworkMsgHandler handler) {
        if (handler != null) {
            mNetworkHandlers.remove(handler);
        }
    }

    public interface INetworkMsgHandler {
        void onConnected(boolean isConnected);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        boolean noConnectivity;
        if (intent != null) {
            noConnectivity = intent.getBooleanExtra(ConnectivityManager.EXTRA_NO_CONNECTIVITY, false);
        } else {
            noConnectivity = false;
        }
        sConnected = !noConnectivity;
        Log.i(TAG, "onReceive noConnectivity=" + noConnectivity + ",intent=" + intent);
        notifyHandlers(sConnected);
    }

    private synchronized void notifyHandlers(boolean isConnected) {
        for (INetworkMsgHandler handler : mNetworkHandlers) {
            if (handler != null) {
                handler.onConnected(isConnected);
            }
        }
    }

    @SuppressLint("MissingPermission")
    public void registerReceiver() {
        if (!mRegistered) {
            mNetworkCallback = new NetworkCallbackImpl();
            NetworkRequest.Builder builder = new NetworkRequest.Builder();
            NetworkRequest request = builder.build();
            mConnMgr = (ConnectivityManager) App.getInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (mConnMgr != null) {
                mConnMgr.registerNetworkCallback(request, mNetworkCallback);
            }
            mRegistered = true;
        }
    }

    public void unregisterReceiver() {
        mNetworkHandlers.clear();
        if (mRegistered) {
            if (mConnMgr != null) {
                mConnMgr.unregisterNetworkCallback(mNetworkCallback);
                mConnMgr = null;
            }
            mRegistered = false;
        }
    }

    private class NetworkCallbackImpl extends ConnectivityManager.NetworkCallback {
        @Override
        public void onAvailable(Network network) {
            super.onAvailable(network);
            backgroundExecutor.execute(() -> {
                sConnected = ConnectionChangeReceiver.this.hasNetwork();
                boolean networkAvailable = NetUtils.isNetworkAvailable(App.getInstance());
                Log.i(TAG, "onAvailable noConnectivity=" + !sConnected + ",networkAvailable=" + networkAvailable);
                notifyHandlers(sConnected);
            });
        }

        @Override
        public void onLost(Network network) {
            super.onLost(network);
            backgroundExecutor.execute(() -> {
                sConnected = ConnectionChangeReceiver.this.hasNetwork();
                boolean networkAvailable = NetUtils.isNetworkAvailable(App.getInstance());
                Log.i(TAG, "onLost noConnectivity=" + !sConnected + ",networkAvailable=" + networkAvailable);
                notifyHandlers(sConnected);
            });
        }

        @Override
        public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities);
        }
    }

    @SuppressLint("MissingPermission")
    private boolean hasNetwork() {
        if (mConnMgr != null) {
            NetworkInfo activeNetworkInfo = mConnMgr.getActiveNetworkInfo();
            if (activeNetworkInfo != null) {
                return activeNetworkInfo.isConnected();
            }
        }
        return false;
    }

    /**
     * 判断是否连接到wifi
     * */
    public boolean isWifiConnected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10 and above
            Network network = mConnMgr.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities capabilities = mConnMgr.getNetworkCapabilities(network);
                Log.i(TAG, "isWifiConnected capabilities=" + capabilities +
                        ",hasTransport=" + (capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)));
                return capabilities != null && (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET));
            }
        }else {
            NetworkInfo activeNetwork = mConnMgr.getActiveNetworkInfo();
            Log.i(TAG, "isWifiConnected activeNetwork=" + activeNetwork +
                    ",isConnected=" + (activeNetwork != null && activeNetwork.isConnected()) +
                    ",type=" + (activeNetwork != null ? activeNetwork.getType() : "null"));
            return activeNetwork != null &&
                    activeNetwork.isConnected() &&
                    (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI || activeNetwork.getType() == ConnectivityManager.TYPE_ETHERNET);
        }
        return false;
    }

    // 判断是否连接到指定的wifi
    public boolean isConnectedToXPAUTO(String ssid) {
        WifiManager wifiManager = (WifiManager)
                App.getInstance().getSystemService(Context.WIFI_SERVICE);
        if (wifiManager == null) return false;

        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        return wifiInfo != null &&
                wifiInfo.getSSID().equals("\"" + ssid + "\"");
    }

}

