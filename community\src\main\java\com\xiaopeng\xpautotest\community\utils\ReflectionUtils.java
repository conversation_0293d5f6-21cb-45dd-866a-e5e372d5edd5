package com.xiaopeng.xpautotest.community.utils;

import java.lang.reflect.Field;

/**
 * 简洁的反射工具类
 */
public class ReflectionUtils {

    /**
     * 获取字段值
     */
    public static Object getField(Object obj, String fieldName) {
        if (obj == null || fieldName == null) return null;

        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取字段值，支持类型转换
     */
    @SuppressWarnings("unchecked")
    public static <T> T getField(Object obj, String fieldName, Class<T> type) {
        Object value = getField(obj, fieldName);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 链式字段访问（如 "field1.field2.field3"）
     */
    public static <T> T getNestedField(Object obj, String fieldPath, Class<T> type) {
        Object value = getNestedField(obj, fieldPath);
        if (value != null && type.isInstance(value)) {
            return type.cast(value);
        }
        return null;
    }

    /**
     * 链式字段访问
     */
    public static Object getNestedField(Object obj, String fieldPath) {
        if (obj == null || fieldPath == null) return null;

        Object current = obj;
        for (String fieldName : fieldPath.split("\\.")) {
            current = getField(current, fieldName);
            if (current == null) return null;
        }
        return current;
    }
}
