package com.xiaopeng.xpautotest.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.adapter.TestCaseAdapter;
import com.xiaopeng.xpautotest.helper.DialogHelper;
import com.xiaopeng.xpautotest.manager.TestManager;
import com.xiaopeng.xpautotest.model.ITestDataState;
import com.xiaopeng.xpautotest.model.ITestSuiteItem;
import com.xiaopeng.xpautotest.model.TestCaseItem;
import com.xiaopeng.xpautotest.viewmodel.SuiteViewModel;
import com.xiaopeng.xui.app.XDialogInterface;
import com.xiaopeng.xui.app.XToast;
import com.xiaopeng.xui.widget.XButton;
import com.xiaopeng.xui.widget.XTextView;

import java.util.ArrayList;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class TestCaseListFragment extends Fragment {
    private static final String TAG = "TestCaseListFragment";
    private SuiteViewModel viewModel;
    private TestCaseAdapter adapter;

    private XButton mExecuteAllButton;
    private XTextView mDirectoryNameTextView;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.i(TAG, "onCreateView(" + getClass().getSimpleName() + "):" + this.hashCode());
        return inflater.inflate(R.layout.at_fragment_factory_sub_directory, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);

        // 初始化RecyclerView
        RecyclerView rvScripts = view.findViewById(R.id.rv_test_case_list);
        rvScripts.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new TestCaseAdapter(new ArrayList<>(), new TestCaseAdapter.OnActionClickListener() {
            @Override
            public void onExecuteClick(TestCaseItem caseItem) {
                // 单个执行测试用例按钮点击事件，从task中获取已有的执行ID
//                viewModel.executeCase(caseItem);
                ((MainActivity) getContext()).startCaseTesting(caseItem, caseItem.getTaskExecutionId());
//                viewModel.startTest(new TestManager.ITestDataCallBack<ITestDataState<Long>>() {
//                    @Override
//                    public void onUpdate(ITestDataState<Long> state) {
//                        if (state instanceof ITestDataState.Success) {
//                            Long executionId = ((ITestDataState.Success<Long>) state).getData();
//                            ((MainActivity) getContext()).startCaseTesting(caseItem, executionId);
//                            Log.i(TAG, "start case test successful: " + executionId);
//                        } else if (state instanceof ITestDataState.Error) {
//                            Throwable error = ((ITestDataState.Error<Long>) state).getError();
//                            Log.e(TAG, "start case test error: " + error.getMessage());
//                        }
//                    }
//                });
            }

            @Override
            public void onStopClick(TestCaseItem caseItem) {
                // 单个停止测试用例按钮点击事件
//                viewModel.stopCase(caseItem);
                ((MainActivity) getContext()).stopTesting();
            }
        });

        rvScripts.setAdapter(adapter);

        Log.i("TestCaseListFragment", "onViewCreated: ");

        viewModel.getSelectedSuite().observe(getViewLifecycleOwner(), suite -> {
            if (suite != null) {
                mDirectoryNameTextView.setText(suite.getName());
                adapter.updateData(suite.getItems());
            }
        });

        // 观察数据变化
//        viewModel.getCurrentCases().observe(getViewLifecycleOwner(), scripts -> {
//            adapter.updateData(scripts);
//        });

        viewModel.getExecutingCase().observe(getViewLifecycleOwner(), script -> {
            if (script != null) {
                int position = adapter.getTestCases().indexOf(script);
                if (position != -1) {
                    adapter.notifyItemChanged(position);
                }
            }
        });

        viewModel.getLatestResult().observe(getViewLifecycleOwner(), result -> {
            adapter.updateTestResult(result);
        });
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(requireActivity()).get(SuiteViewModel.class);
    }

    private void initViews(View parentView) {
        this.mExecuteAllButton = parentView.findViewById(R.id.btn_execute_all);
        this.mExecuteAllButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                if (getComponentClient().isExecuting()) {
//                    XToast.show(getString(R.string.tips_function_executing));
//                    return;
//                }
//
//                List<Long> componentIdList = getDirectoryClient().getComponentIdsRecursive(directoryId);
//                if (CheckUtils.isEmpty(componentIdList)) {
//                    XToast.show(getString(R.string.tips_function_no_executable));
//                    return;
//                }

                String title = getContext().getString(R.string.retry_execute_confirm_title);
                String messageFormat = getContext().getString(R.string.retry_execute_confirm_message);
                String message = String.format(messageFormat, mDirectoryNameTextView.getText());
                String positiveButtonText = getContext().getString(R.string.retry_execute_confirm_positive_button);
                String negativeButtonText = getContext().getString(R.string.retry_execute_confirm_negative_button);

                DialogHelper.getInstance().showDialog(getContext(), title, message,
                        positiveButtonText, negativeButtonText, (xDialog, buttonId) -> {
                            xDialog.dismiss();

//                            EventHelper.executeComponentConfirmation(directoryId, 0, buttonId);

                            switch (buttonId) {
                                case XDialogInterface.BUTTON_POSITIVE:
                                    ITestSuiteItem suite = viewModel.getSelectedSuite().getValue();
                                    if (suite == null) {
                                        Log.e(TAG, "No test suite selected");
                                        XToast.show("No test suite selected");
                                        return;
                                    }
                                    viewModel.startTest(new TestManager.ITestDataCallBack<ITestDataState<Long>>() {
                                        @Override
                                        public void onUpdate(ITestDataState<Long> state) {
                                            if (state instanceof ITestDataState.Success) {
                                                Long executionId = ((ITestDataState.Success<Long>) state).getData();
                                                ((MainActivity) getContext()).startSuiteTesting(suite, executionId);
                                                Log.i(TAG, "start suite test successful: " + executionId);
                                            } else if (state instanceof ITestDataState.Error) {
                                                Throwable error = ((ITestDataState.Error<Long>) state).getError();
                                                Log.e(TAG, "start suite test error: " + error.getMessage());
                                            }
                                        }
                                    });
//                                    if (stationGroup != null) {
//                                        // save station group execute state
//                                        saveStationGroupRecord(directoryId, stationGroup);
//                                    }
//
//                                    getComponentClient().executeBatchAsync(
//                                            directoryId, componentIdList, new ComponentExecuteListener() {
//                                                @Override
//                                                public void beforeExecution(long componentId) {
//                                                    ForwardHelper.refreshFactoryTabStatus();
//                                                    ForwardHelper.refreshComponentExecuteState(componentId, FactorySubDirectoryFragment.class);
//                                                }
//
//                                                @Override
//                                                public void afterExecution(long componentId, ComponentExecuteResult result) {
//                                                    ForwardHelper.refreshFactoryTabStatus();
//                                                    ForwardHelper.refreshComponentExecuteState(componentId, FactorySubDirectoryFragment.class);
//                                                }
//                                            });

                                    break;

                                case XDialogInterface.BUTTON_NEGATIVE:
//                                    EventHelper.executeComponentCancel(directoryId, 0, "cancel");
                                    break;
                            }
                        });
            }
        });

        this.mDirectoryNameTextView = parentView.findViewById(R.id.tv_ota_version);
    }
}
