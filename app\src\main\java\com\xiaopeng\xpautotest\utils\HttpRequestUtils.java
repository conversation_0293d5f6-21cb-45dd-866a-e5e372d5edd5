package com.xiaopeng.xpautotest.utils;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.xiaopeng.lib.BuildConfig;
import com.xiaopeng.lib.framework.moduleinterface.netchannelmodule.http.Callback;
import com.xiaopeng.lib.framework.moduleinterface.netchannelmodule.http.IBizHelper;
import com.xiaopeng.lib.framework.moduleinterface.netchannelmodule.http.IHttp;
import com.xiaopeng.lib.framework.moduleinterface.netchannelmodule.http.IResponse;
import com.xiaopeng.lib.http.server.ServerBean;
import com.xiaopeng.xpautotest.App;
import com.xiaopeng.xpautotest.bean.CommonResponse;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.lib.utils.ThreadUtils;

import org.json.JSONObject;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class HttpRequestUtils {

    private static final String TAG = "HttpRequestUtils";
    private static final IHttp HTTP = App.getHttp();
    private static final Gson sGson = new GsonBuilder().disableHtmlEscaping().create();


    public static void httpPost(String url, Map<String, Object> params, HttpCallback callback) {
        httpPost(url, params, callback, false);
    }

    public static void httpPost(final String url,
                                final Map<String, Object> params,
                                final HttpCallback callback,
                                final boolean needAuthorizationInfo) {
        ThreadUtils.postDelayed(ThreadUtils.THREAD_BACKGROUND, () -> post(url, params, callback, needAuthorizationInfo), 0);
    }

    private static void post(final String url, Map<String, Object> params,
                             final HttpCallback callback,
                             boolean needAuthorizationInfo) {
        String body = null;
        if (null != params) {
            body = sGson.toJson(params);
        }

        if (null == body) {
            body = "{}";
        }
        if (HTTP == null) {
            Log.e(TAG, "http is null");
            return;
        }
        IBizHelper bizHelper = HTTP.bizHelper().post(url, body);
        if (needAuthorizationInfo) {
            bizHelper.needAuthorizationInfo();
            bizHelper.enableSecurityEncoding();
        }

        bizHelper.build()
                .execute(new Callback() {

                    @SuppressLint("CheckResult")
                    @Override
                    public void onSuccess(final IResponse iResponse) {
                        final String body = iResponse.body();
                        final boolean bodyEmpty = TextUtils.isEmpty(body);
                        if (BuildConfig.DEBUG) {
                            Log.i(TAG, "onSuccess:" + iResponse.code() + ",body=" + body + ",url=" + url);
                        } else {
                            Log.i(TAG, "onSuccess:" + iResponse.code() + ",bodyEmpty=" + bodyEmpty + ",url=" + url);
                        }
                        if (!bodyEmpty) {
                            Disposable mDisposable = Rx2Util
                                    .getFlowableOnIo(() -> getServerBean(body))
                                    .observeOn(Schedulers.io())
                                    .map(serverBean -> {
                                        final Type type = callback.getType();
                                        Log.i(TAG, "onSuccess(map):type=" + type + ",url=" + url);
                                        if (type == null) {
                                            return serverBean.getData();
                                        }
                                        if (CommonResponse.class.equals(type)) {
                                            return new CommonResponse();
                                        }
                                        Object bean = getDataBean(serverBean, type);
                                        if (bean == null) {
                                            return new NullPointerException();
                                        } else {
                                            return bean;
                                        }
                                    })
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe(callback::onResponse, throwable -> {
                                        String body1 = iResponse.body();
                                        Log.e(TAG, "data error:body=" + body1 + ",url=" + url);
                                        callback.onError(iResponse.code(), body1);
                                    });
                            Log.d(TAG, "mDisposable = " + mDisposable);
                        } else {
                            callback.onError(iResponse.code(), iResponse.message());
                        }
                    }

                    @Override
                    public void onFailure(IResponse iResponse) {
                        Log.e(TAG, "onFailure:code=" + iResponse.code() + ",body = " + iResponse.body() + ",url=" + url);
                        callback.onError(iResponse.code(), iResponse.message());
                    }
                });
    }

    public static ServerBean getServerBean(String body) {
        ServerBean bean;
        try {
            JSONObject jsonObject = new JSONObject(body);
            bean = new ServerBean();
            bean.setCode(jsonObject.getInt("code"));
            if (jsonObject.has("data")) {
                bean.setData(jsonObject.getString("data"));
            }
            if (jsonObject.has("msg")) {
                bean.setMsg(jsonObject.getString("msg"));
            }
        } catch (Exception ex) {
            bean = null;
            Log.i(TAG, "Failed to parser the response data. Exception:" + ex);
        }
        return bean;
    }

    private static <T> T getDataBean(ServerBean bean, Type type) {
        final String data = bean.getData();
        if (data != null && type != null && !data.isEmpty()) {
            try {
                return sGson.fromJson(data, type);
            } catch (JsonSyntaxException e) {
                Log.e(TAG, "error:" + e.getMessage());
                return null;
            }
        } else {
            return null;
        }
    }

    public abstract static class HttpCallback<T> {
        /**
         * 请求成功
         *
         * @param bean 结果
         */
        public abstract void onResponse(T bean);

        /**
         * 请求失败
         *
         * @param code 错误码
         * @param msg  错误提示
         */
        public abstract void onError(int code, String msg);

        Type getType() {
            Type rawType = null;
            try {
                final ParameterizedType genericSuperclass = (ParameterizedType) getClass().getGenericSuperclass();
                if (genericSuperclass != null) {
                    rawType = genericSuperclass.getActualTypeArguments()[0];
                }
            } catch (Exception e) {
                Log.e(TAG, "----parse type error!");
                return null;
            }
            return rawType;
        }
    }

}
