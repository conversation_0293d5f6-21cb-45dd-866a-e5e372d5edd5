package com.xiaopeng.xpautotest.client.api;

import com.xiaopeng.xpautotest.bean.HeartbeatEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.bean.VehicleInfo;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Path;

public interface VehicleApiService {
    @POST("vehicle/register")
    Call<ApiResponse<TestTaskBean>> register(@Body VehicleInfo vehicleInfo);

    @POST("vehicle/{vin}/heartbeat")
    Call<ApiResponse<TestTaskBean>> heartbeat(@Path("vin") String vin);

    @POST("vehicle/{vin}/heartbeat")
    Call<ApiResponse<TestTaskBean>> heartbeatSync(@Path("vin") String vin, @Body HeartbeatEntity info);
}
