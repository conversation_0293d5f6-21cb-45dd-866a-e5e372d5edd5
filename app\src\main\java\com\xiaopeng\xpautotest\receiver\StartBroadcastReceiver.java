package com.xiaopeng.xpautotest.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.xiaopeng.xpautotest.community.utils.Log;

public class StartBroadcastReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i("StartBroadcastReceiver", "receiver action --》" + intent.getAction());
        if ("com.xiaoppeng.xpautotest.OPEN_APP_ACTION".equals(intent.getAction())) {
            Log.i("StartBroadcastReceiver", "StartBroadcastReceiver onReceive");
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(launchIntent);
            }
        }
    }
}
