package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class ClickByTextContainsAction extends BaseAction {
    private static final String TAG = "ClickByTextContainsAction";
    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String text = (String) context.getStringParam();
        if (text == null) {
            throw new ActionException("text is null!", FailureCode.SI001);
        }

        FileLogger.i(TAG, "text: " + text);
        boolean result = this.service.clickNodeByTextContains(text);
        if (!result) {
            return TestResult.failure("Failed to click by text containing: '" + text + "'");
        }
        return TestResult.success("Clicked by text containing: '" + text + "' successfully.");
    }
}
