package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class InputTextAction extends BaseAction {
    private static final String TAG = "InputTextAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String sourceId = (String) context.getStringParam();
        if (sourceId == null) {
            throw new ActionException("source id is null!", FailureCode.SI001);
        }
        String text = (String) context.getStringParam();
        if (text == null) {
            throw new ActionException("text is null!",FailureCode.SI001);
        }

        FileLogger.i(TAG, "resourceId: " + sourceId + ", text: " + text);
        boolean result = this.service.inputText(sourceId, text);
        if (!result) {
            return TestResult.failure("Failed to input text '" + text + "' into element with id: " + sourceId);
        }
        return TestResult.success("Input text '" + text + "' into element with id: " + sourceId + " successfully.");
    }
}
