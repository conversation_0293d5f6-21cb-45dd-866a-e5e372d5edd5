package com.xiaopeng.xpautotest.client.oss;

import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.manager.ConnectionChangeReceiver;
import java.io.File;
import com.xiaopeng.xpautotest.helper.OSSDataUploadHelper;
import com.xiaopeng.xpautotest.utils.SystemPropertiesUtils;

import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class OSSUploadQueueManager implements ConnectionChangeReceiver.INetworkMsgHandler {
    private static final String TAG = "OSSUploadQueueManager";
    private static final int MAX_UPLOAD_QUEUE_SIZE = 200;
    private static final int MAX_UPLOAD_RETRIES = 3; // 最大上传重试次数
    private static final long UPLOAD_RETRY_DELAY_MS = 3000; // 每次重试的基础延迟时间

    private final BlockingQueue<UploadFileTask> mUploadQueue = new LinkedBlockingQueue<>(MAX_UPLOAD_QUEUE_SIZE);
    private ExecutorService mExecutorService;
    private ScheduledExecutorService mRetryScheduler; // 用于延迟重试
    private static volatile OSSUploadQueueManager instance;

    public static OSSUploadQueueManager getInstance() {
        if (instance == null) {
            synchronized (OSSUploadQueueManager.class) {
                if (instance == null) {
                    instance = new OSSUploadQueueManager();
                }
            }
        }
        return instance;
    }

    private OSSUploadQueueManager() {
    }

    public void start() {
        if (mExecutorService == null || mExecutorService.isShutdown()) {
            mExecutorService = Executors.newSingleThreadExecutor();
        }
        if (mRetryScheduler == null || mRetryScheduler.isShutdown()){ // 初始化用于延迟重试的调度器
            mRetryScheduler = Executors.newSingleThreadScheduledExecutor();
        }
        // 注册网络状态变化监听
        ConnectionChangeReceiver.getInstance().addNetworkMsgHandler(this);

        // vin 值为空就不上传了
        if(!SystemPropertiesUtils.getVIN().isEmpty()){
            List<UploadFileTask> uploadFileTasks = OSSDataUploadHelper.scanAndCreateUploadTasks();
            for (UploadFileTask uploadFileTask : uploadFileTasks) {
                addUploadTaskInternal(uploadFileTask);
            }
        }

        notifyWorkerToProcess();
        FileLogger.i(TAG, "OssUploadQueueManager started. Max queue size: " + MAX_UPLOAD_QUEUE_SIZE + ", Max retries: " + MAX_UPLOAD_RETRIES);
    }

    public void stop() {
        FileLogger.i(TAG, "Stopping OssUploadQueueManager...");
        ConnectionChangeReceiver.getInstance().removeNetworkMsgHandler(this);
        if (mExecutorService != null) {
            mExecutorService.shutdown();
            try {
                if (!mExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    mExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                mExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        if (mRetryScheduler != null) {
            mRetryScheduler.shutdown();
            try {
                if (!mRetryScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    mRetryScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                mRetryScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        FileLogger.i(TAG, "OssUploadQueueManager stopped.");
        instance = null;
    }

    private boolean addUploadTaskInternal(UploadFileTask task) {
        // offer() 是非阻塞的，如果队列满了（已达到MAX_UPLOAD_QUEUE_SIZE）会立即返回false
        if (mUploadQueue.offer(task)) {
            //FileLogger.d(TAG, "Upload task added to queue (current size: " + mUploadQueue.size() + "/" + MAX_UPLOAD_QUEUE_SIZE + ", retries: " + task.getRetryCount() + "): " + task.getAbsoluteFilePath());
            return true;
        } else {
            FileLogger.e(TAG, "Upload queue is full (capacity: " + MAX_UPLOAD_QUEUE_SIZE + "). Cannot add new task: " + task.getAbsoluteFilePath());
            return false;
        }
    }

    public void addUploadTask(String absoluteFilePath) {
        // VIN 值为空就不上传了
        if(SystemPropertiesUtils.getVIN().isEmpty()){
            return;
        }

        File file = new File(absoluteFilePath);
        if (!file.exists()) {
            FileLogger.e(TAG, "File does not exist, cannot add to upload queue: " + absoluteFilePath);
            return;
        }
        UploadFileTask task = new UploadFileTask(absoluteFilePath, file.lastModified(), 0);
        boolean added = addUploadTaskInternal(task);
        if (added) {
            notifyWorkerToProcess();
        }
    }

    private void notifyWorkerToProcess() {
        if (mExecutorService != null && !mExecutorService.isShutdown()) {
            mExecutorService.submit(this::processSingleTaskFromQueue);
            //FileLogger.d(TAG, "Submitted task processing to ExecutorService. Current queue size: " + mUploadQueue.size());
        } else {
            FileLogger.w(TAG, "ExecutorService not available.");
        }
    }

    private void processSingleTaskFromQueue() {
        if (!ConnectionChangeReceiver.getInstance().isWifiConnected()) {
            FileLogger.i(TAG, "WiFi not connected.");
            return;
        }

        UploadFileTask uploadFileTask = mUploadQueue.poll();
        if (uploadFileTask == null) {
            //FileLogger.d(TAG, "Upload queue is empty.");
            return;
        }

        FileLogger.i(TAG, "Processing task: " + uploadFileTask.getAbsoluteFilePath() + " (Retry: " + uploadFileTask.getRetryCount() + ")");
        boolean success = false;
        boolean shouldRetry = false;

        try {
            success = OSSManager.getInstance().upload(uploadFileTask);
            if (!success) {
                FileLogger.i(TAG, "Upload failed (returned false) for: " + uploadFileTask.getAbsoluteFilePath());
                shouldRetry = true;
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "Exception during upload for " + uploadFileTask.getAbsoluteFilePath() + ": " + e.getMessage());
            shouldRetry = true;
        }

        if (success) {
            boolean deletedSuccessfully = OSSDataUploadHelper.attemptDeleteWithRetries(uploadFileTask);
            if (!deletedSuccessfully) {
                FileLogger.e(TAG, "Failed to delete local file : " + uploadFileTask.getAbsoluteFilePath() + ".");
                // OSSDataUploadHelper.markFileForDeferredDeletion(uploadFileTask);
            }
        } else if (shouldRetry) {
            if (uploadFileTask.getRetryCount() < MAX_UPLOAD_RETRIES) {
                uploadFileTask.incrementRetryCount();
                FileLogger.w(TAG, "Retrying upload: " + uploadFileTask.getAbsoluteFilePath() + " (Attempt " + uploadFileTask.getRetryCount() + "/" + MAX_UPLOAD_RETRIES + ")");

                if (mRetryScheduler != null && !mRetryScheduler.isShutdown()) {
                    mRetryScheduler.schedule(() -> {
                        FileLogger.i(TAG, "Scheduled retry for: " + uploadFileTask.getAbsoluteFilePath());
                        // 将任务重新放回主队列
                        if (addUploadTaskInternal(uploadFileTask)) {
                           notifyWorkerToProcess();
                        } else {
                            FileLogger.e(TAG, "Failed to re-queue task for retry (main queue full): " + uploadFileTask.getAbsoluteFilePath());
                            // 如果主队列满了，这个延迟重试的任务会被丢弃
                        }
                    }, UPLOAD_RETRY_DELAY_MS * uploadFileTask.getRetryCount(), TimeUnit.MILLISECONDS);
                } else { // Fallback: 立即重新入队到尾部
                     FileLogger.w(TAG, "Retry scheduler not available, re-queuing immediately for: " + uploadFileTask.getAbsoluteFilePath());
                     if (!mUploadQueue.offer(uploadFileTask)) {
                         FileLogger.e(TAG, "Failed to re-queue task for immediate retry (queue full): " + uploadFileTask.getAbsoluteFilePath());
                     } else {
                         notifyWorkerToProcess();
                     }
                }
            } else {
                FileLogger.e(TAG, "Max retries reached for: " + uploadFileTask.getAbsoluteFilePath() + ". Upload failed permanently.");
                // TODO: 重试达到上限次数仍旧失败的情况，目前只能等应用重启再扫进来了
            }
        }

        if (mExecutorService != null && !mExecutorService.isShutdown()) {
            // 再次提交，执行队列中剩余任务
            mExecutorService.submit(this::processSingleTaskFromQueue);
            //FileLogger.d(TAG, "Submitted next task processing cycle.");
        } else {
            FileLogger.w(TAG, "ExecutorService stopped.");
        }
    }

    @Override
    public void onConnected(boolean isConnected) {
        Log.i(TAG, "onConnected: isConnected=" + isConnected);
        if (isConnected && ConnectionChangeReceiver.getInstance().isWifiConnected()) {
            Log.i(TAG, "onConnected: WiFi connected, notifying worker to process queue.");
            notifyWorkerToProcess();
        }
    }
}