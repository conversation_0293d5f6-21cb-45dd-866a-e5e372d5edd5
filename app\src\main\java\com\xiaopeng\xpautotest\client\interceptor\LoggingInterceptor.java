package com.xiaopeng.xpautotest.client.interceptor;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.xiaopeng.xpautotest.community.utils.Log;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;

public class LoggingInterceptor implements Interceptor {
    private final Gson gson = new GsonBuilder().disableHtmlEscaping().create();
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String requestJson = "null";
        try (Buffer buffer = new Buffer()) {
            if (request.body() != null) {
                request.body().writeTo(buffer);
                Charset charset = StandardCharsets.UTF_8;
                MediaType contentType = request.body().contentType();
                if (contentType != null) {
                    charset = contentType.charset(StandardCharsets.UTF_8);
                    requestJson = buffer.readString(charset);
                }
            }
        }

        Log.d("HTTP Request", request.method() + ", " + request.url() + ", " + requestJson);

        long startTime = System.nanoTime();
        Response response = chain.proceed(request);
        long elapsedTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startTime);

        // response.body().string() will close the response body, so we need to save it for logging
//        String responseJson = response.body() != null ? "" : "null";
        Log.d("HTTP Response",
                String.format(Locale.US, "Code: %d, Time: %dms",
                        response.code(), elapsedTime));

        return response;
    }
}
