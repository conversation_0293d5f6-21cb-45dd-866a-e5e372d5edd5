package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.CMDUtils;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class SendBroadcastAction extends BaseAction {

    private static final String TAG = "SendBroadcastAction";

    @Override
    public TestResult execute(ActionContext actionContext) throws ActionException {
        if (this.service == null) {
            String errorMsg = "AccessibilityHelper service is null.";
            FileLogger.e(TAG, errorMsg);
            throw new ActionException(errorMsg, FailureCode.AB002);
        }

        String userCommandPayload = actionContext.getStringParam();
        if (userCommandPayload == null || userCommandPayload.trim().isEmpty()) {
            String errorMsg = "Parameter 'command' for broadcast is required and cannot be empty.";
            FileLogger.e(TAG, errorMsg);
            throw new ActionException(errorMsg, FailureCode.SI001);
        }
        //  拼接`am broadcast` 之后的所有参数
        String amCommand = "am broadcast " + userCommandPayload.trim();
        FileLogger.i(TAG, "Executing am command: " + amCommand);

        try {
            CMDUtils.CMD_Result cmdResult = CMDUtils.runCMD(amCommand, true, true);

            if (cmdResult == null) {
                String errorMsg = "Failed to execute am command. CMDUtils.runCMD returned null.";
                FileLogger.e(TAG, errorMsg);
                return TestResult.failure(errorMsg);
            }

            if (cmdResult.resultCode == 0) {
                FileLogger.i(TAG, "am command executed successfully. Output: " + cmdResult.success);
                String successMessage = "'am broadcast' command executed.";
                if (cmdResult.success != null && !cmdResult.success.trim().isEmpty()) {
                    successMessage += "\nOutput:\n" + cmdResult.success.trim();
                } else if (cmdResult.error != null && !cmdResult.error.trim().isEmpty()) {
                    successMessage += "\nInfo/Warnings:\n" + cmdResult.error.trim();
                }
                return TestResult.success(successMessage);
            } else {
                String errorMsg = "'am broadcast' command execution failed. Result code: " + cmdResult.resultCode;
                if (cmdResult.error != null && !cmdResult.error.trim().isEmpty()) {
                    errorMsg += "\nError output:\n" + cmdResult.error.trim();
                } else if (cmdResult.success != null && !cmdResult.success.trim().isEmpty()) {
                    errorMsg += "\nOutput (possibly containing error details):\n" + cmdResult.success.trim();
                }
                FileLogger.e(TAG, errorMsg);
                return TestResult.failure(errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = "Exception while executing am command: " + amCommand + "; Error: " + e.getMessage();
            FileLogger.e(TAG, errorMsg);
            throw new ActionException(errorMsg, FailureCode.AB002, e);
        }
    }
} 