package com.xiaopeng.xpautotest.client.oss;

import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.utils.OSSPathUtils;
import java.io.File;
import java.net.URI;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.urlconnection.UrlConnectionHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import com.xiaopeng.xpautotest.constant.EnvironmentConfig;

public class MinIOOSSUploader implements IUploader {

    private static final String TAG = "MinIOOSSUploader";

    private static volatile MinIOOSSUploader instance;
    private S3Client s3Client;
    private String bucketName;

    private MinIOOSSUploader() {
        try {
            EnvironmentConfig.OSSConfig ossConfig = EnvironmentConfig.getInstance().getOssConfig();
            this.bucketName = ossConfig.getBucketName();

            StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
                    AwsBasicCredentials.create(ossConfig.getAccessKey(), ossConfig.getSecurityKey())
            );

            SdkHttpClient urlConnectionHttpClient = UrlConnectionHttpClient.builder()
                    .socketTimeout(java.time.Duration.ofSeconds(60))
                    .connectionTimeout(java.time.Duration.ofSeconds(10))
                    .build();

            s3Client = S3Client.builder()
                    .endpointOverride(URI.create(ossConfig.getEndpoint()))
                    .region(Region.AF_SOUTH_1) 
                    .credentialsProvider(credentialsProvider)
                    .forcePathStyle(true) 
                    .httpClient(urlConnectionHttpClient) 
                    .build();
        } catch (Exception ex) {
            FileLogger.e(TAG, "FATAL: Exception during MinIOOSSUploader initialization!");
            FileLogger.e(TAG, "Exception: " + ex.getMessage());
        }
    }

    public static synchronized MinIOOSSUploader getInstance() {
        if (instance == null) {
            instance = new MinIOOSSUploader();
        }
        return instance;
    }

    private String getContentType(UploadFileTask.FileType fileType) {
        switch (fileType) {
            case FILE_IMAGE:
                return "image/png";
            case FILE_ZIP:
                return "application/zip";
            case FILE_LOG:
                return "text/plain";
            case FILE_XML:
                return "application/xml";
            case FILE_CDU_LOG:
                return "application/zip";
            default:
                return "application/octet-stream";
        }
    }

    @Override
    public boolean upload(UploadFileTask uploadFileTask) throws Exception {
        File fileToUpload = new File(uploadFileTask.getAbsoluteFilePath());
        if (!fileToUpload.exists() || !fileToUpload.isFile() || !fileToUpload.canRead()) {
            FileLogger.e(TAG, "Invalid or unreadable file provided: " + uploadFileTask.getAbsoluteFilePath());
            return false;
        }

        String originalFileName = fileToUpload.getName();
        String objectKey = OSSPathUtils.buildObjectKey(uploadFileTask.getFiletype(), originalFileName, uploadFileTask.getAbsoluteFilePath());

        // Check if the objectKey is null or empty
        if (objectKey == null || objectKey.isEmpty()) {
            FileLogger.w(TAG, "Generated object key is null or empty for file: " + originalFileName + ". Skipping upload.");
            return false; // Skip upload
        }

        String contentType = getContentType(uploadFileTask.getFiletype());
        long fileSize = fileToUpload.length();

        //FileLogger.i(TAG, "Attempting upload for '" + fileToUpload.getAbsolutePath() + "' (Size: " + fileSize + " bytes) as '" + objectKey);

        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .contentType(contentType)
                    .contentLength(fileSize)
                    .build();

            PutObjectResponse response = s3Client.putObject(putObjectRequest, RequestBody.fromFile(fileToUpload));

            FileLogger.i(TAG, "upload completed successfully for '" + objectKey + "'. ETag: " + response.eTag());
            return true;

        } catch (Exception e) {
            FileLogger.e(TAG, "AWS SDK synchronous upload failed for '" + objectKey + "': " + e.getCause());
            FileLogger.e(TAG, "Cause: " + e.getMessage());
        }
        return false;
    }

    public void close() {
        try {
            if (s3Client != null) {
                s3Client.close();
                FileLogger.d(TAG, "S3Client closed.");
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "Error closing S3Client: " + e.getMessage());
        }
        s3Client = null;
        instance = null; 
        FileLogger.i(TAG, "AWS SDK components closed via close().");
    }
}