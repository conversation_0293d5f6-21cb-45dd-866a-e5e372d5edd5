package com.xiaopeng.xpautotest.helper.log.model;

/**
 * 日志处理结果
 * 用于封装所有日志处理的结果信息，包含FileLogger和CDU日志的路径
 */
public class LogProcessResult {
    private final String fileLogPath;
    private final String cduLogPath;
    private final boolean hasErrors;

    public LogProcessResult(String fileLogPath, String cduLogPath, boolean hasErrors) {
        this.fileLogPath = fileLogPath;
        this.cduLogPath = cduLogPath;
        this.hasErrors = hasErrors;
    }

    public String getFileLogPath() { 
        return fileLogPath; 
    }
    
    public String getCduLogPath() { 
        return cduLogPath; 
    }
    
    public boolean hasErrors() { 
        return hasErrors; 
    }

    /**
     * 创建成功结果
     */
    public static LogProcessResult success(String fileLogPath, String cduLogPath) {
        return new LogProcessResult(fileLogPath, cduLogPath, false);
    }

    /**
     * 创建失败结果
     */
    public static LogProcessResult failure() {
        return new LogProcessResult(null, null, true);
    }

    /**
     * 创建部分成功结果
     */
    public static LogProcessResult partial(String fileLogPath, String cduLogPath, boolean hasErrors) {
        return new LogProcessResult(fileLogPath, cduLogPath, hasErrors);
    }

    @Override
    public String toString() {
        return "LogProcessResult{" +
                "fileLogPath='" + fileLogPath + '\'' +
                ", cduLogPath='" + cduLogPath + '\'' +
                ", hasErrors=" + hasErrors +
                '}';
    }
}
