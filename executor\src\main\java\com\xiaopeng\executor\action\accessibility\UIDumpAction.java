package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.FailureContextHolder;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;

public class UIDumpAction extends BaseAction {

    private static final String TAG = "UIDumpAction";

    @Override
    public TestResult execute(ActionContext context) throws ActionException {
        String timeStampString = context.getTimeStampString();

        if (this.service == null) {
            String errorMsg = "AccessibilityHelper service is null. Cannot perform UIDump.";
            FileLogger.e(TAG, errorMsg);
            FailureContextHolder.setFailureIfNotSet(FailureCode.AB002);
            return TestResult.failure(errorMsg);
        }

        FileLogger.i(TAG, "UIDump on current screen");
        TestResult.ActionArtifacts artifacts = this.service.screenShotandDumpUI(timeStampString);

        if (artifacts.uiDumpFileName != null && !artifacts.uiDumpFileName.isEmpty()) {
            return TestResult.success("UIDump executed successfully.", artifacts);
        } else {
            FileLogger.w(TAG, "UIDump failed.");
            return TestResult.failure("UIDump failed");
        }
    }
}