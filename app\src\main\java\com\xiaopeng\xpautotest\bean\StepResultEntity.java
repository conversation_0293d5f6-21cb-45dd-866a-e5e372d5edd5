package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import lombok.Data;
import lombok.NoArgsConstructor;
import com.xiaopeng.xpautotest.community.test.FailureReasonDetail;
import com.xiaopeng.xpautotest.community.test.FailureCode;

@Data
@NoArgsConstructor
public class StepResultEntity implements Parcelable, ReportableEntity {
    private long taskExecutionId;
    private long scriptId;
    private int stepId;
    private String step;    // 步骤内容
    private String comment; // 步骤注释
    private String keywords; // 步骤关键字
    private String stepType; // 步骤类型，可能是"Precondition", "Procedure", "PostCondition"
    private String stepResult;
    private long timestamp; // 时间戳
    private long startTime; // 步骤开始时间
    private long endTime;   // 步骤结束时间

    // 存储到oss上的 can数据路径
    private String canPath;
    // 存储到oss上的截图路径
    private String imgPath;
    // 存储到oss上的UIdump数据路径
    private String dumpPath;

    // 失败原因详情
    private FailureReasonDetail failureReason;

    public void setCanPath(String canPath) {
        this.canPath = canPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public void setDumpPath(String dumpPath) {
        this.dumpPath = dumpPath;
    }

    public StepResultEntity(long taskExecutionId, long scriptId, int stepId, String step, String comment, String keywords, String stepType, String stepResult, long startTime,long endTime) {
        this.taskExecutionId = taskExecutionId;
        this.scriptId = scriptId;
        this.stepId = stepId;
        this.step = step;
        this.comment = comment;
        this.keywords = keywords;
        this.stepType = stepType;
        this.stepResult = stepResult;
        this.timestamp = System.currentTimeMillis();
        this.startTime = startTime;
        this.endTime = endTime;
    }

    protected StepResultEntity(Parcel in) {
        taskExecutionId = in.readLong();
        scriptId = in.readLong();
        stepId = in.readInt();
        step = in.readString();
        comment = in.readString();
        keywords = in.readString();
        stepType = in.readString();
        stepResult = in.readString();
        timestamp = in.readLong();
        startTime = in.readLong();
        endTime = in.readLong();
        canPath = in.readString();
        imgPath = in.readString();
        dumpPath = in.readString();
        boolean hasFailureReason = in.readByte() != 0;
        if (hasFailureReason) {
            String errorCodeStr = in.readString();
            String originalMessage = in.readString();
            // 重建FailureReasonDetail对象
            FailureCode errorCode = FailureCode.valueOf(errorCodeStr);
            this.failureReason = new FailureReasonDetail(errorCode, originalMessage, null);
        }
    }

    public static final Creator<StepResultEntity> CREATOR = new Creator<StepResultEntity>() {
        @Override
        public StepResultEntity createFromParcel(Parcel in) {
            return new StepResultEntity(in);
        }

        @Override
        public StepResultEntity[] newArray(int size) {
            return new StepResultEntity[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(taskExecutionId);
        dest.writeLong(scriptId);
        dest.writeInt(stepId);
        dest.writeString(step);
        dest.writeString(comment);
        dest.writeString(keywords);
        dest.writeString(stepType);
        dest.writeString(stepResult);
        dest.writeLong(timestamp);
        dest.writeLong(startTime);
        dest.writeLong(endTime);
        dest.writeString(canPath);
        dest.writeString(imgPath);
        dest.writeString(dumpPath);

        // 写入失败原因
        if (failureReason != null) {
            dest.writeByte((byte) 1); // 有失败原因
            dest.writeString(failureReason.getErrorCode().name());
            dest.writeString(failureReason.getErrorMessage());
            dest.writeString(failureReason.getOriginalMessage());
        } else {
            dest.writeByte((byte) 0); // 没有失败原因
        }
    }

    @Override
    public long getTaskExecutionId() {
        return taskExecutionId;
    }

    public long getScriptId() {
        return scriptId;
    }

    public void setFailureReason(FailureReasonDetail failureReason) {
        this.failureReason = failureReason;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

}
