// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"

buildscript {
    repositories {
        maven {
            url RELEASE_REPO_URL
            allowInsecureProtocol = true
        }       // release maven 仓库
        maven {
            url SNAPSHOT_REPO_URL
            allowInsecureProtocol = true
        }      // snapshot maven 仓库
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/central' }
//        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0'
        classpath 'com.xiaopeng.lib.build:buildhelper:1.0.0-SNAPSHOT'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven {
            url RELEASE_REPO_URL
            allowInsecureProtocol = true
        }       // release maven 仓库
        maven {
            url SNAPSHOT_REPO_URL
            allowInsecureProtocol = true
        }      // snapshot maven 仓库
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
