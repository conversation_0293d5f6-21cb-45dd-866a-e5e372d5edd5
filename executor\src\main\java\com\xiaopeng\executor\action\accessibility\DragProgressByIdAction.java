package com.xiaopeng.executor.action.accessibility;

import com.xiaopeng.executor.bean.ActionContext;
import com.xiaopeng.executor.bean.ActionException;
import com.xiaopeng.xpautotest.community.test.FailureCode;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.accessibility.AccessibilityHelper;

import java.util.Locale;

public class DragProgressByIdAction extends BaseAction {
    private static final String TAG = "DragProgressByIdAction";

    /**
     * 通过拖动进度条/滑块执行设置进度操作
     * 需要从ActionContext获取两个参数：
     * 1、进度条/滑块的资源ID（String类型）
     * 2、目标百分比值（float类型，0.0到100.0）
     *
     * @param context ActionContext
     * @return 返回表示操作成功或失败的TestResult结果
     */
    @Override
    public TestResult execute(ActionContext context) throws ActionException {

        String resourceId = context.getStringParam().trim();
        float percentageValue = context.getFloatParam();
        if(Float.isNaN(percentageValue) || percentageValue < 0.0f || percentageValue > 100.0f) {
            String errorMsg = String.format(Locale.ROOT, "Invalid percentage value: %.2f. It must be between 0.0 and 100.0.", percentageValue);
            FileLogger.e(TAG, errorMsg);
            throw new ActionException(errorMsg, FailureCode.SI001);
        }

        int duration = 300;
        boolean success = this.service.setProgressByGesture(resourceId, percentageValue, AccessibilityHelper.GESTURE_TYPE_DRAG, duration);

        if (success) {
            String message = String.format(Locale.ROOT, "Successfully set progress (by drag) for resource ID '%s' to %.2f%% (default duration).",
                                           resourceId, percentageValue);
            FileLogger.i(TAG, message);
            return TestResult.success(message);
        } else {
            String message = String.format(Locale.ROOT, "Failed to set progress (by drag) for resource ID '%s' to %.2f%% (default duration).",
                                           resourceId, percentageValue);
            FileLogger.i(TAG, message);
            return TestResult.failure(message);
        }
    }
} 