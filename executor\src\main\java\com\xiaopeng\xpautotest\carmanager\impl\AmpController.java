package com.xiaopeng.xpautotest.carmanager.impl;

import android.car.Car;
import android.car.hardware.CarPropertyValue;
import android.car.hardware.amp.CarAmpManager;
import android.os.SystemClock;

import com.xiaopeng.utils.ValueCompareUtils;
import com.xiaopeng.utils.ValueTypeUtils;
import com.xiaopeng.xpautotest.carmanager.BaseCarController;
import com.xiaopeng.xpautotest.carmanager.carcontroller.IAmpController;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.constant.Constant.CarApi;
import com.xiaopeng.constant.Constant.ValueType;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public class AmpController extends BaseCarController<CarAmpManager, IAmpController.Callback>
        implements IAmpController {
    private static final String TAG = "AmpController";
    public AmpController(Car carClient) {

    }
    public static class AmpControllerFactory {
        public static AmpController createCarController(Car carClient) {
            return new AmpController(carClient);
        }
    }

    private final CarAmpManager.CarAmpEventCallback mCarAmpEventCallback = new CarAmpManager.CarAmpEventCallback() {
        @Override
        public void onChangeEvent(CarPropertyValue carPropertyValue) {
            FileLogger.d(TAG, "onChangeEvent: " + carPropertyValue);
            handleCarEventsUpdate(carPropertyValue);
        }

        @Override
        public void onErrorEvent(int propertyId, int zone) {
            FileLogger.e(TAG, "onErrorEvent: " + propertyId);
        }
    };

    @Override
    protected void initCarManager(Car carClient) {
        FileLogger.d(TAG, "Init start");
        try {
            mCarManager = (CarAmpManager) carClient.getCarManager(Car.XP_AMP_SERVICE);
            if (mCarManager != null) {
                mCarManager.registerPropCallback(mPropertyIds, mCarAmpEventCallback);
            }
        } catch (Exception e) {
            FileLogger.e(TAG, e.getMessage());
        }
        FileLogger.d(TAG, "Init end");
    }

    @Override
    protected List<Integer> getRegisterPropertyIds() {
        return Collections.emptyList();
    }

    @Override
    protected void disconnect() {
    }

    @Override
    protected void handleEventsUpdate(CarPropertyValue<?> value) {

    }

    int getRealId(String id) {
        int realId = -1;
        try {
            Field field = CarAmpManager.class.getField(id);
            realId = field.getInt(null);
        } catch (NoSuchFieldException e) {
            FileLogger.e(TAG, "getRealId: NoSuchFieldException: " + e.getMessage());
        } catch (IllegalAccessException e) {
            FileLogger.e(TAG, "getRealId: IllegalAccessException: " + e.getMessage());
        } catch (Exception e) {
            FileLogger.e(TAG, "getRealId: Exception: " + e.getMessage());
        }
        return realId;
    }

    @Override
    public boolean setValue(String id, String targetValue) {
        int realId = getRealId(id);
        if (realId < 0) {
            FileLogger.e(TAG, "setValue: Invalid id: " + id);
            return false;
        }
        // 对于简单的类型，直接通过ID
        String valType = ValueTypeUtils.getValueType(targetValue);
        FileLogger.i(TAG, "setValue: id: " + id + ", realId: " + realId + ", targetValue: " + targetValue + ", valType: " + valType);
        try {
            switch (valType) {
                case ValueType.BOOLEAN:
                    mCarManager.setBooleanProperty(realId, 0, Boolean.parseBoolean(targetValue));
                    break;
                case ValueType.INT:
                    mCarManager.setIntProperty(realId, 0, Integer.parseInt(targetValue));
                    break;
                case ValueType.FLOAT:
                    mCarManager.setFloatProperty(realId, 0, Float.parseFloat(targetValue));
                    break;
                case ValueType.STRING:
                    mCarManager.setStringProperty(realId, 0, targetValue);
                    break;
                case ValueType.BYTE_VECTOR:
                    byte[] byteArray = ValueTypeUtils.strToByteArray(targetValue);
                    mCarManager.setByteVectorProperty(realId, 0, byteArray);
                    break;
                case ValueType.INT_VECTOR:
                    int[] intArray = ValueTypeUtils.strToIntArray(targetValue);
                    mCarManager.setIntVectorProperty(realId, 0, intArray);
                    break;
                case ValueType.FLOAT_VECTOR:
                    float[] floatArray = ValueTypeUtils.strToFloatArray(targetValue);
                    mCarManager.setFloatVectorProperty(realId, 0, floatArray);
                    break;
//                case ValueType.LONG_VECTOR:
//                    long[] longArray = ValueTypeUtils.strToLongArray(targetValue);
//                    mCarManager.setLongVectorProperty(realId, 0, longArray);
//                    break;
                default:
                    FileLogger.e(TAG, "setValue Unsupported valueType: " + valType + ", id: " + id);
                    return false;
            }
        } catch (Exception e) {
            FileLogger.e(TAG, "setValue: Exception: " + e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public boolean checkValue(String id, String expectValue, int timeout) {
        int realId = getRealId(id);
        if (realId < 0) {
            FileLogger.e(TAG, "checkValue: Invalid id: " + id);
            return false;
        }
        String valueType = ValueTypeUtils.getValueType(expectValue);
        FileLogger.i(TAG, "checkValue: id: " + id + ", realId: " + realId + ", valueType: " + valueType);
        try {
            boolean checkResult = false;
            int checkTimes = 0;
            int retryTimes = (timeout < 0 ? CarApi.DEFAULT_CHECK_TIMEOUT : timeout) * 10;
            while (checkTimes < retryTimes && !checkResult) {
                switch (valueType) {
                    case ValueType.BOOLEAN:
                        boolean curBooleanValue = mCarManager.getBooleanProperty(realId, 0);
                        FileLogger.i(TAG, "curBooleanValue: " + curBooleanValue + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curBooleanValue, expectValue, valueType);
                        break;
                    case ValueType.INT:
                        int curIntValue = mCarManager.getIntProperty(realId, 0);
                        FileLogger.i(TAG, "curIntValue: " + curIntValue + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curIntValue, expectValue, valueType);
                        break;
                    case ValueType.FLOAT:
                        float curFloatValue = mCarManager.getFloatProperty(realId, 0);
                        FileLogger.i(TAG, "curFloatValue: " + curFloatValue + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curFloatValue, expectValue, valueType);
                        break;
                    case ValueType.STRING:
                        String curStringValue = mCarManager.getStringProperty(realId, 0);
                        FileLogger.i(TAG, "curStringValue: " + curStringValue + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curStringValue, expectValue, valueType);
                        break;
                    case ValueType.BYTE_VECTOR:
                        byte[] curByteVectorValue = mCarManager.getByteVectorProperty(realId, 0);
                        String curByteVectorValueStr = Arrays.toString(curByteVectorValue);
                        FileLogger.i(TAG, "curByteArrayValue: " + curByteVectorValueStr + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curByteVectorValueStr, expectValue, valueType);
                        break;
                    case ValueType.INT_VECTOR:
                        int[] curIntVectorValue = mCarManager.getIntVectorProperty(realId, 0);
                        String curIntVectorValueStr = Arrays.toString(curIntVectorValue);
                        FileLogger.i(TAG, "curIntArrayValue: " + curIntVectorValueStr + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curIntVectorValueStr, expectValue, valueType);
                        break;
                    case ValueType.FLOAT_VECTOR:
                        float[] curFloatVectorValue = mCarManager.getFloatVectorProperty(realId, 0);
                        String curFloatVectorValueStr = Arrays.toString(curFloatVectorValue);
                        FileLogger.i(TAG, "curFloatArrayValue: " + curFloatVectorValueStr + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curFloatVectorValueStr, expectValue, valueType);
                        break;
                    case ValueType.LONG_VECTOR:
                        long[] curLongVectorValue = mCarManager.getLongVectorProperty(realId, 0);
                        String curLongVectorValueStr = Arrays.toString(curLongVectorValue);
                        FileLogger.i(TAG, "curLongArrayValue: " + curLongVectorValueStr + ", expectValue: " + expectValue);
                        checkResult = ValueCompareUtils.compareValue(curLongVectorValueStr, expectValue, valueType);
                        break;
                    default:
                        FileLogger.e(TAG, "checkValue Unsupported valueType: " + valueType + ", id: " + id);
                        return false;
                }
                checkTimes++;
                SystemClock.sleep(CarApi.DEFAULT_CHECK_WAIT_TIME);
            }
            if (checkResult) {
                FileLogger.i(TAG, "Get expected value: " + expectValue + " after " + checkTimes + " times.");
                return true;
            }
            FileLogger.e(TAG, "Failed to get expected value after " + retryTimes + " times.");
            return false;
        } catch (Exception e) {
            FileLogger.e(TAG, "checkValue: Exception: " + e.getMessage());
        }
        return false;
    }
}