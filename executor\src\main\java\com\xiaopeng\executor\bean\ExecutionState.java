package com.xiaopeng.executor.bean;

/**
 * 执行状态类
 *
 * 集中管理测试执行过程中的位置信息，包括：
 * - 脚本循环索引（scriptLoopIndex）：整个脚本的第几次执行
 * - 当前阶段索引（currentPhaseIndex）：当前执行的阶段索引（1=Precondition, 2=Procedure, 3=PostCondition）
 * - 当前场景索引（currentSceneIndex）：正在执行的第几个场景
 * - 场景循环索引（sceneLoopIndex）：当前场景的第几次执行
 * - 当前步骤索引（currentStepIndex）：当前场景中的第几个步骤
 *
 * 这些信息主要用于日志记录和状态跟踪
 */
public class ExecutionState {

    private int scriptLoopIndex = 1;        // 脚本循环索引（从1开始）
    private int currentPhaseIndex = 1;       // 当前阶段索引（从1开始）：1=Precondition, 2=Procedure, 3=PostCondition
    private int currentSceneIndex = 1;       // 当前场景索引（从1开始）
    private int sceneLoopIndex = 1;          // 场景循环索引（从1开始）
    private int currentStepIndex = 1;        // 当前步骤索引（从1开始）

    /**
     * 默认构造函数
     * 初始化所有属性为默认值
     */
    public ExecutionState() {
        reset();
    }

    /**
     * 构造函数
     *
     * @param scriptLoopIndex 脚本循环索引
     * @param currentPhaseIndex 当前阶段索引
     * @param currentSceneIndex 当前场景索引
     * @param sceneLoopIndex 场景循环索引
     * @param currentStepIndex 当前步骤索引
     */
    public ExecutionState(int scriptLoopIndex, int currentPhaseIndex, int currentSceneIndex, int sceneLoopIndex, int currentStepIndex) {
        this.scriptLoopIndex = scriptLoopIndex;
        this.currentPhaseIndex = currentPhaseIndex;
        this.currentSceneIndex = currentSceneIndex;
        this.sceneLoopIndex = sceneLoopIndex;
        this.currentStepIndex = currentStepIndex;
    }

    /**
     * 兼容性构造函数
     *
     * @param scriptLoopIndex 脚本循环索引
     * @param currentSceneIndex 当前场景索引
     * @param sceneLoopIndex 场景循环索引
     */
    public ExecutionState(int scriptLoopIndex, int currentSceneIndex, int sceneLoopIndex) {
        this(scriptLoopIndex, 1, currentSceneIndex, sceneLoopIndex, 1);
    }

    /**
     * 更新执行位置
     *
     * @param scriptLoopIndex 脚本循环索引
     * @param currentPhaseIndex 当前阶段索引
     * @param currentSceneIndex 当前场景索引
     * @param sceneLoopIndex 场景循环索引
     * @param currentStepIndex 当前步骤索引
     */
    public void update(int scriptLoopIndex, int currentPhaseIndex, int currentSceneIndex, int sceneLoopIndex, int currentStepIndex) {
        this.scriptLoopIndex = scriptLoopIndex;
        this.currentPhaseIndex = currentPhaseIndex;
        this.currentSceneIndex = currentSceneIndex;
        this.sceneLoopIndex = sceneLoopIndex;
        this.currentStepIndex = currentStepIndex;
    }

    /**
     * 兼容性更新方法
     *
     * @param scriptLoopIndex 脚本循环索引
     * @param currentSceneIndex 当前场景索引
     * @param sceneLoopIndex 场景循环索引
     */
    public void update(int scriptLoopIndex, int currentSceneIndex, int sceneLoopIndex) {
        update(scriptLoopIndex, this.currentPhaseIndex, currentSceneIndex, sceneLoopIndex, this.currentStepIndex);
    }

    /**
     * 更新脚本循环索引
     *
     * @param scriptLoopIndex 脚本循环索引
     */
    public void updateScriptLoopIndex(int scriptLoopIndex) {
        this.scriptLoopIndex = scriptLoopIndex;
    }

    /**
     * 更新当前场景索引
     *
     * @param currentSceneIndex 当前场景索引
     */
    public void updateCurrentSceneIndex(int currentSceneIndex) {
        this.currentSceneIndex = currentSceneIndex;
    }

    /**
     * 更新场景循环索引
     *
     * @param sceneLoopIndex 场景循环索引
     */
    public void updateSceneLoopIndex(int sceneLoopIndex) {
        this.sceneLoopIndex = sceneLoopIndex;
    }

    /**
     * 重置到初始状态
     */
    public void reset() {
        this.scriptLoopIndex = 1;
        this.currentPhaseIndex = 1;
        this.currentSceneIndex = 1;
        this.sceneLoopIndex = 1;
        this.currentStepIndex = 1;
    }

    /**
     * 获取脚本循环索引
     *
     * @return 脚本循环索引
     */
    public int getScriptLoopIndex() {
        return scriptLoopIndex;
    }

    /**
     * 获取当前阶段索引
     *
     * @return 当前阶段索引
     */
    public int getCurrentPhaseIndex() {
        return currentPhaseIndex;
    }

    /**
     * 获取当前场景索引
     *
     * @return 当前场景索引
     */
    public int getCurrentSceneIndex() {
        return currentSceneIndex;
    }

    /**
     * 获取场景循环索引
     *
     * @return 场景循环索引
     */
    public int getSceneLoopIndex() {
        return sceneLoopIndex;
    }

    /**
     * 获取当前步骤索引
     *
     * @return 当前步骤索引
     */
    public int getCurrentStepIndex() {
        return currentStepIndex;
    }

    /**
     * 创建当前状态的副本
     *
     * @return 状态副本
     */
    public ExecutionState copy() {
        return new ExecutionState(scriptLoopIndex, currentPhaseIndex, currentSceneIndex, sceneLoopIndex, currentStepIndex);
    }

    /**
     * 获取状态摘要字符串
     *
     * @return 状态摘要
     */
    public String getSummary() {
        return String.format("scriptLoop:%d, phase:%d, scene:%d, sceneLoop:%d, step:%d",
                scriptLoopIndex, currentPhaseIndex, currentSceneIndex, sceneLoopIndex, currentStepIndex);
    }

    @Override
    public String toString() {
        return "ExecutionState{" +
                "scriptLoopIndex=" + scriptLoopIndex +
                ", currentPhaseIndex=" + currentPhaseIndex +
                ", currentSceneIndex=" + currentSceneIndex +
                ", sceneLoopIndex=" + sceneLoopIndex +
                ", currentStepIndex=" + currentStepIndex +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ExecutionState that = (ExecutionState) o;

        if (scriptLoopIndex != that.scriptLoopIndex) return false;
        if (currentPhaseIndex != that.currentPhaseIndex) return false;
        if (currentSceneIndex != that.currentSceneIndex) return false;
        if (sceneLoopIndex != that.sceneLoopIndex) return false;
        return currentStepIndex == that.currentStepIndex;
    }

    @Override
    public int hashCode() {
        int result = scriptLoopIndex;
        result = 31 * result + currentPhaseIndex;
        result = 31 * result + currentSceneIndex;
        result = 31 * result + sceneLoopIndex;
        result = 31 * result + currentStepIndex;
        return result;
    }
}
