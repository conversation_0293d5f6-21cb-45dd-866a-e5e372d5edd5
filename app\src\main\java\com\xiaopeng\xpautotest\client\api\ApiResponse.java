package com.xiaopeng.xpautotest.client.api;

public class ApiResponse<T> {
    private int code = -1;
    private T data;
    private String detail;

    public ApiResponse() {
    }

    public int getCode() { return code; }
    public T getData() { return data; }
    public String getMessage() { return detail; }

    public boolean isSuccess() {
        return code == 0; // 假设10000表示成功
    }

    public String toString() {
        return "ApiResponse{code=" + code + ", data=" + data + ", detail='" + detail + "'}";
    }
}
